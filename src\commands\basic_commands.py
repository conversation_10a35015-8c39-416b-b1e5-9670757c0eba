"""
Module contenant les commandes de base pour la manipulation de grilles ARC
"""

import numpy as np
from typing import Dict, Any
from .unified_command import UnifiedCommand
from .coordinate_utils import CoordinateUtils


class BasicCommands:
    """Classe contenant les commandes de base pour les grilles ARC"""
    
    def __init__(self, executor):
        """
        Initialise les commandes de base
        
        Args:
            executor: Instance de CommandExecutor pour accéder à la grille et aux propriétés
        """
        self.executor = executor

    def cmd_init(self, cmd: UnifiedCommand) -> bool:
        """Initialise une grille de taille width x height"""
        # Gérer les formats: INIT 3 3 ou INIT 3x3
        if len(cmd.parameters) == 1:
            # Format INIT 3x3
            param = cmd.parameters[0]
            if 'x' in param:
                try:
                    width_str, height_str = param.split('x')
                    width = int(width_str)
                    height = int(height_str)
                except (ValueError, TypeError):
                    self.executor.error = f"Format INIT invalide: {param}. Utilisez INIT 3x3 ou INIT 3 3"
                    return False
            else:
                self.executor.error = f"Format INIT invalide: {param}. Utilisez INIT 3x3 ou INIT 3 3"
                return False
        elif len(cmd.parameters) == 2:
            # Format INIT 3 3
            try:
                width = int(cmd.parameters[0])
                height = int(cmd.parameters[1])
            except (ValueError, TypeError):
                self.executor.error = f"Paramètres INIT invalides: {cmd.parameters}"
                return False
        else:
            self.executor.error = f"INIT attend 1 ou 2 paramètres, reçu {len(cmd.parameters)}: {cmd.parameters}"
            return False

        try:
            if width <= 0 or height <= 0:
                return False

            # Si une grille d'entrée existe déjà avec les bonnes dimensions, la préserver
            if (hasattr(self.executor, 'grid') and self.executor.grid is not None and 
                hasattr(self.executor, 'width') and hasattr(self.executor, 'height') and
                self.executor.width == width and self.executor.height == height and
                self.executor.grid.shape == (height, width)):
                # Grille déjà initialisée avec les bonnes dimensions, ne pas l'écraser
                return True

            self.executor.width = width
            self.executor.height = height
            # CORRECTION: INIT 19x24 signifie 19 colonnes x 24 lignes
            # numpy utilise (rows, cols) donc pour width=19, height=24 -> (height, width)
            self.executor.grid = np.zeros((height, width), dtype=int)
            return True
        except (ValueError, TypeError):
            return False

    def cmd_edit(self, cmd: UnifiedCommand) -> bool:
        """Modifie une ou plusieurs cellules avec une valeur"""
        if len(cmd.parameters) != 1:
            return False

        if self.executor.grid is None:
            self.executor.error = "La grille n'a pas été initialisée."
            return False

        try:
            value = int(cmd.parameters[0])
            
            # Utiliser la fonction générique pour parser les blocs de coordonnées
            if hasattr(cmd, 'raw_command') and cmd.raw_command:
                coordinate_blocks = CoordinateUtils.parse_coordinate_blocks(cmd.raw_command)
            else:
                # Fallback: ancienne méthode par paires de coordonnées
                coords = cmd.coordinates
                coordinate_blocks = []
                
                if len(coords) == 2:
                    # Format simple: EDIT 1 [x,y]
                    coordinate_blocks.append([f"{coords[0]},{coords[1]}"])
                elif len(coords) % 2 == 0:
                    # Format multiple: EDIT 1 [x1,y1] [x2,y2] [x3,y3] ...
                    for i in range(0, len(coords), 2):
                        coordinate_blocks.append([f"{coords[i]},{coords[i+1]}"])
                else:
                    self.executor.error = f"Format de coordonnées non valide pour EDIT: {cmd.coordinates}"
                    return False
            
            # Utiliser la fonction générique pour traiter les blocs
            def edit_action(x1, y1, x2, y2):
                # Pour EDIT, traiter chaque cellule individuellement
                for x in range(x1, x2 + 1):
                    for y in range(y1, y2 + 1):
                        self.executor.grid[x, y] = value
            
            CoordinateUtils.process_coordinate_blocks(self.executor.grid, coordinate_blocks, edit_action)
            return True
                
        except (ValueError, TypeError, IndexError) as e:
            self.executor.error = f"Erreur dans cmd_edit: {e}"
            return False

    def cmd_fill(self, cmd: UnifiedCommand) -> bool:
        """Remplit les zones sélectionnées avec une couleur"""
        if len(cmd.parameters) != 1:
            return False

        if self.executor.grid is None:
            return False

        try:
            color = int(cmd.parameters[0])

            # Utiliser la fonction générique pour parser les blocs de coordonnées
            if hasattr(cmd, 'raw_command') and cmd.raw_command:
                coordinate_blocks = CoordinateUtils.parse_coordinate_blocks(cmd.raw_command)
            else:
                # Fallback: traiter les coordonnées parsées
                coords = cmd.coordinates
                coordinate_blocks = []

                # Traiter les coordonnées par paires
                i = 0
                while i + 1 < len(coords):
                    x1, y1 = coords[i][0], coords[i][1]
                    x2, y2 = coords[i+1][0], coords[i+1][1]

                    if x1 == x2 and y1 == y2:
                        # Cellule simple
                        coordinate_blocks.append([f"{x1},{y1}"])
                    else:
                        # Rectangle
                        coordinate_blocks.append([f"{x1},{y1}", f"{x2},{y2}"])
                    i += 2

            # Utiliser la fonction générique pour traiter les blocs
            def fill_action(x1, y1, x2, y2):
                self.executor.grid[x1:x2+1, y1:y2+1] = color

            CoordinateUtils.process_coordinate_blocks(self.executor.grid, coordinate_blocks, fill_action)
            return True

        except (ValueError, TypeError, IndexError) as e:
            self.executor.error = f"Erreur dans cmd_fill: {e}"
            return False

    def cmd_clear(self, cmd: UnifiedCommand) -> bool:
        """Efface les zones sélectionnées (remplit avec 0)"""
        if self.executor.grid is None:
            return False

        try:
            # Utiliser la fonction générique pour parser les blocs de coordonnées
            if hasattr(cmd, 'raw_command') and cmd.raw_command:
                coordinate_blocks = CoordinateUtils.parse_coordinate_blocks(cmd.raw_command)
            else:
                # Fallback: ancienne méthode par paires de 4 coordonnées
                coords = cmd.coordinates
                coordinate_blocks = []
                i = 0
                while i + 3 < len(coords):
                    x1, y1, x2, y2 = coords[i], coords[i+1], coords[i+2], coords[i+3]
                    if x1 == x2 and y1 == y2:
                        # Cellule simple
                        coordinate_blocks.append([f"{x1},{y1}"])
                    else:
                        # Rectangle
                        coordinate_blocks.append([f"{x1},{y1}", f"{x2},{y2}"])
                    i += 4

            # Utiliser la fonction générique pour traiter les blocs
            def clear_action(x1, y1, x2, y2):
                self.executor.grid[x1:x2+1, y1:y2+1] = 0

            CoordinateUtils.process_coordinate_blocks(self.executor.grid, coordinate_blocks, clear_action)
            return True

        except (ValueError, TypeError, IndexError) as e:
            self.executor.error = f"Erreur dans cmd_clear: {e}"
            return False

    def cmd_surround(self, cmd: UnifiedCommand) -> bool:
        """Entoure les zones sélectionnées avec une couleur"""
        if len(cmd.parameters) != 1:
            return False

        if self.executor.grid is None:
            return False

        try:
            color = int(cmd.parameters[0])

            # Utiliser la fonction générique pour parser les blocs de coordonnées
            if hasattr(cmd, 'raw_command') and cmd.raw_command:
                coordinate_blocks = CoordinateUtils.parse_coordinate_blocks(cmd.raw_command)
            else:
                # Fallback: ancienne méthode par paires de 4 coordonnées
                coords = cmd.coordinates
                coordinate_blocks = []
                if cmd.coordinates:
                    # Vérification du format des coordonnées existant
                    if isinstance(cmd.coordinates[0], list):
                        # Format [[x1,y1,x2,y2]] ou [[x,y]]
                        for selection in cmd.coordinates:
                            if len(selection) == 2:
                                x, y = selection
                                coordinate_blocks.append([f"{x},{y}"])
                            elif len(selection) == 4:
                                x1, y1, x2, y2 = selection
                                if x1 == x2 and y1 == y2:
                                    coordinate_blocks.append([f"{x1},{y1}"])
                                else:
                                    coordinate_blocks.append([f"{x1},{y1}", f"{x2},{y2}"])
                    else:
                        # Format plat [x1,y1,x2,y2,...]
                        i = 0
                        while i + 3 < len(coords):
                            x1, y1, x2, y2 = coords[i], coords[i+1], coords[i+2], coords[i+3]
                            if x1 == x2 and y1 == y2:
                                coordinate_blocks.append([f"{x1},{y1}"])
                            else:
                                coordinate_blocks.append([f"{x1},{y1}", f"{x2},{y2}"])
                            i += 4

            # Utiliser la fonction générique pour traiter les blocs
            def surround_action(x1, y1, x2, y2):
                x_min, x_max = min(x1, x2), max(x1, x2)
                y_min, y_max = min(y1, y2), max(y1, y2)

                # Coordonnées de la boîte englobante pour l'entourage
                surround_x_min = max(0, x_min - 1)
                surround_x_max = min(self.executor.height - 1, x_max + 1)
                surround_y_min = max(0, y_min - 1)
                surround_y_max = min(self.executor.width - 1, y_max + 1)

                # Itérer sur la boîte d'entourage et colorier si c'est une bordure
                for r in range(surround_x_min, surround_x_max + 1):
                    for c in range(surround_y_min, surround_y_max + 1):
                        # Ne pas colorier l'intérieur de la sélection originale
                        if not (x_min <= r <= x_max and y_min <= c <= y_max):
                            self.executor.grid[r, c] = color

            CoordinateUtils.process_coordinate_blocks(self.executor.grid, coordinate_blocks, surround_action)
            return True

        except (ValueError, TypeError, IndexError) as e:
            self.executor.error = f"Erreur dans cmd_surround: {e}"
            return False

    def cmd_replace(self, cmd: UnifiedCommand) -> bool:
        """Remplace des couleurs par une autre dans les zones sélectionnées"""
        if len(cmd.parameters) < 2:
            return False

        if self.executor.grid is None:
            return False

        try:
            # Gestion des couleurs multiples pour REPLACE
            if len(cmd.parameters) >= 2:
                # Les paramètres peuvent être dans deux formats :
                # Format simple: [7, 5, 0] -> source_colors=[7, 5], target_color=0
                # Format groupé: [[7, 5], 0] -> source_colors=[7, 5], target_color=0

                if isinstance(cmd.parameters[0], list):
                    # Format groupé: [[7, 5], 0]
                    source_colors = cmd.parameters[0]
                    target_color = int(cmd.parameters[1])
                else:
                    # Format simple: [7, 5, 0]
                    source_colors = [int(c) for c in cmd.parameters[:-1]]
                    target_color = int(cmd.parameters[-1])
            else:
                return False

            # Utiliser la fonction générique pour parser les blocs de coordonnées
            if hasattr(cmd, 'raw_command') and cmd.raw_command:
                coordinate_blocks = CoordinateUtils.parse_coordinate_blocks(cmd.raw_command)
            else:
                # Fallback: traiter les coordonnées parsées
                coords = cmd.coordinates
                coordinate_blocks = []

                # Traiter les coordonnées par paires
                i = 0
                while i + 1 < len(coords):
                    x1, y1 = coords[i][0], coords[i][1]
                    x2, y2 = coords[i+1][0], coords[i+1][1]

                    if x1 == x2 and y1 == y2:
                        # Cellule simple
                        coordinate_blocks.append([f"{x1},{y1}"])
                    else:
                        # Rectangle
                        coordinate_blocks.append([f"{x1},{y1}", f"{x2},{y2}"])
                    i += 2

            # REPLACE doit TOUJOURS avoir des coordonnées selon la grammaire officielle
            if not coordinate_blocks:
                self.executor.error = "REPLACE nécessite des coordonnées. Format: REPLACE couleur_source couleur_cible [coordonnées]"
                return False

            # Utiliser la fonction générique pour traiter les blocs
            def replace_action(x1, y1, x2, y2):
                # Accès direct à la grille pour éviter les problèmes de vues NumPy
                region = self.executor.grid[x1:x2+1, y1:y2+1]
                mask = np.isin(region, source_colors)
                # Application directe sur self.executor.grid
                self.executor.grid[x1:x2+1, y1:y2+1][mask] = target_color

            CoordinateUtils.process_coordinate_blocks(self.executor.grid, coordinate_blocks, replace_action)
            return True

        except (ValueError, TypeError, IndexError) as e:
            self.executor.error = f"Erreur dans cmd_replace: {e}"
            return False
