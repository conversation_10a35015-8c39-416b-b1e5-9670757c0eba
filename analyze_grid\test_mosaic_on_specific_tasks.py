#!/usr/bin/env python3
"""
Test du MosaicSolver sur les tâches ARC spécifiées
Analyse les tâches : 0dfd9992, 29ec7d0e, 3631a71a, 484b58aa, 9ecd008a, b8825c91, c3f564a4, dc0a314f
"""

import json
import os
import sys
import numpy as np
from pathlib import Path

# Ajouter le dossier parent au path pour importer MosaicSolver
sys.path.append(str(Path(__file__).parent))

from MosaicSolver import MosaicSolver

def load_arc_task(task_id: str) -> dict:
    """Charge une tâche ARC depuis le fichier JSON"""
    task_path = Path("../arcdata/training") / f"{task_id}.json"
    
    if not task_path.exists():
        raise FileNotFoundError(f"Tâche {task_id} non trouvée dans {task_path}")
    
    with open(task_path, 'r') as f:
        return json.load(f)

def analyze_grid_with_mosaic_solver(grid, grid_type, task_id, example_idx=None):
    """Analyse une grille avec le MosaicSolver"""
    solver = MosaicSolver(empty_value=0, min_block_size=2)  # Ajuster min_block_size pour ARC
    
    # Analyser la grille
    results = solver.analyze_mosaic(grid)
    
    # Ajouter des métadonnées
    results['metadata'] = {
        'task_id': task_id,
        'grid_type': grid_type,
        'example_idx': example_idx,
        'grid_shape': np.array(grid).shape,
        'unique_values': sorted(list(set(np.array(grid).flatten()))),
        'empty_cells': np.sum(np.array(grid) == 0)
    }
    
    return results

def test_mosaic_solver_on_specific_tasks():
    """Test le MosaicSolver sur les tâches spécifiées"""
    
    # Tâches à analyser
    task_ids = [
        "0dfd9992", "29ec7d0e", "3631a71a", "484b58aa", 
        "9ecd008a", "b8825c91", "c3f564a4", "dc0a314f"
    ]
    
    all_results = {}
    
    print("=" * 80)
    print("TEST DU MOSAICSOLVER SUR LES TÂCHES ARC SPÉCIFIÉES")
    print("=" * 80)
    
    for task_id in task_ids:
        print(f"\n🔍 Analyse de la tâche {task_id} avec MosaicSolver...")
        
        try:
            # Charger la tâche
            task_data = load_arc_task(task_id)
            
            task_results = {
                'task_id': task_id,
                'train_inputs': [],
                'train_outputs': [],
                'test_inputs': [],
                'summary': {
                    'mosaic_detected_inputs': 0,
                    'mosaic_detected_outputs': 0,
                    'avg_confidence_inputs': 0.0,
                    'avg_confidence_outputs': 0.0,
                    'best_mosaic_example': None
                }
            }
            
            # Analyser les grilles d'entraînement (inputs)
            print(f"   📥 Analyse des inputs d'entraînement...")
            input_confidences = []
            for i, example in enumerate(task_data['train']):
                input_grid = example['input']
                result = analyze_grid_with_mosaic_solver(input_grid, 'train_input', task_id, i)
                task_results['train_inputs'].append(result)
                
                if result['is_mosaic']:
                    task_results['summary']['mosaic_detected_inputs'] += 1
                    print(f"      ✅ Input {i+1}: Mosaïque détectée (confiance: {result['confidence']:.2f})")
                else:
                    print(f"      ❌ Input {i+1}: Pas de mosaïque (confiance: {result['confidence']:.2f})")
                
                input_confidences.append(result['confidence'])
            
            # Analyser les grilles d'entraînement (outputs)
            print(f"   📤 Analyse des outputs d'entraînement...")
            output_confidences = []
            for i, example in enumerate(task_data['train']):
                output_grid = example['output']
                result = analyze_grid_with_mosaic_solver(output_grid, 'train_output', task_id, i)
                task_results['train_outputs'].append(result)
                
                if result['is_mosaic']:
                    task_results['summary']['mosaic_detected_outputs'] += 1
                    print(f"      ✅ Output {i+1}: Mosaïque détectée (confiance: {result['confidence']:.2f})")
                else:
                    print(f"      ❌ Output {i+1}: Pas de mosaïque (confiance: {result['confidence']:.2f})")
                
                output_confidences.append(result['confidence'])
            
            # Analyser les grilles de test (inputs)
            print(f"   🧪 Analyse des inputs de test...")
            for i, example in enumerate(task_data['test']):
                input_grid = example['input']
                result = analyze_grid_with_mosaic_solver(input_grid, 'test_input', task_id, i)
                task_results['test_inputs'].append(result)
                
                if result['is_mosaic']:
                    print(f"      ✅ Test input {i+1}: Mosaïque détectée (confiance: {result['confidence']:.2f})")
                else:
                    print(f"      ❌ Test input {i+1}: Pas de mosaïque (confiance: {result['confidence']:.2f})")
            
            # Calculer les statistiques
            if input_confidences:
                task_results['summary']['avg_confidence_inputs'] = sum(input_confidences) / len(input_confidences)
            if output_confidences:
                task_results['summary']['avg_confidence_outputs'] = sum(output_confidences) / len(output_confidences)
            
            # Trouver le meilleur exemple de mosaïque
            all_examples = task_results['train_inputs'] + task_results['train_outputs'] + task_results['test_inputs']
            mosaic_examples = [ex for ex in all_examples if ex['is_mosaic']]
            if mosaic_examples:
                best_example = max(mosaic_examples, key=lambda x: x['confidence'])
                task_results['summary']['best_mosaic_example'] = {
                    'grid_type': best_example['metadata']['grid_type'],
                    'example_idx': best_example['metadata']['example_idx'],
                    'confidence': best_example['confidence'],
                    'num_transformations': len(best_example['proposed_transformations'])
                }
            
            all_results[task_id] = task_results
            
            # Affichage du résumé
            print(f"   📊 Résumé tâche {task_id}:")
            print(f"      - Mosaïques détectées (inputs): {task_results['summary']['mosaic_detected_inputs']}/{len(task_data['train'])}")
            print(f"      - Mosaïques détectées (outputs): {task_results['summary']['mosaic_detected_outputs']}/{len(task_data['train'])}")
            print(f"      - Confiance moyenne inputs: {task_results['summary']['avg_confidence_inputs']:.2f}")
            print(f"      - Confiance moyenne outputs: {task_results['summary']['avg_confidence_outputs']:.2f}")
            
            if task_results['summary']['best_mosaic_example']:
                best = task_results['summary']['best_mosaic_example']
                print(f"      - Meilleur exemple: {best['grid_type']} {best['example_idx']} (conf: {best['confidence']:.2f})")
            
        except Exception as e:
            print(f"❌ Erreur lors de l'analyse de {task_id}: {e}")
            all_results[task_id] = {'error': str(e)}
    
    # Sauvegarde des résultats
    output_file = "mosaic_solver_specific_tasks_results.json"
    
    def convert_numpy_types(obj):
        """Convertit les types numpy en types Python natifs pour JSON"""
        if isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, dict):
            return {str(k): convert_numpy_types(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [convert_numpy_types(item) for item in obj]
        elif isinstance(obj, tuple):
            return tuple(convert_numpy_types(item) for item in obj)
        else:
            return obj
    
    converted_results = convert_numpy_types(all_results)
    
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(converted_results, f, indent=2, ensure_ascii=False, default=str)
    
    print(f"\n📊 Résultats détaillés sauvegardés dans {output_file}")
    
    # Génération du rapport de synthèse
    generate_mosaic_synthesis_report(all_results)
    
    return all_results

def generate_mosaic_synthesis_report(all_results: dict):
    """Génère un rapport de synthèse des analyses MosaicSolver"""
    
    report_lines = []
    report_lines.append("=" * 80)
    report_lines.append("RAPPORT DE SYNTHÈSE - MOSAICSOLVER SUR TÂCHES ARC SPÉCIFIÉES")
    report_lines.append("=" * 80)
    report_lines.append("")
    
    successful_analyses = {k: v for k, v in all_results.items() if 'error' not in v}
    
    report_lines.append(f"Tâches analysées avec succès: {len(successful_analyses)}/{len(all_results)}")
    report_lines.append("")
    
    # Statistiques globales
    total_mosaic_inputs = sum(r['summary']['mosaic_detected_inputs'] for r in successful_analyses.values())
    total_mosaic_outputs = sum(r['summary']['mosaic_detected_outputs'] for r in successful_analyses.values())
    total_inputs = sum(len(r['train_inputs']) for r in successful_analyses.values())
    total_outputs = sum(len(r['train_outputs']) for r in successful_analyses.values())
    
    report_lines.append("STATISTIQUES GLOBALES MOSAICSOLVER:")
    report_lines.append(f"- Mosaïques détectées (inputs): {total_mosaic_inputs}/{total_inputs} ({total_mosaic_inputs/total_inputs*100:.1f}%)")
    report_lines.append(f"- Mosaïques détectées (outputs): {total_mosaic_outputs}/{total_outputs} ({total_mosaic_outputs/total_outputs*100:.1f}%)")
    report_lines.append("")
    
    # Analyse par tâche
    report_lines.append("ANALYSE DÉTAILLÉE PAR TÂCHE:")
    report_lines.append("-" * 50)
    
    for task_id, results in successful_analyses.items():
        report_lines.append(f"\n🔍 TÂCHE {task_id}:")
        
        summary = results['summary']
        num_train = len(results['train_inputs'])
        
        report_lines.append(f"   Mosaïques inputs: {summary['mosaic_detected_inputs']}/{num_train}")
        report_lines.append(f"   Mosaïques outputs: {summary['mosaic_detected_outputs']}/{num_train}")
        report_lines.append(f"   Confiance moyenne inputs: {summary['avg_confidence_inputs']:.2f}")
        report_lines.append(f"   Confiance moyenne outputs: {summary['avg_confidence_outputs']:.2f}")
        
        if summary['best_mosaic_example']:
            best = summary['best_mosaic_example']
            report_lines.append(f"   Meilleur exemple: {best['grid_type']} (conf: {best['confidence']:.2f}, {best['num_transformations']} transformations)")
        else:
            report_lines.append("   Aucune mosaïque détectée")
        
        # Détails des transformations les plus intéressantes
        all_examples = results['train_inputs'] + results['train_outputs'] + results['test_inputs']
        interesting_examples = [ex for ex in all_examples if ex['is_mosaic'] and ex['confidence'] > 0.7]
        
        if interesting_examples:
            report_lines.append(f"   Exemples intéressants ({len(interesting_examples)}):")
            for ex in interesting_examples[:2]:  # Max 2 exemples
                meta = ex['metadata']
                report_lines.append(f"      - {meta['grid_type']} {meta['example_idx']}: {len(ex['proposed_transformations'])} transformations")
                for i, trans in enumerate(ex['proposed_transformations'][:2]):  # Max 2 transformations
                    report_lines.append(f"        {i+1}. {trans['operation']} (score: {trans['score']:.2f})")
    
    # Tâches les plus prometteuses pour les mosaïques
    report_lines.append("\n" + "=" * 50)
    report_lines.append("TÂCHES LES PLUS PROMETTEUSES POUR MOSAÏQUES:")
    
    # Trier par nombre total de mosaïques détectées
    task_scores = []
    for task_id, results in successful_analyses.items():
        if 'error' not in results:
            total_mosaics = results['summary']['mosaic_detected_inputs'] + results['summary']['mosaic_detected_outputs']
            avg_confidence = (results['summary']['avg_confidence_inputs'] + results['summary']['avg_confidence_outputs']) / 2
            score = total_mosaics + avg_confidence
            task_scores.append((task_id, total_mosaics, avg_confidence, score))
    
    task_scores.sort(key=lambda x: x[3], reverse=True)
    
    for i, (task_id, total_mosaics, avg_conf, score) in enumerate(task_scores[:5]):
        report_lines.append(f"{i+1}. {task_id}: {total_mosaics} mosaïques, confiance {avg_conf:.2f}")
    
    # Sauvegarde du rapport
    report_content = "\n".join(report_lines)
    
    with open("mosaic_solver_synthesis_report.txt", 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    print("📋 Rapport de synthèse MosaicSolver sauvegardé dans mosaic_solver_synthesis_report.txt")
    print("\n" + "=" * 50)
    print("APERÇU DU RAPPORT MOSAICSOLVER:")
    print("=" * 50)
    print(report_content[:1500] + "..." if len(report_content) > 1500 else report_content)

def display_detailed_mosaic_example(task_id, example_type, example_idx):
    """Affiche un exemple détaillé d'analyse de mosaïque"""
    try:
        with open("mosaic_solver_specific_tasks_results.json", 'r', encoding='utf-8') as f:
            results = json.load(f)
        
        if task_id not in results:
            print(f"❌ Tâche {task_id} non trouvée")
            return
        
        task_results = results[task_id]
        examples = task_results.get(example_type, [])
        
        if example_idx >= len(examples):
            print(f"❌ Exemple {example_idx} non trouvé dans {example_type}")
            return
        
        example = examples[example_idx]
        
        print(f"=" * 60)
        print(f"ANALYSE DÉTAILLÉE - {task_id} {example_type} {example_idx}")
        print(f"=" * 60)
        
        print(f"🔍 Métadonnées:")
        meta = example['metadata']
        print(f"   - Forme de grille: {meta['grid_shape']}")
        print(f"   - Valeurs uniques: {meta['unique_values']}")
        print(f"   - Cellules vides: {meta['empty_cells']}")
        
        print(f"\n📊 Résultats MosaicSolver:")
        print(f"   - Est une mosaïque: {'✅ Oui' if example['is_mosaic'] else '❌ Non'}")
        print(f"   - Confiance: {example['confidence']:.2f}")
        print(f"   - Blocs sources: {len(example['source_blocks'])}")
        print(f"   - Régions cibles: {len(example['target_regions'])}")
        print(f"   - Transformations proposées: {len(example['proposed_transformations'])}")
        
        if example['proposed_transformations']:
            print(f"\n🔧 Transformations détaillées:")
            for i, trans in enumerate(example['proposed_transformations']):
                print(f"   {i+1}. {trans['operation']}")
                print(f"      Score: {trans['score']:.2f}")
                print(f"      Source: {trans['source']['coords']} (taille: {trans['source']['size']})")
                print(f"      Cible: {trans['target']['coords']} (taille: {trans['target']['size']})")
        
        if example['analysis']:
            print(f"\n📈 Analyse:")
            analysis = example['analysis']
            print(f"   - Couverture: {analysis.get('coverage_ratio', 0):.1%}")
            print(f"   - Qualité moyenne: {analysis.get('avg_transformation_quality', 0):.1%}")
        
    except Exception as e:
        print(f"❌ Erreur lors de l'affichage: {e}")

if __name__ == "__main__":
    # Changer vers le dossier AnalysesGrilles
    os.chdir(Path(__file__).parent)
    
    print("Démarrage de l'analyse MosaicSolver sur les tâches ARC spécifiées...")
    results = test_mosaic_solver_on_specific_tasks()
    print("\n✅ Analyse MosaicSolver terminée!")
    
    # Exemple d'affichage détaillé (optionnel)
    # print("\n" + "="*60)
    # print("EXEMPLE DÉTAILLÉ:")
    # display_detailed_mosaic_example("0dfd9992", "train_inputs", 0)