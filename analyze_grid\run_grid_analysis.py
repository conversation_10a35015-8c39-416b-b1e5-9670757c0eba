"""
Script de lancement pour l'analyse de grilles ARC
Analyse des transformations avec algorithmes classiques
"""

import sys
import os
from pathlib import Path
import argparse
import traceback

def setup_environment():
    """Configure l'environnement pour l'analyse"""
    root_dir = Path(__file__).parent.parent
    if str(root_dir) not in sys.path:
        sys.path.insert(0, str(root_dir))
    
    print(f"Environnement configuré - Répertoire: {root_dir}")

def check_dependencies():
    """Vérifie les dépendances requises"""
    required_modules = ['numpy', 'pandas', 'matplotlib', 'seaborn']
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)
    
    if missing_modules:
        print(f"Modules manquants: {', '.join(missing_modules)}")
        print(f"Installation: pip install {' '.join(missing_modules)}")
        return False
    
    print("Dépendances vérifiées")
    return True

def check_data_directory(data_dir):
    """Vérifie le répertoire de données"""
    data_path = Path(data_dir)
    
    if not data_path.exists():
        print(f"Répertoire non trouvé: {data_dir}")
        return False
    
    json_files = list(data_path.glob("*.json"))
    if not json_files:
        print(f"Aucun fichier JSON dans: {data_dir}")
        return False
    
    print(f"Répertoire valide: {len(json_files)} fichiers JSON")
    return True

def run_single_task_analysis(data_dir, task_id, generate_visualizations=True, verbose=True):
    """Lance l'analyse d'une tâche spécifique"""
    
    try:
        from grid_analysis_pure import PureGridAnalyzer
        
        print(f"Initialisation de l'analyseur pour la tâche: {task_id}")
        analyzer = PureGridAnalyzer(data_dir)
        
        # Vérifier que le fichier existe
        task_file = Path(data_dir) / f"{task_id}.json"
        if not task_file.exists():
            print(f"Fichier non trouvé: {task_file}")
            return False
        
        print(f"Analyse de la tâche {task_id}...")
        result = analyzer.analyze_puzzle(task_file)
        
        if not result:
            print("Échec de l'analyse de la tâche")
            return False
        
        # Ajouter le mapping AGI
        result['agi_mapping'] = analyzer.map_to_agi_commands(result['puzzle_summary'])
        
        print(f"Analyse terminée pour la tâche: {task_id}")
        
        # Affichage détaillé des résultats
        print(f"\nRésultats détaillés pour {task_id}:")
        print(f"  Exemples d'entraînement: {result['n_train_examples']}")
        
        summary = result['puzzle_summary']
        print(f"  Type de transformation: {summary['dominant_transformation_type']}")
        print(f"  Ratio de taille moyen: {summary['average_size_ratio']:.2f}")
        print(f"  Changement de complexité: {summary['average_complexity_change']:.2f}")
        print(f"  Score de consistance: {summary['consistency_score']:.2f}")
        
        print(f"\nPatterns détectés:")
        for pattern in summary['common_patterns']:
            print(f"  - {pattern}")
        
        print(f"\nInsights calculés:")
        for insight, value in summary['computed_insights'].items():
            status = "✓" if value else "✗"
            print(f"  {status} {insight}")
        
        print(f"\nCommandes AGI suggérées:")
        agi = result['agi_mapping']
        for command in agi['possible_commands']:
            params = agi['command_parameters'].get(command, "")
            print(f"  - {command} {params}".strip())
        print(f"  Confiance: {agi['confidence_level']}")
        
        # Sauvegarder les résultats de la tâche spécifique dans le répertoire des données
        output_file = Path(data_dir) / f"{task_id}_analysis.json"
        import json
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2, ensure_ascii=False)
        
        print(f"\nFichier généré: {output_file}")
        
        return True
        
    except Exception as e:
        print(f"Erreur lors de l'analyse: {e}")
        if verbose:
            print("Détails de l'erreur:")
            traceback.print_exc()
        return False

def run_analysis(data_dir="../arcdata/training", max_puzzles=None, 
                generate_visualizations=True, verbose=True):
    """Lance l'analyse des grilles"""
    
    try:
        from grid_analysis_pure import PureGridAnalyzer
        
        print("Initialisation de l'analyseur...")
        analyzer = PureGridAnalyzer(data_dir)
        
        print("Début de l'analyse...")
        if max_puzzles:
            print(f"Limitation: {max_puzzles} puzzles")
            results = analyzer.analyze_limited_puzzles(max_puzzles)
        else:
            results = analyzer.analyze_all_puzzles()
        
        if not results:
            print("Aucun résultat obtenu")
            return False
        
        print(f"Analyse terminée: {len(results)} puzzles traités")
        
        # Génération du rapport
        print("Génération du rapport...")
        report_file = analyzer.generate_comprehensive_report()
        
        # Export CSV
        print("Export des données...")
        csv_file = analyzer.generate_csv_export()
        
        # Visualisations optionnelles
        if generate_visualizations:
            try:
                print("Génération des graphiques...")
                analyzer.generate_visualization_dashboard()
                viz_generated = True
            except Exception as e:
                print(f"Erreur visualisations: {e}")
                viz_generated = False
        else:
            viz_generated = False
        
        # Résumé des résultats
        print(f"\nRésultats:")
        print(f"  Puzzles analysés: {len(results)}")
        print(f"  Types de transformation: {len(set(analyzer.statistics['transformation_types']))}")
        print(f"  Commandes AGI suggérées: {len(set(analyzer.statistics['agi_commands']))}")
        print(f"  Insights calculés: {len(set(analyzer.statistics['computed_insights']))}")
        
        print(f"\nFichiers générés:")
        print(f"  {report_file}")
        print(f"  {csv_file}")
        if viz_generated:
            print(f"  grid_analysis_dashboard.png")
        
        return True
        
    except Exception as e:
        print(f"Erreur lors de l'analyse: {e}")
        if verbose:
            print("Détails de l'erreur:")
            traceback.print_exc()
        return False

def main():
    """Fonction principale"""
    
    parser = argparse.ArgumentParser(
        description="Analyse des transformations de grilles ARC",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    
    parser.add_argument(
        '--data-dir', 
        type=str, 
        default='../arcdata/training',
        help='Répertoire des fichiers JSON'
    )
    
    parser.add_argument(
        '--max-puzzles', 
        type=int, 
        default=None,
        help='Nombre maximum de puzzles à analyser'
    )
    
    parser.add_argument(
        '--taskId', 
        type=str, 
        default=None,
        help='Analyser une tâche spécifique (ex: 007bbfb7)'
    )
    
    parser.add_argument(
        '--no-viz', 
        action='store_true',
        help='Pas de visualisations'
    )
    
    parser.add_argument(
        '--quiet', 
        action='store_true',
        help='Mode silencieux'
    )
    
    args = parser.parse_args()
    
    print("ANALYSE DE GRILLES ARC")
    print("=" * 30)
    
    # Configuration
    setup_environment()
    
    if not check_dependencies():
        sys.exit(1)
    
    if not check_data_directory(args.data_dir):
        sys.exit(1)
    
    # Analyse
    if args.taskId:
        # Analyse d'une tâche spécifique
        success = run_single_task_analysis(
            data_dir=args.data_dir,
            task_id=args.taskId,
            generate_visualizations=not args.no_viz,
            verbose=not args.quiet
        )
    else:
        # Analyse multiple
        success = run_analysis(
            data_dir=args.data_dir,
            max_puzzles=args.max_puzzles,
            generate_visualizations=not args.no_viz,
            verbose=not args.quiet
        )
    
    if success:
        print("\nAnalyse terminée avec succès")
    else:
        print("\nÉchec de l'analyse")
        sys.exit(1)

if __name__ == "__main__":
    main()