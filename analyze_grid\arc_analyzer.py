import numpy as np
from collections import Counter, defaultdict
from typing import Dict, List, Tuple, Set, Any, Optional
import json
from skimage.measure import label, regionprops

class ARCAnalyzer:
    """
    Analyseur complet pour les puzzles ARC (Abstraction and Reasoning Corpus)
    Extrait des features et patterns des grilles pour faciliter la résolution
    """
    
    def __init__(self):
        self.colors = list(range(10))  # ARC utilise les couleurs 0-9
        
    def analyze_puzzle(self, puzzle: Dict) -> Dict[str, Any]:
        """
        Analyse complète d'un puzzle ARC
        
        Args:
            puzzle: Dict avec 'train' et 'test' contenant les exemples
            
        Returns:
            Dict avec toutes les analyses du puzzle
        """
        analysis = {
            'grid_info': self.analyze_grid_structure(puzzle),
            'objects': self.analyze_objects(puzzle),
            'patterns': self.detect_patterns(puzzle),
            'transformations': self.infer_transformations(puzzle),
            'symmetries': self.find_symmetries(puzzle),
            'complexity': self.measure_complexity(puzzle),
            'colors': self.analyze_colors(puzzle),
            'spatial_relations': self.analyze_spatial_relations(puzzle),
            'diff_analysis': self.analyze_differences(puzzle),
            'enhanced_objects': self.analyze_objects_with_regionprops(puzzle)
        }
        return analysis

    def compute_diff(self, input_grid: np.ndarray, output_grid: np.ndarray) -> Dict[str, Any]:
        """
        Calcule la différence entre les grilles d'entrée et de sortie

        Args:
            input_grid: Grille d'entrée
            output_grid: Grille de sortie

        Returns:
            Dict contenant les informations de différence
        """
        if input_grid.shape != output_grid.shape:
            return {
                'compatible': False,
                'reason': 'different_dimensions',
                'input_shape': input_grid.shape,
                'output_shape': output_grid.shape
            }

        # Calculer la matrice de différence
        diff_matrix = (input_grid != output_grid).astype(int)

        # Analyser les changements de couleur
        color_changes = {}
        for i in range(input_grid.shape[0]):
            for j in range(input_grid.shape[1]):
                if diff_matrix[i, j] == 1:
                    change_key = f"{input_grid[i, j]}→{output_grid[i, j]}"
                    color_changes[change_key] = color_changes.get(change_key, 0) + 1

        return {
            'compatible': True,
            'diff_matrix': diff_matrix.tolist(),
            'total_changes': int(np.sum(diff_matrix)),
            'change_percentage': float(np.sum(diff_matrix) / diff_matrix.size * 100),
            'color_changes': color_changes,
            'modified_positions': np.argwhere(diff_matrix == 1).tolist()
        }

    def analyze_modified_regions(self, diff_matrix: np.ndarray) -> Dict[str, Any]:
        """
        Analyse les régions modifiées en utilisant scikit-image

        Args:
            diff_matrix: Matrice binaire des différences

        Returns:
            Dict contenant l'analyse des régions modifiées
        """
        if np.sum(diff_matrix) == 0:
            return {'regions': [], 'total_regions': 0}

        # Étiqueter les régions connexes
        labeled_diff = label(diff_matrix)
        regions = regionprops(labeled_diff)

        region_info = []
        for region in regions:
            region_info.append({
                'area': int(region.area),
                'centroid': [float(region.centroid[0]), float(region.centroid[1])],
                'bbox': [int(x) for x in region.bbox],
                'eccentricity': float(region.eccentricity),
                'solidity': float(region.solidity),
                'perimeter': float(region.perimeter),
                'orientation': float(region.orientation)
            })

        return {
            'regions': region_info,
            'total_regions': len(region_info),
            'total_modified_area': sum(r['area'] for r in region_info)
        }

    def analyze_differences(self, puzzle: Dict) -> Dict[str, Any]:
        """
        Analyse complète des différences pour tous les exemples du puzzle

        Args:
            puzzle: Dict contenant les exemples train et test

        Returns:
            Dict contenant l'analyse des différences
        """
        diff_analysis = {
            'train_diffs': [],
            'compatible_examples': 0,
            'total_examples': len(puzzle['train']),
            'average_changes': 0.0,
            'common_transformations': {}
        }

        total_changes = 0
        compatible_count = 0
        all_color_changes = Counter()

        for example in puzzle['train']:
            input_grid = np.array(example['input'])
            output_grid = np.array(example['output'])

            # Calculer la diff
            diff_result = self.compute_diff(input_grid, output_grid)

            if diff_result['compatible']:
                compatible_count += 1
                total_changes += diff_result['total_changes']

                # Analyser les régions modifiées
                diff_matrix = np.array(diff_result['diff_matrix'])
                regions_analysis = self.analyze_modified_regions(diff_matrix)

                diff_result['regions_analysis'] = regions_analysis
                all_color_changes.update(diff_result['color_changes'])

            diff_analysis['train_diffs'].append(diff_result)

        diff_analysis['compatible_examples'] = compatible_count
        if compatible_count > 0:
            diff_analysis['average_changes'] = total_changes / compatible_count
        diff_analysis['common_color_changes'] = dict(all_color_changes.most_common(10))

        return diff_analysis

    def extract_objects_with_regionprops(self, grid: np.ndarray) -> List[Dict]:
        """
        Extrait les objets d'une grille en utilisant scikit-image regionprops

        Args:
            grid: Grille à analyser

        Returns:
            Liste des objets avec leurs propriétés détaillées
        """
        # Créer un masque pour les objets (non-zéro)
        object_mask = grid != 0

        if not np.any(object_mask):
            return []

        # Étiqueter les objets connexes
        labeled_grid = label(object_mask)
        regions = regionprops(labeled_grid, intensity_image=grid)

        objects = []
        for region in regions:
            # Extraire la couleur dominante de l'objet
            object_pixels = grid[region.coords[:, 0], region.coords[:, 1]]
            color_counts = Counter(object_pixels)
            dominant_color = color_counts.most_common(1)[0][0]

            obj_info = {
                'area': int(region.area),
                'centroid': [float(region.centroid[0]), float(region.centroid[1])],
                'bbox': [int(x) for x in region.bbox],
                'color': int(dominant_color),
                'perimeter': float(region.perimeter),
                'eccentricity': float(region.eccentricity),
                'solidity': float(region.solidity),
                'orientation': float(region.orientation),
                'major_axis_length': float(region.major_axis_length),
                'minor_axis_length': float(region.minor_axis_length),
                'pixels': region.coords.tolist(),
                'convex_area': int(region.convex_area),
                'filled_area': int(region.filled_area)
            }
            objects.append(obj_info)

        return objects

    def analyze_objects_with_regionprops(self, puzzle: Dict) -> Dict[str, Any]:
        """
        Analyse les objets de tous les exemples en utilisant regionprops

        Args:
            puzzle: Dict contenant les exemples

        Returns:
            Dict contenant l'analyse des objets avec regionprops
        """
        analysis = {
            'train_objects': [],
            'object_statistics': {
                'total_input_objects': 0,
                'total_output_objects': 0,
                'average_object_area': 0.0,
                'common_shapes': {},
                'color_distribution': Counter()
            }
        }

        all_input_objects = []
        all_output_objects = []

        for example in puzzle['train']:
            input_grid = np.array(example['input'])
            output_grid = np.array(example['output'])

            input_objects = self.extract_objects_with_regionprops(input_grid)
            output_objects = self.extract_objects_with_regionprops(output_grid)

            all_input_objects.extend(input_objects)
            all_output_objects.extend(output_objects)

            # Analyser les transformations d'objets
            object_transformations = self.analyze_object_transformations_enhanced(
                input_objects, output_objects
            )

            analysis['train_objects'].append({
                'input_objects': input_objects,
                'output_objects': output_objects,
                'transformations': object_transformations
            })

        # Calculer les statistiques globales
        all_objects = all_input_objects + all_output_objects
        if all_objects:
            analysis['object_statistics']['total_input_objects'] = len(all_input_objects)
            analysis['object_statistics']['total_output_objects'] = len(all_output_objects)
            analysis['object_statistics']['average_object_area'] = np.mean([obj['area'] for obj in all_objects])

            # Distribution des couleurs
            for obj in all_objects:
                analysis['object_statistics']['color_distribution'][obj['color']] += 1

        # Convertir Counter en dict pour la sérialisation JSON
        analysis['object_statistics']['color_distribution'] = dict(analysis['object_statistics']['color_distribution'])

        # Formes communes (basées sur l'excentricité)
        shape_categories = {'circular': 0, 'elongated': 0, 'rectangular': 0}
        for obj in all_objects:
            if obj['eccentricity'] < 0.3:
                shape_categories['circular'] += 1
            elif obj['eccentricity'] > 0.8:
                shape_categories['elongated'] += 1
            else:
                shape_categories['rectangular'] += 1

        analysis['object_statistics']['common_shapes'] = shape_categories

        return analysis

    def analyze_object_transformations_enhanced(self, input_objects: List[Dict],
                                              output_objects: List[Dict]) -> Dict[str, Any]:
        """
        Analyse les transformations entre objets avec les propriétés regionprops

        Args:
            input_objects: Objets d'entrée
            output_objects: Objets de sortie

        Returns:
            Dict contenant l'analyse des transformations
        """
        transformations = {
            'count_change': len(output_objects) - len(input_objects),
            'area_changes': [],
            'position_changes': [],
            'shape_changes': [],
            'color_changes': [],
            'size_scaling': []
        }

        # Analyser les changements si le nombre d'objets est similaire
        if len(input_objects) > 0 and len(output_objects) > 0:
            # Calculer les changements moyens
            input_areas = [obj['area'] for obj in input_objects]
            output_areas = [obj['area'] for obj in output_objects]

            transformations['area_changes'] = {
                'input_total': sum(input_areas),
                'output_total': sum(output_areas),
                'average_input': np.mean(input_areas) if input_areas else 0,
                'average_output': np.mean(output_areas) if output_areas else 0
            }

            # Analyser les changements de couleur
            input_colors = Counter([obj['color'] for obj in input_objects])
            output_colors = Counter([obj['color'] for obj in output_objects])

            transformations['color_changes'] = {
                'input_colors': dict(input_colors),
                'output_colors': dict(output_colors),
                'new_colors': list(set(output_colors.keys()) - set(input_colors.keys())),
                'removed_colors': list(set(input_colors.keys()) - set(output_colors.keys()))
            }

        return transformations

    def to_json_serializable(self, obj):
        """
        Convertit les objets NumPy en types Python natifs pour la sérialisation JSON

        Args:
            obj: Objet à convertir

        Returns:
            Objet sérialisable en JSON
        """
        if isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, dict):
            return {str(k): self.to_json_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self.to_json_serializable(item) for item in obj]
        elif isinstance(obj, tuple):
            return [self.to_json_serializable(item) for item in obj]
        elif isinstance(obj, set):
            return list(obj)
        elif isinstance(obj, Counter):
            return dict(obj)
        else:
            return obj

    def get_enhanced_analysis_structure(self, analysis: Dict) -> Any:
        """
        Restructure l'analyse selon le format proposé dans le document

        Args:
            analysis: Analyse brute

        Returns:
            Analyse restructurée selon le nouveau format
        """
        enhanced = {
            'metadata': {
                'analyzer_version': '2.0_enhanced',
                'features': ['diff_analysis', 'regionprops', 'vectorized_operations'],
                'total_examples': analysis['grid_info']['train_examples']
            },
            'grid_structure': analysis['grid_info'],
            'traditional_analysis': {
                'objects': analysis['objects'],
                'patterns': analysis['patterns'],
                'transformations': analysis['transformations'],
                'symmetries': analysis['symmetries'],
                'colors': analysis['colors'],
                'spatial_relations': analysis['spatial_relations']
            },
            'enhanced_analysis': {
                'diff_analysis': analysis.get('diff_analysis', {}),
                'regionprops_objects': analysis.get('enhanced_objects', {}),
                'complexity_metrics': analysis['complexity']
            },
            'summary': {
                'compatible_transformations': 0,
                'primary_transformation_type': 'unknown',
                'confidence_score': 0.0
            }
        }

        # Calculer le résumé
        if 'diff_analysis' in analysis:
            diff_data = analysis['diff_analysis']
            enhanced['summary']['compatible_transformations'] = diff_data.get('compatible_examples', 0)

            # Déterminer le type de transformation principal
            if analysis['patterns']['repetition']['detected']:
                enhanced['summary']['primary_transformation_type'] = 'repetition'
                enhanced['summary']['confidence_score'] = 0.9
            elif analysis['patterns']['scaling']['detected']:
                enhanced['summary']['primary_transformation_type'] = 'scaling'
                enhanced['summary']['confidence_score'] = 0.8
            elif diff_data.get('compatible_examples', 0) > 0:
                enhanced['summary']['primary_transformation_type'] = 'modification'
                enhanced['summary']['confidence_score'] = 0.7

        return self.to_json_serializable(enhanced)

    def analyze_grid_structure(self, puzzle: Dict) -> Dict[str, Any]:
        """Analyse la structure basique des grilles"""
        info = {
            'train_examples': len(puzzle['train']),
            'input_dimensions': [],
            'output_dimensions': [],
            'dimension_changes': [],
            'size_ratios': []
        }
        
        for example in puzzle['train']:
            input_grid = np.array(example['input'])
            output_grid = np.array(example['output'])
            
            in_shape = input_grid.shape
            out_shape = output_grid.shape
            
            info['input_dimensions'].append(in_shape)
            info['output_dimensions'].append(out_shape)
            info['dimension_changes'].append((out_shape[0] - in_shape[0], out_shape[1] - in_shape[1]))
            
            if in_shape[0] > 0 and in_shape[1] > 0:
                info['size_ratios'].append((out_shape[0] / in_shape[0], out_shape[1] / in_shape[1]))
        
        # Détection de patterns dans les changements de dimension
        if info['dimension_changes']:
            unique_changes = set(info['dimension_changes'])
            info['consistent_dimension_change'] = len(unique_changes) == 1
            info['most_common_change'] = Counter(info['dimension_changes']).most_common(1)[0][0]
        
        return info
    
    def analyze_objects(self, puzzle: Dict) -> Dict[str, Any]:
        """Analyse des objets dans les grilles"""
        objects_info = {
            'input_objects': [],
            'output_objects': [],
            'object_transformations': []
        }
        
        for example in puzzle['train']:
            input_grid = np.array(example['input'])
            output_grid = np.array(example['output'])
            
            input_objects = self.extract_objects(input_grid)
            output_objects = self.extract_objects(output_grid)
            
            objects_info['input_objects'].append(input_objects)
            objects_info['output_objects'].append(output_objects)
            objects_info['object_transformations'].append(
                self.analyze_object_transformation(input_objects, output_objects)
            )
        
        return objects_info
    
    def extract_objects(self, grid: np.ndarray) -> List[Dict]:
        """Extrait les objets d'une grille par connexité"""
        objects = []
        visited = np.zeros_like(grid, dtype=bool)
        
        for i in range(grid.shape[0]):
            for j in range(grid.shape[1]):
                if not visited[i, j] and grid[i, j] != 0:  # 0 = background
                    obj = self.flood_fill(grid, i, j, visited)
                    if len(obj['pixels']) > 0:
                        objects.append(obj)
        
        return objects
    
    def flood_fill(self, grid: np.ndarray, start_i: int, start_j: int, visited: np.ndarray) -> Dict:
        """Remplit une région connexe et retourne les informations de l'objet"""
        color = grid[start_i, start_j]
        pixels = []
        stack = [(start_i, start_j)]
        
        while stack:
            i, j = stack.pop()
            if (i < 0 or i >= grid.shape[0] or j < 0 or j >= grid.shape[1] or 
                visited[i, j] or grid[i, j] != color):
                continue
                
            visited[i, j] = True
            pixels.append((i, j))
            
            # Connexité 4 (haut, bas, gauche, droite)
            for di, dj in [(-1, 0), (1, 0), (0, -1), (0, 1)]:
                stack.append((i + di, j + dj))
        
        if pixels:
            pixels = np.array(pixels)
            return {
                'color': int(color),
                'pixels': pixels,
                'size': len(pixels),
                'bbox': (pixels[:, 0].min(), pixels[:, 1].min(), 
                        pixels[:, 0].max(), pixels[:, 1].max()),
                'center': (pixels[:, 0].mean(), pixels[:, 1].mean())
            }
        return {'color': int(color), 'pixels': [], 'size': 0, 'bbox': None, 'center': None}
    
    def analyze_object_transformation(self, input_objects: List[Dict], output_objects: List[Dict]) -> Dict:
        """Analyse les transformations entre objets d'entrée et de sortie"""
        return {
            'input_count': len(input_objects),
            'output_count': len(output_objects),
            'count_change': len(output_objects) - len(input_objects),
            'color_changes': self.analyze_color_changes(input_objects, output_objects),
            'size_changes': self.analyze_size_changes(input_objects, output_objects)
        }
    
    def analyze_color_changes(self, input_objects: List[Dict], output_objects: List[Dict]) -> Dict:
        """Analyse les changements de couleur entre objets"""
        input_colors = Counter([obj['color'] for obj in input_objects])
        output_colors = Counter([obj['color'] for obj in output_objects])
        
        return {
            'input_colors': dict(input_colors),
            'output_colors': dict(output_colors),
            'new_colors': set(output_colors.keys()) - set(input_colors.keys()),
            'removed_colors': set(input_colors.keys()) - set(output_colors.keys())
        }
    
    def analyze_size_changes(self, input_objects: List[Dict], output_objects: List[Dict]) -> Dict:
        """Analyse les changements de taille entre objets"""
        input_sizes = [obj['size'] for obj in input_objects]
        output_sizes = [obj['size'] for obj in output_objects]
        
        return {
            'input_sizes': input_sizes,
            'output_sizes': output_sizes,
            'size_ratio': np.mean(output_sizes) / np.mean(input_sizes) if input_sizes else 0
        }
    
    def detect_patterns(self, puzzle: Dict) -> Dict[str, Any]:
        """Détecte les patterns récurrents dans le puzzle"""
        patterns = {
            'repetition': self.detect_repetition_patterns(puzzle),
            'scaling': self.detect_scaling_patterns(puzzle),
            'rotation': self.detect_rotation_patterns(puzzle),
            'reflection': self.detect_reflection_patterns(puzzle),
            'translation': self.detect_translation_patterns(puzzle),
            'motif': self.detect_motif_patterns(puzzle)
        }
        return patterns
    
    def detect_repetition_patterns(self, puzzle: Dict) -> Dict:
        """Détecte les patterns de répétition (comme dans votre exemple)"""
        repetition_info = {'detected': False, 'type': None, 'factor': None}
        
        for example in puzzle['train']:
            input_grid = np.array(example['input'])
            output_grid = np.array(example['output'])
            
            # Test pour répétition en matrice (comme votre puzzle)
            if self.is_matrix_repetition(input_grid, output_grid):
                repetition_info['detected'] = True
                repetition_info['type'] = 'matrix_tiling'
                repetition_info['factor'] = output_grid.shape[0] // input_grid.shape[0]
                break
        
        return repetition_info
    
    def is_matrix_repetition(self, input_grid: np.ndarray, output_grid: np.ndarray) -> bool:
        """Vérifie si output est une répétition matricielle d'input"""
        in_h, in_w = input_grid.shape
        out_h, out_w = output_grid.shape
        
        # Vérifier si les dimensions sont des multiples
        if out_h % in_h != 0 or out_w % in_w != 0:
            return False
        
        h_factor = out_h // in_h
        w_factor = out_w // in_w
        
        # Vérifier la répétition
        for i in range(h_factor):
            for j in range(w_factor):
                start_i, start_j = i * in_h, j * in_w
                end_i, end_j = start_i + in_h, start_j + in_w
                
                if not np.array_equal(input_grid, output_grid[start_i:end_i, start_j:end_j]):
                    return False
        
        return True
    
    def detect_scaling_patterns(self, puzzle: Dict) -> Dict:
        """Détecte les patterns de mise à l'échelle"""
        scaling_info = {'detected': False, 'factors': []}
        
        for example in puzzle['train']:
            input_grid = np.array(example['input'])
            output_grid = np.array(example['output'])
            
            if input_grid.shape[0] > 0 and input_grid.shape[1] > 0:
                h_factor = output_grid.shape[0] / input_grid.shape[0]
                w_factor = output_grid.shape[1] / input_grid.shape[1]
                scaling_info['factors'].append((h_factor, w_factor))
        
        if scaling_info['factors']:
            unique_factors = set(scaling_info['factors'])
            scaling_info['detected'] = len(unique_factors) == 1 and (1.0, 1.0) not in unique_factors
            scaling_info['consistent_factor'] = len(unique_factors) == 1
        
        return scaling_info
    
    def detect_rotation_patterns(self, puzzle: Dict) -> Dict:
        """Détecte les patterns de rotation"""
        # Implémentation simplifiée - pourrait être étendue
        return {'detected': False, 'angles': []}
    
    def detect_reflection_patterns(self, puzzle: Dict) -> Dict:
        """Détecte les patterns de réflexion"""
        # Implémentation simplifiée - pourrait être étendue
        return {'detected': False, 'axes': []}
    
    def detect_translation_patterns(self, puzzle: Dict) -> Dict:
        """Détecte les patterns de translation"""
        # Implémentation simplifiée - pourrait être étendue
        return {'detected': False, 'vectors': []}
    
    def detect_motif_patterns(self, puzzle: Dict) -> Dict:
        """Détecte les patterns MOTIF (crucial pour 30% des puzzles ARC)"""
        motif_info = {
            'detected': False,
            'type': None,
            'confidence': 0.0,
            'motif_dimensions': None,
            'background_colors': [],
            'motif_colors': [],
            'tiling_factor': None,
            'extraction_method': None
        }
        
        # Type 1: Tiling pattern (007bbfb7)
        tiling_result = self.detect_tiling_motif(puzzle)
        
        # Type 2: Detached motif (36d67576)  
        detached_result = self.detect_detached_motif(puzzle)
        
        # Type 3: Coordinate motif (017c7c7b, 0520fde7)
        coordinate_result = self.detect_coordinate_motif(puzzle)
        
        # Choisir le meilleur résultat par priorité
        results = [tiling_result, detached_result, coordinate_result]
        best_result = max(results, key=lambda x: x['confidence'])
        
        if best_result['confidence'] > 0.5:  # Seuil minimum ajusté
            motif_info.update(best_result)
            
        return motif_info
    
    def detect_tiling_motif(self, puzzle: Dict) -> Dict:
        """Détecte les motifs de type tiling (input → répétition dans output)"""
        result = {
            'detected': False,
            'type': 'tiling',
            'confidence': 0.0,
            'motif_dimensions': None,
            'tiling_factor': None,
            'extraction_method': 'full_input'
        }
        
        consistent_ratios = []
        
        for example in puzzle['train']:
            input_grid = np.array(example['input'])
            output_grid = np.array(example['output'])
            
            in_h, in_w = input_grid.shape
            out_h, out_w = output_grid.shape
            
            # Vérifier si output est multiple exact de input
            if out_h % in_h == 0 and out_w % in_w == 0:
                h_factor = out_h // in_h
                w_factor = out_w // in_w
                
                # Pour 007bbfb7: 3x3 → 9x9 = facteur (3,3)
                if h_factor > 1 or w_factor > 1:  # Au moins un facteur > 1
                    # Test de tiling plus permissif pour ARC
                    if self.validate_tiling_permissive(input_grid, output_grid, h_factor, w_factor):
                        consistent_ratios.append((h_factor, w_factor))
        
        # Si au moins 20% des exemples ont un tiling cohérent (ARC est permissif)
        if len(consistent_ratios) >= max(1, len(puzzle['train']) * 0.2):
            unique_ratios = list(set(consistent_ratios))
            if len(unique_ratios) == 1:  # Tous les exemples ont le même ratio
                ratio = unique_ratios[0]
                result.update({
                    'detected': True,
                    'confidence': 0.95,
                    'motif_dimensions': list(puzzle['train'][0]['input']),
                    'tiling_factor': ratio,
                    'total_tiles': ratio[0] * ratio[1]
                })
        
        return result
    
    def validate_tiling_permissive(self, input_grid: np.ndarray, output_grid: np.ndarray, 
                                  h_factor: int, w_factor: int) -> bool:
        """Validation permissive du tiling (ARC a des variations)"""
        in_h, in_w = input_grid.shape
        
        # Compter les tiles qui ressemblent à l'input
        similar_tiles = 0
        total_tiles = h_factor * w_factor
        
        for i in range(h_factor):
            for j in range(w_factor):
                start_i, start_j = i * in_h, j * in_w
                end_i, end_j = start_i + in_h, start_j + in_w
                
                if end_i <= output_grid.shape[0] and end_j <= output_grid.shape[1]:
                    tile = output_grid[start_i:end_i, start_j:end_j]
                    
                    # Similarité structurelle (même couleurs présentes)
                    input_colors = set(input_grid.flatten())
                    tile_colors = set(tile.flatten())
                    
                    # Si les couleurs sont similaires, c'est probablement un tiling
                    color_overlap = len(input_colors.intersection(tile_colors)) / len(input_colors.union(tile_colors))
                    
                    if color_overlap > 0.5:  # Au moins 50% de couleurs communes
                        similar_tiles += 1
        
        # Au moins 60% des tiles doivent être similaires
        return similar_tiles / total_tiles >= 0.6
    
    def validate_tiling(self, input_grid: np.ndarray, output_grid: np.ndarray, 
                       h_factor: int, w_factor: int) -> bool:
        """Valide si output est un tiling cohérent de input"""
        in_h, in_w = input_grid.shape
        
        # Tolérance pour variations (ARC n'est pas toujours exact)
        matches = 0
        total_tiles = h_factor * w_factor
        
        for i in range(h_factor):
            for j in range(w_factor):
                start_i, start_j = i * in_h, j * in_w
                end_i, end_j = start_i + in_h, start_j + in_w
                
                tile = output_grid[start_i:end_i, start_j:end_j]
                
                # Calculer similarité (pas forcément identique)
                similarity = self.compute_grid_similarity(input_grid, tile)
                if similarity > 0.7:  # 70% de similarité minimum
                    matches += 1
        
        # Au moins 80% des tiles doivent être similaires
        return matches / total_tiles >= 0.8
    
    def compute_grid_similarity(self, grid1: np.ndarray, grid2: np.ndarray) -> float:
        """Calcule la similarité entre deux grilles"""
        if grid1.shape != grid2.shape:
            return 0.0
        
        total_cells = grid1.size
        matching_cells = np.sum(grid1 == grid2)
        
        return matching_cells / total_cells
    
    def detect_detached_motif(self, puzzle: Dict) -> Dict:
        """Détecte les motifs détachés du fond"""
        result = {
            'detected': False,
            'type': 'detached',
            'confidence': 0.0,
            'background_colors': [],
            'motif_colors': [],
            'extraction_method': 'color_selection'
        }
        
        # Analyser les couleurs pour identifier fond vs motif
        for example in puzzle['train']:
            input_grid = np.array(example['input'])
            
            # Identifier couleur de fond (plus fréquente)
            color_counts = Counter(input_grid.flatten())
            most_common_color = color_counts.most_common(1)[0][0]
            
            # Seuil plus permissif : si une couleur domine (>40%), c'est probablement le fond
            total_cells = input_grid.size
            dominance = color_counts[most_common_color] / total_cells
            
            # Critères BEAUCOUP plus stricts pour éviter faux positifs
            if (dominance > 0.6 and dominance < 0.95 and  # Dominance modérée
                len(color_counts) >= 2 and len(color_counts) <= 5):  # 2-5 couleurs max
                
                background_color = most_common_color
                motif_colors = [c for c in color_counts.keys() if c != background_color]
                
                # Vérifications supplémentaires pour éviter faux positifs
                motif_pixel_count = sum(color_counts[c] for c in motif_colors)
                motif_ratio = motif_pixel_count / total_cells
                
                # Le motif doit représenter au moins 10% mais pas plus de 40% de la grille
                if 0.1 <= motif_ratio <= 0.4:
                    # Confiance basée sur des critères stricts
                    confidence = 0.6 + (motif_ratio * 0.3)  # Max 0.72
                    
                    result.update({
                        'detected': True,
                        'confidence': confidence,
                        'background_colors': [background_color],
                        'motif_colors': motif_colors
                    })
                    break
        
        return result
    
    def detect_coordinate_motif(self, puzzle: Dict) -> Dict:
        """Détecte les motifs par coordonnées directes (COPY [x,y z,w])"""
        result = {
            'detected': False,
            'type': 'coordinate',
            'confidence': 0.0,
            'extraction_method': 'coordinate_selection'
        }
        
        # Analyser les ratios de dimensions pour détecter patterns
        dimension_ratios = []
        for example in puzzle['train']:
            input_shape = (len(example['input']), len(example['input'][0]))
            output_shape = (len(example['output']), len(example['output'][0]))
            
            if input_shape[0] > 0 and input_shape[1] > 0:
                ratio = (output_shape[0] / input_shape[0], output_shape[1] / input_shape[1])
                dimension_ratios.append(ratio)
        
        # Détecter patterns spécifiques
        if dimension_ratios:
            unique_ratios = list(set(dimension_ratios))
            
            # Pattern 1: Ratios non-entiers (1.5x1.0, etc.) - souvent coordonnées
            non_integer_ratios = [r for r in unique_ratios if r[0] != int(r[0]) or r[1] != int(r[1])]
            
            # Pattern 2: Contraction (output plus petit) - souvent extraction
            contraction_ratios = [r for r in unique_ratios if r[0] < 1.0 or r[1] < 1.0]
            
            if non_integer_ratios or contraction_ratios:
                result.update({
                    'detected': True,
                    'confidence': 0.6,
                    'dimension_ratios': unique_ratios
                })
        
        return result
    
    def find_symmetries(self, puzzle: Dict) -> Dict[str, Any]:
        """Trouve les symétries dans les grilles"""
        symmetries = {
            'input_symmetries': [],
            'output_symmetries': [],
            'symmetry_preservation': []
        }
        
        for example in puzzle['train']:
            input_grid = np.array(example['input'])
            output_grid = np.array(example['output'])
            
            input_sym = self.detect_symmetries(input_grid)
            output_sym = self.detect_symmetries(output_grid)
            
            symmetries['input_symmetries'].append(input_sym)
            symmetries['output_symmetries'].append(output_sym)
            symmetries['symmetry_preservation'].append(
                self.compare_symmetries(input_sym, output_sym)
            )
        
        return symmetries
    
    def detect_symmetries(self, grid: np.ndarray) -> Dict[str, bool]:
        """Détecte les symétries dans une grille"""
        return {
            'horizontal': np.array_equal(grid, np.flipud(grid)),
            'vertical': np.array_equal(grid, np.fliplr(grid)),
            'diagonal_main': np.array_equal(grid, grid.T) if grid.shape[0] == grid.shape[1] else False,
            'diagonal_anti': np.array_equal(grid, np.rot90(grid, 2).T) if grid.shape[0] == grid.shape[1] else False
        }
    
    def compare_symmetries(self, sym1: Dict[str, bool], sym2: Dict[str, bool]) -> Dict[str, bool]:
        """Compare deux ensembles de symétries"""
        return {key: sym1[key] == sym2[key] for key in sym1.keys()}
    
    def infer_transformations(self, puzzle: Dict) -> Dict[str, Any]:
        """Infère les types de transformations possibles"""
        transformations = {
            'geometric': self.infer_geometric_transformations(puzzle),
            'color': self.infer_color_transformations(puzzle),
            'structural': self.infer_structural_transformations(puzzle)
        }
        return transformations
    
    def infer_geometric_transformations(self, puzzle: Dict) -> List[str]:
        """Infère les transformations géométriques"""
        transformations = []
        
        patterns = self.detect_patterns(puzzle)
        
        if patterns['repetition']['detected']:
            transformations.append('repetition')
        if patterns['scaling']['detected']:
            transformations.append('scaling')
        if patterns['rotation']['detected']:
            transformations.append('rotation')
        if patterns['reflection']['detected']:
            transformations.append('reflection')
        if patterns['translation']['detected']:
            transformations.append('translation')
        
        return transformations
    
    def infer_color_transformations(self, puzzle: Dict) -> List[str]:
        """Infère les transformations de couleur"""
        transformations = []
        
        for example in puzzle['train']:
            input_grid = np.array(example['input'])
            output_grid = np.array(example['output'])
            
            input_colors = set(input_grid.flatten())
            output_colors = set(output_grid.flatten())
            
            if input_colors != output_colors:
                if len(output_colors) > len(input_colors):
                    transformations.append('color_addition')
                elif len(output_colors) < len(input_colors):
                    transformations.append('color_removal')
                else:
                    transformations.append('color_substitution')
        
        return list(set(transformations))
    
    def infer_structural_transformations(self, puzzle: Dict) -> List[str]:
        """Infère les transformations structurelles"""
        transformations = []
        
        grid_info = self.analyze_grid_structure(puzzle)
        
        if grid_info['consistent_dimension_change']:
            if grid_info['most_common_change'] == (0, 0):
                transformations.append('in_place_transformation')
            elif all(c >= 0 for c in grid_info['most_common_change']):
                transformations.append('expansion')
            else:
                transformations.append('contraction')
        
        return transformations
    
    def measure_complexity(self, puzzle: Dict) -> Dict[str, float]:
        """Mesure la complexité du puzzle"""
        complexity: Dict[str, float] = {
            'grid_size_complexity': 0.0,
            'color_complexity': 0.0,
            'object_complexity': 0.0,
            'transformation_complexity': 0.0,
            'overall_complexity': 0.0
        }
        
        total_cells = 0
        unique_colors = set()
        total_objects = 0
        
        for example in puzzle['train']:
            input_grid = np.array(example['input'])
            output_grid = np.array(example['output'])
            
            total_cells += input_grid.size + output_grid.size
            unique_colors.update(input_grid.flatten())
            unique_colors.update(output_grid.flatten())
            
            input_objects = self.extract_objects(input_grid)
            output_objects = self.extract_objects(output_grid)
            total_objects += len(input_objects) + len(output_objects)
        
        complexity['grid_size_complexity'] = total_cells / len(puzzle['train'])
        complexity['color_complexity'] = len(unique_colors)
        complexity['object_complexity'] = total_objects / len(puzzle['train'])
        
        # Complexité des transformations basée sur le nombre de types détectés
        transformations = self.infer_transformations(puzzle)
        complexity['transformation_complexity'] = float(
            len(transformations['geometric']) + 
            len(transformations['color']) + 
            len(transformations['structural'])
        )
        
        complexity['overall_complexity'] = float(
            complexity['grid_size_complexity'] * 0.2 +
            complexity['color_complexity'] * 0.3 +
            complexity['object_complexity'] * 0.3 +
            complexity['transformation_complexity'] * 0.2
        )
        
        return complexity
    
    def analyze_colors(self, puzzle: Dict) -> Dict[str, Any]:
        """Analyse les couleurs utilisées dans le puzzle"""
        color_info = {
            'input_colors': set(),
            'output_colors': set(),
            'color_frequency': Counter(),
            'color_mapping': {}
        }
        
        for example in puzzle['train']:
            input_grid = np.array(example['input'])
            output_grid = np.array(example['output'])
            
            input_colors = set(input_grid.flatten())
            output_colors = set(output_grid.flatten())
            
            color_info['input_colors'].update(input_colors)
            color_info['output_colors'].update(output_colors)
            
            for color in input_colors:
                color_info['color_frequency'][color] += np.sum(input_grid == color)
            for color in output_colors:
                color_info['color_frequency'][color] += np.sum(output_grid == color)
        
        color_info['input_colors'] = list(color_info['input_colors'])
        color_info['output_colors'] = list(color_info['output_colors'])
        
        return color_info
    
    def analyze_spatial_relations(self, puzzle: Dict) -> Dict[str, Any]:
        """Analyse les relations spatiales entre objets"""
        spatial_info = {
            'adjacency_patterns': [],
            'distance_patterns': [],
            'alignment_patterns': []
        }
        
        for example in puzzle['train']:
            input_grid = np.array(example['input'])
            objects = self.extract_objects(input_grid)
            
            if len(objects) > 1:
                adjacencies = self.compute_adjacencies(objects)
                distances = self.compute_distances(objects)
                alignments = self.compute_alignments(objects)
                
                spatial_info['adjacency_patterns'].append(adjacencies)
                spatial_info['distance_patterns'].append(distances)
                spatial_info['alignment_patterns'].append(alignments)
        
        return spatial_info
    
    def compute_adjacencies(self, objects: List[Dict]) -> List[Tuple[int, int]]:
        """Calcule les adjacences entre objets"""
        adjacencies = []
        for i, obj1 in enumerate(objects):
            for j, obj2 in enumerate(objects[i+1:], i+1):
                if self.are_adjacent(obj1, obj2):
                    adjacencies.append((i, j))
        return adjacencies
    
    def are_adjacent(self, obj1: Dict, obj2: Dict) -> bool:
        """Vérifie si deux objets sont adjacents"""
        pixels1 = set(map(tuple, obj1['pixels']))
        pixels2 = set(map(tuple, obj2['pixels']))
        
        for i, j in pixels1:
            for di, dj in [(-1, 0), (1, 0), (0, -1), (0, 1)]:
                if (i + di, j + dj) in pixels2:
                    return True
        return False
    
    def compute_distances(self, objects: List[Dict]) -> List[float]:
        """Calcule les distances entre centres d'objets"""
        distances = []
        for i, obj1 in enumerate(objects):
            for j, obj2 in enumerate(objects[i+1:], i+1):
                if obj1['center'] is not None and obj2['center'] is not None:
                    dist = np.sqrt(
                        (obj1['center'][0] - obj2['center'][0])**2 +
                        (obj1['center'][1] - obj2['center'][1])**2
                    )
                    distances.append(dist)
        return distances
    
    def compute_alignments(self, objects: List[Dict]) -> Dict[str, int]:
        """Calcule les alignements entre objets"""
        alignments = {'horizontal': 0, 'vertical': 0, 'diagonal': 0}
        
        for i, obj1 in enumerate(objects):
            for j, obj2 in enumerate(objects[i+1:], i+1):
                if obj1['center'] is not None and obj2['center'] is not None:
                    c1, c2 = obj1['center'], obj2['center']
                    
                    if abs(c1[0] - c2[0]) < 0.5:  # Même ligne
                        alignments['horizontal'] += 1
                    elif abs(c1[1] - c2[1]) < 0.5:  # Même colonne
                        alignments['vertical'] += 1
                    elif abs(abs(c1[0] - c2[0]) - abs(c1[1] - c2[1])) < 0.5:  # Diagonale
                        alignments['diagonal'] += 1
        
        return alignments
    
    def generate_report(self, analysis: Dict) -> str:
        """Génère un rapport textuel de l'analyse"""
        report = []
        report.append("=== RAPPORT D'ANALYSE ARC ===\n")
        
        # Informations générales
        grid_info = analysis['grid_info']
        report.append(f"Nombre d'exemples d'entraînement: {grid_info['train_examples']}")
        report.append(f"Dimensions d'entrée: {grid_info['input_dimensions']}")
        report.append(f"Dimensions de sortie: {grid_info['output_dimensions']}")
        
        if grid_info['consistent_dimension_change']:
            report.append(f"Changement de dimension cohérent: {grid_info['most_common_change']}")
        
        # Patterns détectés
        patterns = analysis['patterns']
        report.append("\n--- PATTERNS DÉTECTÉS ---")
        
        if patterns['repetition']['detected']:
            report.append(f"✓ Répétition {patterns['repetition']['type']} détectée")
        
        if patterns['scaling']['detected']:
            report.append(f"✓ Mise à l'échelle détectée")
        
        # Transformations
        transformations = analysis['transformations']
        report.append("\n--- TRANSFORMATIONS ---")
        report.append(f"Géométriques: {', '.join(transformations['geometric'])}")
        report.append(f"Couleurs: {', '.join(transformations['color'])}")
        report.append(f"Structurelles: {', '.join(transformations['structural'])}")
        
        # Complexité
        complexity = analysis['complexity']
        report.append(f"\n--- COMPLEXITÉ ---")
        report.append(f"Complexité globale: {complexity['overall_complexity']:.2f}")
        
        return "\n".join(report)


# Fonction utilitaire pour analyser un puzzle depuis un fichier JSON
def analyze_puzzle_from_file(filepath: str) -> Dict:
    """Analyse un puzzle ARC depuis un fichier JSON"""
    with open(filepath, 'r') as f:
        puzzle_data = json.load(f)
    
    analyzer = ARCAnalyzer()
    analysis = analyzer.analyze_puzzle(puzzle_data)
    
    return analysis


# Exemple d'utilisation
if __name__ == "__main__":
    # Exemple avec le puzzle fourni
    puzzle_example = {
        "train": [
            {
                "input": [[0, 7, 7], [7, 7, 7], [0, 7, 7]],
                "output": [[0, 0, 0, 0, 7, 7, 0, 7, 7], [0, 0, 0, 7, 7, 7, 7, 7, 7], [0, 0, 0, 0, 7, 7, 0, 7, 7], [0, 7, 7, 0, 7, 7, 0, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7, 7], [0, 7, 7, 0, 7, 7, 0, 7, 7], [0, 0, 0, 0, 7, 7, 0, 7, 7], [0, 0, 0, 7, 7, 7, 7, 7, 7], [0, 0, 0, 0, 7, 7, 0, 7, 7]]
            },
            {
                "input": [[4, 0, 4], [0, 0, 0], [0, 4, 0]],
                "output": [[4, 0, 4, 0, 0, 0, 4, 0, 4], [0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 4, 0, 0, 0, 0, 0, 4, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 4, 0, 4, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 4, 0, 0, 0, 0]]
            }
        ],
        "test": [
            {
                "input": [[7, 0, 7], [7, 0, 7], [7, 7, 0]],
                "output": [[7, 0, 7, 0, 0, 0, 7, 0, 7], [7, 0, 7, 0, 0, 0, 7, 0, 7], [7, 7, 0, 0, 0, 0, 7, 7, 0], [7, 0, 7, 0, 0, 0, 7, 0, 7], [7, 0, 7, 0, 0, 0, 7, 0, 7], [7, 7, 0, 0, 0, 0, 7, 7, 0], [7, 0, 7, 7, 0, 7, 0, 0, 0], [7, 0, 7, 7, 0, 7, 0, 0, 0], [7, 7, 0, 7, 7, 0, 0, 0, 0]]
            }
        ]
    }
    
    analyzer = ARCAnalyzer()
    analysis = analyzer.analyze_puzzle(puzzle_example)
    
    print(analyzer.generate_report(analysis))
    
    # Affichage détaillé de l'analyse
    print("\n=== ANALYSE FACTISSE DÉTAILLÉE A PARTIR D'UN EXEMPLE ===")
    print(json.dumps(analysis, indent=2, default=str))