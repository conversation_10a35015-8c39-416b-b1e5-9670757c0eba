#!/usr/bin/env python3
"""
Validateur de mosaïque par symétrie pour la tâche 3631a71a
Test l'hypothèse : input replié = output (zones manquantes comblées)
"""

import numpy as np
import json
from pathlib import Path
from collections import Counter

class SymmetryMosaicValidator:
    """
    Validateur basé sur l'analyse de symétrie et repliement
    """
    
    def __init__(self):
        self.empty_value = 0
    
    def analyze_task_3631a71a(self):
        """
        Analyse spécifique de la tâche 3631a71a avec test de symétrie
        """
        # Charger la tâche
        task_path = Path("../arcdata/training/3631a71a.json")
        with open(task_path, 'r') as f:
            task_data = json.load(f)
        
        print("=" * 80)
        print("ANALYSE DE SYMÉTRIE - TÂCHE 3631a71a")
        print("=" * 80)
        
        results = {
            'is_symmetric_mosaic': False,
            'examples_analysis': [],
            'center_analysis': {},
            'symmetry_evidence': []
        }
        
        for i, example in enumerate(task_data['train']):
            input_grid = np.array(example['input'])
            output_grid = np.array(example['output'])
            
            print(f"\n🔍 EXEMPLE {i+1}:")
            example_analysis = self.analyze_symmetry_example(input_grid, output_grid, i)
            results['examples_analysis'].append(example_analysis)
            
            if example_analysis['has_symmetry']:
                results['symmetry_evidence'].append(f"Exemple {i+1}: {example_analysis['symmetry_type']}")
        
        # Analyse du centre
        first_input = np.array(task_data['train'][0]['input'])
        results['center_analysis'] = self.analyze_center(first_input)
        
        # Conclusion
        symmetric_examples = sum(1 for ex in results['examples_analysis'] if ex['has_symmetry'])
        results['is_symmetric_mosaic'] = symmetric_examples >= len(task_data['train']) * 0.75
        
        print(f"\n{'='*50}")
        print(f"CONCLUSION GLOBALE:")
        print(f"   Mosaïque symétrique: {'✅ OUI' if results['is_symmetric_mosaic'] else '❌ NON'}")
        print(f"   Exemples avec symétrie: {symmetric_examples}/{len(task_data['train'])}")
        
        if results['symmetry_evidence']:
            print(f"   Preuves de symétrie:")
            for evidence in results['symmetry_evidence']:
                print(f"      • {evidence}")
        
        return results
    
    def analyze_symmetry_example(self, input_grid: np.ndarray, output_grid: np.ndarray, example_idx: int) -> dict:
        """
        Analyse un exemple pour détecter les symétries
        """
        h, w = input_grid.shape
        center_h, center_w = h // 2, w // 2
        
        analysis = {
            'example_idx': example_idx,
            'has_symmetry': False,
            'symmetry_type': None,
            'center': (center_h, center_w),
            'uniform_blocks': [],
            'fold_test_results': {}
        }
        
        print(f"   Dimensions: {h}x{w}, Centre: ({center_h}, {center_w})")
        
        # 1. Identifier les blocs de couleur uniforme
        uniform_blocks = self.find_uniform_blocks(input_grid)
        analysis['uniform_blocks'] = uniform_blocks
        print(f"   Blocs uniformes détectés: {len(uniform_blocks)}")
        
        # 2. Test de repliement horizontal
        fold_h_result = self.test_horizontal_fold(input_grid, output_grid)
        analysis['fold_test_results']['horizontal'] = fold_h_result
        
        # 3. Test de repliement vertical  
        fold_v_result = self.test_vertical_fold(input_grid, output_grid)
        analysis['fold_test_results']['vertical'] = fold_v_result
        
        # 4. Test de repliement diagonal
        fold_d_result = self.test_diagonal_fold(input_grid, output_grid)
        analysis['fold_test_results']['diagonal'] = fold_d_result
        
        # Déterminer le type de symétrie
        if fold_h_result['similarity'] > 0.8:
            analysis['has_symmetry'] = True
            analysis['symmetry_type'] = 'horizontal'
            print(f"   ✅ Symétrie horizontale détectée (similarité: {fold_h_result['similarity']:.2f})")
        elif fold_v_result['similarity'] > 0.8:
            analysis['has_symmetry'] = True
            analysis['symmetry_type'] = 'vertical'
            print(f"   ✅ Symétrie verticale détectée (similarité: {fold_v_result['similarity']:.2f})")
        elif fold_d_result['similarity'] > 0.8:
            analysis['has_symmetry'] = True
            analysis['symmetry_type'] = 'diagonal'
            print(f"   ✅ Symétrie diagonale détectée (similarité: {fold_d_result['similarity']:.2f})")
        else:
            print(f"   ❌ Aucune symétrie claire détectée")
            print(f"      Similarités: H={fold_h_result['similarity']:.2f}, V={fold_v_result['similarity']:.2f}, D={fold_d_result['similarity']:.2f}")
        
        return analysis
    
    def find_uniform_blocks(self, grid: np.ndarray) -> list:
        """
        Trouve les blocs de couleur uniforme (rectangles 2x2 ou plus)
        """
        h, w = grid.shape
        uniform_blocks = []
        
        # Chercher des blocs 2x2 uniformes
        for i in range(h - 1):
            for j in range(w - 1):
                block_2x2 = grid[i:i+2, j:j+2]
                if len(np.unique(block_2x2)) == 1 and block_2x2[0,0] != self.empty_value:
                    # Essayer d'étendre le bloc
                    extended_block = self.extend_uniform_block(grid, i, j, block_2x2[0,0])
                    if extended_block not in uniform_blocks:
                        uniform_blocks.append(extended_block)
        
        return uniform_blocks
    
    def extend_uniform_block(self, grid: np.ndarray, start_i: int, start_j: int, color: int) -> dict:
        """
        Étend un bloc uniforme au maximum
        """
        h, w = grid.shape
        
        # Trouver l'extension maximale
        max_h = start_i + 1
        max_w = start_j + 1
        
        # Étendre vers le bas
        while max_h < h and np.all(grid[max_h, start_j:start_j+2] == color):
            max_h += 1
        
        # Étendre vers la droite
        while max_w < w and np.all(grid[start_i:max_h, max_w] == color):
            max_w += 1
        
        return {
            'color': int(color),
            'coords': (start_i, start_j, max_h, max_w),
            'size': (max_h - start_i, max_w - start_j),
            'area': (max_h - start_i) * (max_w - start_j)
        }
    
    def test_horizontal_fold(self, input_grid: np.ndarray, output_grid: np.ndarray) -> dict:
        """
        Test le repliement horizontal (haut vers bas)
        """
        h, w = input_grid.shape
        center_h = h // 2
        
        # Replier la partie haute sur la partie basse
        top_half = input_grid[:center_h, :]
        bottom_half = input_grid[center_h:, :]
        
        # Retourner la partie haute pour la superposer
        top_flipped = np.flipud(top_half)
        
        # Ajuster les tailles si nécessaire
        min_h = min(top_flipped.shape[0], bottom_half.shape[0])
        top_flipped = top_flipped[:min_h, :]
        bottom_half = bottom_half[:min_h, :]
        
        # Créer la grille repliée
        folded_grid = input_grid.copy()
        
        # Combler les zones vides (0) avec les valeurs de l'autre moitié
        for i in range(min_h):
            for j in range(w):
                bottom_idx = center_h + i
                if bottom_idx < h:
                    if folded_grid[bottom_idx, j] == self.empty_value and top_flipped[i, j] != self.empty_value:
                        folded_grid[bottom_idx, j] = top_flipped[i, j]
                    elif folded_grid[i, j] == self.empty_value and bottom_half[i, j] != self.empty_value:
                        folded_grid[i, j] = bottom_half[i, j]
        
        # Comparer avec l'output
        similarity = self.calculate_similarity(folded_grid, output_grid)
        
        return {
            'similarity': similarity,
            'folded_grid': folded_grid,
            'method': 'horizontal_fold'
        }
    
    def test_vertical_fold(self, input_grid: np.ndarray, output_grid: np.ndarray) -> dict:
        """
        Test le repliement vertical (gauche vers droite)
        """
        h, w = input_grid.shape
        center_w = w // 2
        
        # Replier la partie gauche sur la partie droite
        left_half = input_grid[:, :center_w]
        right_half = input_grid[:, center_w:]
        
        # Retourner la partie gauche pour la superposer
        left_flipped = np.fliplr(left_half)
        
        # Ajuster les tailles si nécessaire
        min_w = min(left_flipped.shape[1], right_half.shape[1])
        left_flipped = left_flipped[:, :min_w]
        right_half = right_half[:, :min_w]
        
        # Créer la grille repliée
        folded_grid = input_grid.copy()
        
        # Combler les zones vides
        for i in range(h):
            for j in range(min_w):
                right_idx = center_w + j
                if right_idx < w:
                    if folded_grid[i, right_idx] == self.empty_value and left_flipped[i, j] != self.empty_value:
                        folded_grid[i, right_idx] = left_flipped[i, j]
                    elif folded_grid[i, j] == self.empty_value and right_half[i, j] != self.empty_value:
                        folded_grid[i, j] = right_half[i, j]
        
        # Comparer avec l'output
        similarity = self.calculate_similarity(folded_grid, output_grid)
        
        return {
            'similarity': similarity,
            'folded_grid': folded_grid,
            'method': 'vertical_fold'
        }
    
    def test_diagonal_fold(self, input_grid: np.ndarray, output_grid: np.ndarray) -> dict:
        """
        Test le repliement diagonal (si grille carrée)
        """
        h, w = input_grid.shape
        
        if h != w:
            return {'similarity': 0.0, 'folded_grid': input_grid, 'method': 'diagonal_fold_not_applicable'}
        
        # Replier selon la diagonale principale
        folded_grid = input_grid.copy()
        
        for i in range(h):
            for j in range(w):
                if folded_grid[i, j] == self.empty_value and input_grid[j, i] != self.empty_value:
                    folded_grid[i, j] = input_grid[j, i]
                elif folded_grid[j, i] == self.empty_value and input_grid[i, j] != self.empty_value:
                    folded_grid[j, i] = input_grid[i, j]
        
        # Comparer avec l'output
        similarity = self.calculate_similarity(folded_grid, output_grid)
        
        return {
            'similarity': similarity,
            'folded_grid': folded_grid,
            'method': 'diagonal_fold'
        }
    
    def calculate_similarity(self, grid1: np.ndarray, grid2: np.ndarray) -> float:
        """
        Calcule la similarité entre deux grilles
        """
        if grid1.shape != grid2.shape:
            return 0.0
        
        total_cells = grid1.size
        matching_cells = np.sum(grid1 == grid2)
        
        return matching_cells / total_cells
    
    def analyze_center(self, grid: np.ndarray) -> dict:
        """
        Analyse la région centrale de la grille
        """
        h, w = grid.shape
        center_h, center_w = h // 2, w // 2
        
        # Région centrale (5x5 autour du centre)
        margin = 2
        start_h = max(0, center_h - margin)
        end_h = min(h, center_h + margin + 1)
        start_w = max(0, center_w - margin)
        end_w = min(w, center_w + margin + 1)
        
        center_region = grid[start_h:end_h, start_w:end_w]
        
        return {
            'center_coords': (center_h, center_w),
            'center_region_coords': (start_h, start_w, end_h, end_w),
            'center_region': center_region.tolist(),
            'center_colors': list(set(center_region.flatten())),
            'center_dominant_color': Counter(center_region.flatten()).most_common(1)[0][0]
        }
    
    def visualize_fold_result(self, input_grid: np.ndarray, output_grid: np.ndarray, fold_result: dict):
        """
        Visualise le résultat d'un test de repliement
        """
        print(f"\n   📊 Test de repliement {fold_result['method']}:")
        print(f"      Similarité avec output: {fold_result['similarity']:.2f}")
        
        if fold_result['similarity'] > 0.8:
            print(f"      ✅ Repliement réussi!")
            
            # Compter les cellules modifiées
            folded = fold_result['folded_grid']
            changes = np.sum(input_grid != folded)
            print(f"      Cellules modifiées par repliement: {changes}")
            
            # Comparer avec l'output
            output_changes = np.sum(folded != output_grid)
            print(f"      Différences avec output: {output_changes}")

def main():
    """
    Fonction principale pour tester la validation par symétrie
    """
    validator = SymmetryMosaicValidator()
    results = validator.analyze_task_3631a71a()
    
    # Affichage détaillé du premier exemple
    if results['examples_analysis']:
        first_example = results['examples_analysis'][0]
        print(f"\n{'='*60}")
        print(f"DÉTAILS DU PREMIER EXEMPLE:")
        print(f"{'='*60}")
        
        print(f"Centre détecté: {first_example['center']}")
        print(f"Blocs uniformes: {len(first_example['uniform_blocks'])}")
        
        for i, block in enumerate(first_example['uniform_blocks'][:5]):  # Top 5
            coords = block['coords']
            print(f"   Bloc {i+1}: Couleur {block['color']}, [{coords[0]},{coords[1]} {coords[2]-1},{coords[3]-1}], Taille {block['size']}")
        
        # Résultats des tests de repliement
        fold_results = first_example['fold_test_results']
        print(f"\nRésultats des tests de repliement:")
        for method, result in fold_results.items():
            print(f"   {method}: {result['similarity']:.2f}")
    
    # Analyse du centre
    center_analysis = results['center_analysis']
    print(f"\n{'='*60}")
    print(f"ANALYSE DU CENTRE:")
    print(f"{'='*60}")
    print(f"Centre de la grille: {center_analysis['center_coords']}")
    print(f"Région centrale: {center_analysis['center_region_coords']}")
    print(f"Couleurs dans le centre: {center_analysis['center_colors']}")
    print(f"Couleur dominante au centre: {center_analysis['center_dominant_color']}")

if __name__ == "__main__":
    main()