#!/usr/bin/env python3
"""
Validation d'échantillons de puzzles MOSAIC détectés
Affiche les grilles pour vérification visuelle
"""

import json
import os
import numpy as np
from typing import Dict, List

def display_grid(grid: np.ndarray, title: str = ""):
    """Affiche une grille de manière lisible"""
    print(f"\n{title}")
    print("-" * len(title))
    
    # Utiliser des caractères pour représenter les couleurs
    color_chars = {
        0: '.',  # Noir (fond)
        1: '█',  # Bleu
        2: '▓',  # Rouge  
        3: '▒',  # Vert
        4: '░',  # J<PERSON>ne
        5: '▄',  # Gris
        6: '▌',  # Magenta
        7: '▐',  # Orange
        8: '▀',  # Cyan
        9: '▬'   # Marron
    }
    
    for row in grid:
        line = ""
        for cell in row:
            line += color_chars.get(cell, str(cell))
        print(line)
    
    print(f"Dimensions: {grid.shape[0]}x{grid.shape[1]}")
    unique_colors = set(grid.flatten())
    if 0 in unique_colors:
        unique_colors.remove(0)
    print(f"Couleurs (hors fond): {len(unique_colors)} - {sorted(unique_colors)}")

def analyze_mosaic_sample(puzzle_id: str, data_dir: str = "../arcdata/training"):
    """Analyse un échantillon de puzzle Mosaic"""
    
    filepath = os.path.join(data_dir, f"{puzzle_id}.json")
    
    try:
        with open(filepath, 'r') as f:
            puzzle_data = json.load(f)
        
        print(f"\n{'='*60}")
        print(f"PUZZLE MOSAIC: {puzzle_id}")
        print(f"{'='*60}")
        
        # Analyser chaque exemple d'entraînement
        for i, example in enumerate(puzzle_data['train']):
            input_grid = np.array(example['input'])
            output_grid = np.array(example['output'])
            
            print(f"\n🔍 EXEMPLE {i+1}")
            
            # Afficher l'input
            display_grid(input_grid, f"INPUT {i+1}")
            
            # Afficher l'output
            display_grid(output_grid, f"OUTPUT {i+1}")
            
            # Analyser la transformation
            input_colors = set(input_grid.flatten())
            output_colors = set(output_grid.flatten())
            if 0 in input_colors: input_colors.remove(0)
            if 0 in output_colors: output_colors.remove(0)
            
            print(f"\nTransformation:")
            print(f"  Input:  {input_grid.shape[0]}x{input_grid.shape[1]} - {len(input_colors)} couleurs")
            print(f"  Output: {output_grid.shape[0]}x{output_grid.shape[1]} - {len(output_colors)} couleurs")
            
            if input_grid.shape != output_grid.shape:
                print(f"  Changement de taille: {output_grid.shape[0]/input_grid.shape[0]:.1f}x")
            
            if input_colors != output_colors:
                new_colors = output_colors - input_colors
                removed_colors = input_colors - output_colors
                if new_colors:
                    print(f"  Nouvelles couleurs: {sorted(new_colors)}")
                if removed_colors:
                    print(f"  Couleurs supprimées: {sorted(removed_colors)}")
        
        # Analyser le test si disponible
        if puzzle_data.get('test'):
            test_input = np.array(puzzle_data['test'][0]['input'])
            print(f"\n🎯 TEST INPUT")
            display_grid(test_input, "TEST INPUT")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur avec {puzzle_id}: {e}")
        return False

def main():
    """Validation d'échantillons de puzzles Mosaic"""
    
    # Charger la liste des puzzles Mosaic détectés
    with open("mosaic_puzzles_simple.json", "r") as f:
        mosaic_data = json.load(f)
    
    puzzle_ids = mosaic_data['puzzle_ids']
    
    print("🎨 VALIDATION D'ÉCHANTILLONS DE PUZZLES MOSAIC")
    print("=" * 60)
    print(f"Total de puzzles Mosaic détectés: {len(puzzle_ids)}")
    
    # Sélectionner quelques échantillons représentatifs
    samples = [
        puzzle_ids[0],   # Le meilleur score
        puzzle_ids[4],   # Un score élevé
        puzzle_ids[10],  # Un score moyen
        puzzle_ids[-1]   # Un score plus faible
    ]
    
    print(f"Échantillons sélectionnés: {samples}")
    
    for puzzle_id in samples:
        success = analyze_mosaic_sample(puzzle_id)
        if success:
            input("Appuyez sur Entrée pour continuer vers le puzzle suivant...")
    
    print(f"\n✅ VALIDATION TERMINÉE")
    print(f"Les puzzles analysés correspondent-ils à votre définition de 'Mosaic' ?")
    print(f"- Grandes dimensions ✓")
    print(f"- Nombreuses couleurs ✓")
    print(f"- Patterns complexes de type mosaïque ?")

if __name__ == "__main__":
    main()