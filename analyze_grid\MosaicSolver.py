import numpy as np
from itertools import combinations
import copy

class MosaicSolver:
    """
    Détecteur et solveur générique de puzzles mosaïque
    """
    
    def __init__(self, empty_value=0, min_block_size=3):
        self.empty_value = empty_value
        self.min_block_size = min_block_size
        self.transformations = {
            'identity': lambda x: x,
            'flip_horizontal': lambda x: np.fliplr(x),
            'flip_vertical': lambda x: np.flipud(x),
            'rotate_90': lambda x: np.rot90(x, 1),
            'rotate_180': lambda x: np.rot90(x, 2),
            'rotate_270': lambda x: np.rot90(x, 3),
            'flip_h_rot_90': lambda x: np.rot90(np.fliplr(x), 1),
            'flip_v_rot_90': lambda x: np.rot90(np.flipud(x), 1)
        }
    
    def analyze_mosaic(self, grid):
        """
        Analyse complète d'une grille pour détecter si c'est une mosaïque
        et proposer les transformations nécessaires
        """
        grid = np.array(grid)
        h, w = grid.shape
        
        results = {
            'is_mosaic': False,
            'confidence': 0.0,
            'source_blocks': [],
            'target_regions': [],
            'proposed_transformations': [],
            'analysis': {}
        }
        
        # 1. Détecter les blocs sources (zones avec motifs)
        source_blocks = self._find_source_blocks(grid)
        results['source_blocks'] = source_blocks
        
        # 2. Détecter les régions cibles (zones vides)
        target_regions = self._find_target_regions(grid)
        results['target_regions'] = target_regions
        
        # 3. Analyser les correspondances possibles
        if len(source_blocks) > 0 and len(target_regions) > 0:
            transformations = self._find_transformations(grid, source_blocks, target_regions)
            results['proposed_transformations'] = transformations
            
            # 4. Calculer la confiance
            confidence = self._calculate_confidence(results, h, w)
            results['confidence'] = confidence
            results['is_mosaic'] = confidence > 0.5
            
            # 5. Ajouter l'analyse détaillée
            results['analysis'] = self._generate_analysis(results)
        
        return results
    
    def _find_source_blocks(self, grid):
        """Trouve tous les blocs rectangulaires contenant des motifs"""
        h, w = grid.shape
        blocks = []
        visited = np.zeros_like(grid, dtype=bool)
        
        # Stratégie : trouver des blocs cohérents de taille significative
        for i in range(h):
            for j in range(w):
                if not visited[i, j] and grid[i, j] != self.empty_value:
                    block_info = self._extract_coherent_block(grid, i, j, visited)
                    if block_info and self._is_significant_block(block_info):
                        blocks.append(block_info)
        
        return blocks
    
    def _extract_coherent_block(self, grid, start_i, start_j, visited):
        """Extrait un bloc cohérent à partir d'une position"""
        h, w = grid.shape
        
        # Méthode adaptative : trouve le plus grand rectangle possible
        best_block = None
        max_area = 0
        
        # Essayer différentes tailles de rectangle
        for end_i in range(start_i + self.min_block_size, h + 1):
            for end_j in range(start_j + self.min_block_size, w + 1):
                if self._is_valid_block_region(grid, start_i, start_j, end_i, end_j, visited):
                    area = (end_i - start_i) * (end_j - start_j)
                    if area > max_area:
                        max_area = area
                        best_block = {
                            'coords': (start_i, start_j, end_i, end_j),
                            'pattern': grid[start_i:end_i, start_j:end_j].copy(),
                            'size': (end_i - start_i, end_j - start_j),
                            'area': area
                        }
        
        if best_block:
            # Marquer comme visité
            r1, c1, r2, c2 = best_block['coords']
            visited[r1:r2, c1:c2] = True
            
        return best_block
    
    def _is_valid_block_region(self, grid, r1, c1, r2, c2, visited):
        """Vérifie si une région peut former un bloc cohérent"""
        # Vérifier que la région n'est pas déjà visitée
        if np.any(visited[r1:r2, c1:c2]):
            return False
        
        # Vérifier qu'il n'y a pas trop de valeurs vides
        region = grid[r1:r2, c1:c2]
        empty_ratio = np.sum(region == self.empty_value) / region.size
        
        return empty_ratio < 0.3  # Moins de 30% de vide
    
    def _is_significant_block(self, block_info):
        """Détermine si un bloc est assez significatif"""
        if not block_info:
            return False
        
        area = block_info['area']
        pattern = block_info['pattern']
        
        # Critères de significativité
        min_area = self.min_block_size * self.min_block_size
        has_variety = len(np.unique(pattern)) > 2
        
        return area >= min_area and has_variety
    
    def _find_target_regions(self, grid):
        """Trouve les régions vides qui pourraient être remplies"""
        h, w = grid.shape
        regions = []
        visited = np.zeros_like(grid, dtype=bool)
        
        for i in range(h):
            for j in range(w):
                if not visited[i, j] and grid[i, j] == self.empty_value:
                    region_info = self._extract_empty_region(grid, i, j, visited)
                    if region_info and self._is_significant_empty_region(region_info):
                        regions.append(region_info)
        
        return regions
    
    def _extract_empty_region(self, grid, start_i, start_j, visited):
        """Extrait une région vide rectangulaire"""
        h, w = grid.shape
        
        # Trouver le plus grand rectangle vide
        best_region = None
        max_area = 0
        
        for end_i in range(start_i + self.min_block_size, h + 1):
            for end_j in range(start_j + self.min_block_size, w + 1):
                if self._is_empty_rectangle(grid, start_i, start_j, end_i, end_j, visited):
                    area = (end_i - start_i) * (end_j - start_j)
                    if area > max_area:
                        max_area = area
                        best_region = {
                            'coords': (start_i, start_j, end_i, end_j),
                            'size': (end_i - start_i, end_j - start_j),
                            'area': area
                        }
        
        if best_region:
            # Marquer comme visité
            r1, c1, r2, c2 = best_region['coords']
            visited[r1:r2, c1:c2] = True
            
        return best_region
    
    def _is_empty_rectangle(self, grid, r1, c1, r2, c2, visited):
        """Vérifie si une région est complètement vide et non visitée"""
        if np.any(visited[r1:r2, c1:c2]):
            return False
        
        region = grid[r1:r2, c1:c2]
        return np.all(region == self.empty_value)
    
    def _is_significant_empty_region(self, region_info):
        """Détermine si une région vide est significative"""
        if not region_info:
            return False
        
        min_area = self.min_block_size * self.min_block_size
        return region_info['area'] >= min_area
    
    def _find_transformations(self, grid, source_blocks, target_regions):
        """Trouve les meilleures transformations pour remplir les régions cibles"""
        transformations = []
        
        for target in target_regions:
            target_size = target['size']
            target_coords = target['coords']
            
            best_match = None
            best_score = 0
            
            # Essayer chaque bloc source avec chaque transformation
            for source in source_blocks:
                for trans_name, trans_func in self.transformations.items():
                    transformed_pattern = trans_func(source['pattern'])
                    
                    # Vérifier si la taille correspond
                    if transformed_pattern.shape == target_size:
                        # Calculer un score de correspondance
                        score = self._calculate_transformation_score(
                            grid, source, target, trans_name, transformed_pattern
                        )
                        
                        if score > best_score:
                            best_score = score
                            best_match = {
                                'source': source,
                                'target': target,
                                'transformation': trans_name,
                                'transformed_pattern': transformed_pattern,
                                'score': score,
                                'operation': self._format_operation(source, target, trans_name)
                            }
            
            if best_match and best_score > 0.5:  # Seuil de confiance
                transformations.append(best_match)
        
        # Trier par score de confiance
        transformations.sort(key=lambda x: x['score'], reverse=True)
        return transformations
    
    def _calculate_transformation_score(self, grid, source, target, trans_name, transformed_pattern):
        """Calcule un score de qualité pour une transformation"""
        score = 0.0
        
        # Score de base pour la correspondance de taille
        if transformed_pattern.shape == target['size']:
            score += 0.3
        
        # Score pour la cohérence avec les motifs environnants
        coherence_score = self._calculate_pattern_coherence(grid, target['coords'], transformed_pattern)
        score += coherence_score * 0.4
        
        # Score pour la logique de symétrie
        symmetry_score = self._calculate_symmetry_logic(grid, source, target, trans_name)
        score += symmetry_score * 0.3
        
        return min(score, 1.0)
    
    def _calculate_pattern_coherence(self, grid, target_coords, pattern):
        """Calcule la cohérence d'un motif avec son environnement"""
        r1, c1, r2, c2 = target_coords
        h, w = grid.shape
        
        coherence = 0.0
        checks = 0
        
        # Vérifier les bordures adjacentes
        # Bordure gauche
        if c1 > 0:
            for i, row_idx in enumerate(range(r1, r2)):
                if row_idx < h:
                    grid_val = grid[row_idx, c1-1]
                    pattern_val = pattern[i, 0]
                    if grid_val != self.empty_value and pattern_val != self.empty_value:
                        if abs(grid_val - pattern_val) < 2:  # Tolérance pour valeurs proches
                            coherence += 1
                        checks += 1
        
        # Bordure droite
        if c2 < w:
            for i, row_idx in enumerate(range(r1, r2)):
                if row_idx < h:
                    grid_val = grid[row_idx, c2]
                    pattern_val = pattern[i, -1]
                    if grid_val != self.empty_value and pattern_val != self.empty_value:
                        if abs(grid_val - pattern_val) < 2:
                            coherence += 1
                        checks += 1
        
        return coherence / max(checks, 1)
    
    def _calculate_symmetry_logic(self, grid, source, target, trans_name):
        """Évalue la logique géométrique de la transformation"""
        source_coords = source['coords']
        target_coords = target['coords']
        
        sr1, sc1, sr2, sc2 = source_coords
        tr1, tc1, tr2, tc2 = target_coords
        
        # Centre de la grille
        h, w = grid.shape
        center_r, center_c = h // 2, w // 2
        
        # Centres des blocs
        source_center_r = (sr1 + sr2) // 2
        source_center_c = (sc1 + sc2) // 2
        target_center_r = (tr1 + tr2) // 2
        target_center_c = (tc1 + tc2) // 2
        
        score = 0.0
        
        # Logique de symétrie par rapport au centre
        if 'flip_horizontal' in trans_name:
            # Pour flip horizontal, les centres doivent être symétriques horizontalement
            if abs((source_center_c + target_center_c) / 2 - center_c) < 3:
                score += 0.5
        
        if 'flip_vertical' in trans_name:
            # Pour flip vertical, les centres doivent être symétriques verticalement
            if abs((source_center_r + target_center_r) / 2 - center_r) < 3:
                score += 0.5
        
        if 'rotate' in trans_name:
            # Pour les rotations, vérifier la distance au centre
            source_dist = np.sqrt((source_center_r - center_r)**2 + (source_center_c - center_c)**2)
            target_dist = np.sqrt((target_center_r - center_r)**2 + (target_center_c - center_c)**2)
            if abs(source_dist - target_dist) < 3:
                score += 0.5
        
        return score
    
    def _format_operation(self, source, target, trans_name):
        """Formate l'opération en format lisible"""
        sr1, sc1, sr2, sc2 = source['coords']
        tr1, tc1, tr2, tc2 = target['coords']
        
        # Conversion des noms de transformation
        trans_map = {
            'flip_horizontal': 'FLIP HORIZONTAL',
            'flip_vertical': 'FLIP VERTICAL',
            'rotate_90': 'ROTATE 90',
            'rotate_180': 'ROTATE 180',
            'rotate_270': 'ROTATE 270',
            'identity': 'COPY',
            'flip_h_rot_90': 'FLIP HORIZONTAL; ROTATE 90',
            'flip_v_rot_90': 'FLIP VERTICAL; ROTATE 90'
        }
        
        operation = f"COPY [{sr1},{sc1} {sr2-1},{sc2-1}]; {trans_map.get(trans_name, trans_name.upper())}; PASTE [{tr1},{tc1}]"
        return operation
    
    def _calculate_confidence(self, results, height, width):
        """Calcule la confiance globale que c'est une mosaïque solvable"""
        score = 0.0
        
        # Nombre de transformations trouvées
        num_transforms = len(results['proposed_transformations'])
        if num_transforms > 0:
            score += min(num_transforms * 0.2, 0.6)
        
        # Qualité moyenne des transformations
        if num_transforms > 0:
            avg_quality = sum(t['score'] for t in results['proposed_transformations']) / num_transforms
            score += avg_quality * 0.3
        
        # Couverture des régions vides
        total_empty_area = sum(r['area'] for r in results['target_regions'])
        covered_area = sum(t['target']['area'] for t in results['proposed_transformations'])
        if total_empty_area > 0:
            coverage = covered_area / total_empty_area
            score += coverage * 0.1
        
        return min(score, 1.0)
    
    def _generate_analysis(self, results):
        """Génère un résumé de l'analyse"""
        analysis = {
            'num_source_blocks': len(results['source_blocks']),
            'num_target_regions': len(results['target_regions']),
            'num_transformations': len(results['proposed_transformations']),
            'coverage_ratio': 0.0,
            'avg_transformation_quality': 0.0
        }
        
        if results['proposed_transformations']:
            qualities = [t['score'] for t in results['proposed_transformations']]
            analysis['avg_transformation_quality'] = sum(qualities) / len(qualities)
        
        total_empty = sum(r['area'] for r in results['target_regions'])
        covered = sum(t['target']['area'] for t in results['proposed_transformations'])
        if total_empty > 0:
            analysis['coverage_ratio'] = covered / total_empty
        
        return analysis
    
    def solve_mosaic(self, grid):
        """Résout complètement une mosaïque en appliquant les transformations"""
        results = self.analyze_mosaic(grid)
        
        if not results['is_mosaic']:
            return None, results
        
        solved_grid = np.array(grid, copy=True)
        
        # Appliquer chaque transformation
        for transform in results['proposed_transformations']:
            target_coords = transform['target']['coords']
            pattern = transform['transformed_pattern']
            
            r1, c1, r2, c2 = target_coords
            solved_grid[r1:r2, c1:c2] = pattern
        
        return solved_grid, results
    
    def print_solution(self, results):
        """Affiche la solution de manière lisible"""
        if not results['is_mosaic']:
            print("❌ Cette grille ne semble pas être une mosaïque solvable")
            return
        
        print(f"✅ Mosaïque détectée (confiance: {results['confidence']:.1%})")
        print(f"📊 Analyse:")
        print(f"   - Blocs sources: {results['analysis']['num_source_blocks']}")
        print(f"   - Régions cibles: {results['analysis']['num_target_regions']}")
        print(f"   - Transformations: {results['analysis']['num_transformations']}")
        print(f"   - Couverture: {results['analysis']['coverage_ratio']:.1%}")
        print(f"   - Qualité moyenne: {results['analysis']['avg_transformation_quality']:.1%}")
        
        print(f"\n🔧 Transformations proposées:")
        for i, transform in enumerate(results['proposed_transformations'], 1):
            print(f"   {i}. {transform['operation']} (score: {transform['score']:.2f})")

# Fonction de test générique
def test_generic_mosaic():
    """Test avec différents types de mosaïques"""
    solver = MosaicSolver()
    
    # Test 1: Mosaïque simple avec symétrie horizontale
    print("=== TEST 1: Symétrie horizontale ===")
    test1 = np.array([
        [1, 2, 3, 0, 0, 0],
        [4, 5, 6, 0, 0, 0],
        [7, 8, 9, 0, 0, 0],
        [0, 0, 0, 0, 0, 0],
        [0, 0, 0, 0, 0, 0],
        [0, 0, 0, 0, 0, 0]
    ])
    
    solved1, results1 = solver.solve_mosaic(test1)
    solver.print_solution(results1)
    
    # Test 2: Mosaïque avec rotation
    print("\n=== TEST 2: Mosaïque avec rotation ===")
    test2 = np.zeros((8, 8))
    test2[0:2, 0:2] = [[1, 2], [3, 4]]  # Coin haut-gauche
    # Laisser d'autres coins vides pour tester les rotations
    
    solved2, results2 = solver.solve_mosaic(test2)
    solver.print_solution(results2)
    
    return solver

if __name__ == "__main__":
    solver = test_generic_mosaic()