#!/usr/bin/env python3
"""
Scan complet de tous les puzzles ARC pour détecter les mosaïques
Utilise detect_mosaic_pattern.py sur l'ensemble du dataset
"""

import os
import json
import numpy as np
from typing import Dict, List, Tuple
from detect_mosaic_pattern import detect_mosaic_pattern

def load_arc_puzzle(puzzle_path: str) -> Dict:
    """Charge un puzzle ARC depuis un fichier JSON"""
    try:
        with open(puzzle_path, 'r') as f:
            return json.load(f)
    except Exception as e:
        print(f"Erreur chargement {puzzle_path}: {e}")
        return None

def analyze_puzzle_examples(puzzle_data: Dict, puzzle_id: str) -> List[Dict]:
    """Analyse tous les exemples d'un puzzle (train + test)"""
    results = []
    
    # Analyser les exemples d'entraînement
    for i, example in enumerate(puzzle_data.get('train', [])):
        input_grid = np.array(example['input'])
        output_grid = np.array(example['output'])
        
        # Analyser l'input
        input_result = detect_mosaic_pattern(input_grid)
        if input_result['is_mosaic']:
            results.append({
                'puzzle_id': puzzle_id,
                'example_type': 'train',
                'example_index': i,
                'grid_type': 'input',
                'grid_shape': input_grid.shape,
                'confidence': input_result['confidence'],
                'evidence': input_result['evidence'],
                'pattern_blocks': len(input_result['pattern_blocks']),
                'empty_regions': len(input_result['empty_regions']),
                'symmetries': len(input_result['symmetries']),
                'grid': input_grid.tolist()
            })
        
        # Analyser l'output
        output_result = detect_mosaic_pattern(output_grid)
        if output_result['is_mosaic']:
            results.append({
                'puzzle_id': puzzle_id,
                'example_type': 'train',
                'example_index': i,
                'grid_type': 'output',
                'grid_shape': output_grid.shape,
                'confidence': output_result['confidence'],
                'evidence': output_result['evidence'],
                'pattern_blocks': len(output_result['pattern_blocks']),
                'empty_regions': len(output_result['empty_regions']),
                'symmetries': len(output_result['symmetries']),
                'grid': output_grid.tolist()
            })
    
    # Analyser les exemples de test
    for i, example in enumerate(puzzle_data.get('test', [])):
        input_grid = np.array(example['input'])
        
        # Analyser l'input de test
        input_result = detect_mosaic_pattern(input_grid)
        if input_result['is_mosaic']:
            results.append({
                'puzzle_id': puzzle_id,
                'example_type': 'test',
                'example_index': i,
                'grid_type': 'input',
                'grid_shape': input_grid.shape,
                'confidence': input_result['confidence'],
                'evidence': input_result['evidence'],
                'pattern_blocks': len(input_result['pattern_blocks']),
                'empty_regions': len(input_result['empty_regions']),
                'symmetries': len(input_result['symmetries']),
                'grid': input_grid.tolist()
            })
        
        # Analyser l'output de test s'il existe
        if 'output' in example:
            output_grid = np.array(example['output'])
            output_result = detect_mosaic_pattern(output_grid)
            if output_result['is_mosaic']:
                results.append({
                    'puzzle_id': puzzle_id,
                    'example_type': 'test',
                    'example_index': i,
                    'grid_type': 'output',
                    'grid_shape': output_grid.shape,
                    'confidence': output_result['confidence'],
                    'evidence': output_result['evidence'],
                    'pattern_blocks': len(output_result['pattern_blocks']),
                    'empty_regions': len(output_result['empty_regions']),
                    'symmetries': len(output_result['symmetries']),
                    'grid': output_grid.tolist()
                })
    
    return results

def scan_arc_directory(arc_dir: str = "../arcdata/training") -> List[Dict]:
    """Scan complet du répertoire ARC"""
    
    print(f"🔍 SCAN COMPLET DES PUZZLES ARC - DÉTECTION MOSAÏQUE")
    print(f"Répertoire: {arc_dir}")
    print("=" * 60)
    
    all_mosaic_results = []
    total_puzzles = 0
    puzzles_with_mosaics = 0
    
    # Parcourir tous les fichiers JSON
    for filename in sorted(os.listdir(arc_dir)):
        if filename.endswith('.json'):
            puzzle_id = filename[:-5]  # Enlever .json
            puzzle_path = os.path.join(arc_dir, filename)
            
            total_puzzles += 1
            
            # Charger et analyser le puzzle
            puzzle_data = load_arc_puzzle(puzzle_path)
            if puzzle_data:
                mosaic_results = analyze_puzzle_examples(puzzle_data, puzzle_id)
                
                if mosaic_results:
                    puzzles_with_mosaics += 1
                    all_mosaic_results.extend(mosaic_results)
                    
                    # Afficher les résultats pour ce puzzle
                    print(f"\n🎨 MOSAÏQUE DÉTECTÉE - {puzzle_id}")
                    print("-" * 40)
                    
                    for result in mosaic_results:
                        grid_info = f"{result['example_type']}{result['example_index']}_{result['grid_type']}"
                        shape_info = f"{result['grid_shape'][0]}x{result['grid_shape'][1]}"
                        confidence_info = f"{result['confidence']:.1%}"
                        
                        print(f"  📋 {grid_info} ({shape_info}) - Confiance: {confidence_info}")
                        print(f"     Blocs: {result['pattern_blocks']}, Vides: {result['empty_regions']}, Symétries: {result['symmetries']}")
                        
                        # Afficher les preuves principales
                        if result['evidence']:
                            main_evidence = ', '.join(result['evidence'][:2])
                            print(f"     Preuves: {main_evidence}")
            
            # Affichage du progrès
            if total_puzzles % 50 == 0:
                print(f"\n📊 Progrès: {total_puzzles} puzzles analysés, {puzzles_with_mosaics} avec mosaïques")
    
    print(f"\n" + "=" * 60)
    print(f"📈 RÉSULTATS FINAUX")
    print(f"Total puzzles analysés: {total_puzzles}")
    print(f"Puzzles avec mosaïques: {puzzles_with_mosaics}")
    print(f"Grilles mosaïques détectées: {len(all_mosaic_results)}")
    print(f"Taux de détection: {puzzles_with_mosaics/total_puzzles*100:.1f}%")
    
    return all_mosaic_results

def analyze_mosaic_statistics(results: List[Dict]):
    """Analyse statistique des mosaïques détectées"""
    
    if not results:
        print("Aucune mosaïque détectée pour l'analyse statistique")
        return
    
    print(f"\n📊 ANALYSE STATISTIQUE DES MOSAÏQUES")
    print("=" * 50)
    
    # Statistiques par type de grille
    input_mosaics = [r for r in results if r['grid_type'] == 'input']
    output_mosaics = [r for r in results if r['grid_type'] == 'output']
    
    print(f"Mosaïques en input: {len(input_mosaics)}")
    print(f"Mosaïques en output: {len(output_mosaics)}")
    
    # Statistiques par type d'exemple
    train_mosaics = [r for r in results if r['example_type'] == 'train']
    test_mosaics = [r for r in results if r['example_type'] == 'test']
    
    print(f"Mosaïques en train: {len(train_mosaics)}")
    print(f"Mosaïques en test: {len(test_mosaics)}")
    
    # Statistiques de confiance
    confidences = [r['confidence'] for r in results]
    if confidences:
        print(f"\nConfiance:")
        print(f"  Moyenne: {np.mean(confidences):.1%}")
        print(f"  Médiane: {np.median(confidences):.1%}")
        print(f"  Min: {min(confidences):.1%}")
        print(f"  Max: {max(confidences):.1%}")
    
    # Statistiques de taille
    shapes = [r['grid_shape'] for r in results]
    areas = [shape[0] * shape[1] for shape in shapes]
    if areas:
        print(f"\nTailles de grilles:")
        print(f"  Aire moyenne: {np.mean(areas):.0f} cellules")
        print(f"  Aire médiane: {np.median(areas):.0f} cellules")
        print(f"  Min: {min(areas)} cellules")
        print(f"  Max: {max(areas)} cellules")
    
    # Top puzzles par confiance
    print(f"\n🏆 TOP 10 MOSAÏQUES PAR CONFIANCE")
    print("-" * 40)
    
    sorted_results = sorted(results, key=lambda x: x['confidence'], reverse=True)
    for i, result in enumerate(sorted_results[:10], 1):
        puzzle_info = f"{result['puzzle_id']}_{result['example_type']}{result['example_index']}_{result['grid_type']}"
        shape_info = f"{result['grid_shape'][0]}x{result['grid_shape'][1]}"
        print(f"{i:2d}. {puzzle_info} ({shape_info}) - {result['confidence']:.1%}")

def save_results_to_file(results: List[Dict], filename: str = "arc_mosaic_scan_results.json"):
    """Sauvegarde les résultats dans un fichier JSON"""
    
    try:
        with open(filename, 'w') as f:
            json.dump(results, f, indent=2)
        print(f"\n💾 Résultats sauvegardés dans: {filename}")
    except Exception as e:
        print(f"Erreur sauvegarde: {e}")

def generate_summary_report(results: List[Dict]):
    """Génère un rapport de synthèse"""
    
    # Grouper par puzzle
    puzzles_summary = {}
    for result in results:
        puzzle_id = result['puzzle_id']
        if puzzle_id not in puzzles_summary:
            puzzles_summary[puzzle_id] = {
                'puzzle_id': puzzle_id,
                'mosaic_grids': [],
                'max_confidence': 0,
                'total_mosaics': 0
            }
        
        puzzles_summary[puzzle_id]['mosaic_grids'].append({
            'type': f"{result['example_type']}{result['example_index']}_{result['grid_type']}",
            'confidence': result['confidence'],
            'shape': result['grid_shape']
        })
        puzzles_summary[puzzle_id]['max_confidence'] = max(
            puzzles_summary[puzzle_id]['max_confidence'], 
            result['confidence']
        )
        puzzles_summary[puzzle_id]['total_mosaics'] += 1
    
    print(f"\n📋 RAPPORT DE SYNTHÈSE PAR PUZZLE")
    print("=" * 50)
    
    # Trier par confiance maximale
    sorted_puzzles = sorted(puzzles_summary.values(), key=lambda x: x['max_confidence'], reverse=True)
    
    for puzzle in sorted_puzzles:
        print(f"\n🎨 {puzzle['puzzle_id']} (max confiance: {puzzle['max_confidence']:.1%})")
        print(f"   Total mosaïques: {puzzle['total_mosaics']}")
        
        for grid in puzzle['mosaic_grids']:
            shape_str = f"{grid['shape'][0]}x{grid['shape'][1]}"
            print(f"   • {grid['type']} ({shape_str}): {grid['confidence']:.1%}")

def main():
    """Point d'entrée principal"""
    
    # Scanner tous les puzzles
    results = scan_arc_directory()
    
    if results:
        # Analyses statistiques
        analyze_mosaic_statistics(results)
        
        # Rapport de synthèse
        generate_summary_report(results)
        
        # Sauvegarder les résultats
        save_results_to_file(results)
    else:
        print("❌ Aucune mosaïque détectée dans le dataset")

if __name__ == "__main__":
    main()