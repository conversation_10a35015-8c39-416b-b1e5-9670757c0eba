#!/usr/bin/env python3
"""
Test spécifique pour la détection MOTIF sur les puzzles connus
"""

import json
from analyze_grid.arc_analyzer import ARCAnalyzer

def test_007bbfb7():
    """Test sur le puzzle 007bbfb7 (tiling 3x3 → 9x9)"""
    print("🧪 Test MOTIF sur 007bbfb7 (tiling)")
    
    # Charger le puzzle
    with open('../arcdata/training/007bbfb7.json', 'r') as f:
        puzzle = json.load(f)
    
    # Analyser
    analyzer = ARCAnalyzer()
    analysis = analyzer.analyze_puzzle(puzzle)
    
    # Vérifier la détection MOTIF
    motif_analysis = analysis['patterns']['motif']
    
    print(f"✅ MOTIF détecté: {motif_analysis['detected']}")
    print(f"✅ Type: {motif_analysis['type']}")
    print(f"✅ Confiance: {motif_analysis['confidence']}")
    
    if motif_analysis['detected']:
        print(f"✅ Facteur tiling: {motif_analysis.get('tiling_factor')}")
        print(f"✅ Méthode extraction: {motif_analysis.get('extraction_method')}")
    
    return motif_analysis

def test_36d67576():
    """Test sur le puzzle 36d67576 (motif détaché)"""
    print("\n🧪 Test MOTIF sur 36d67576 (détaché)")
    
    # Charger le puzzle
    with open('../arcdata/training/36d67576.json', 'r') as f:
        puzzle = json.load(f)
    
    # Analyser
    analyzer = ARCAnalyzer()
    analysis = analyzer.analyze_puzzle(puzzle)
    
    # Vérifier la détection MOTIF
    motif_analysis = analysis['patterns']['motif']
    
    print(f"✅ MOTIF détecté: {motif_analysis['detected']}")
    print(f"✅ Type: {motif_analysis['type']}")
    print(f"✅ Confiance: {motif_analysis['confidence']}")
    
    if motif_analysis['detected']:
        print(f"✅ Couleurs fond: {motif_analysis.get('background_colors')}")
        print(f"✅ Couleurs motif: {motif_analysis.get('motif_colors')}")
        print(f"✅ Méthode extraction: {motif_analysis.get('extraction_method')}")
    
    return motif_analysis

def test_non_motif():
    """Test sur un puzzle sans MOTIF pour éviter faux positifs"""
    print("\n🧪 Test sur puzzle SANS MOTIF (contrôle)")
    
    # Puzzle simple sans motif - transformation simple sans pattern MOTIF
    puzzle_no_motif = {
        "train": [
            {
                "input": [[1, 2, 3], [4, 5, 6], [7, 8, 9]],
                "output": [[9, 8, 7], [6, 5, 4], [3, 2, 1]]  # Simple inversion
            }
        ]
    }
    
    analyzer = ARCAnalyzer()
    analysis = analyzer.analyze_puzzle(puzzle_no_motif)
    
    motif_analysis = analysis['patterns']['motif']
    
    print(f"✅ MOTIF détecté: {motif_analysis['detected']} (doit être False)")
    print(f"✅ Confiance: {motif_analysis['confidence']} (doit être faible)")
    
    return motif_analysis

if __name__ == "__main__":
    print("🔍 TESTS DE DÉTECTION MOTIF")
    print("=" * 50)
    
    # Test des deux types principaux
    result_tiling = test_007bbfb7()
    result_detached = test_36d67576()
    result_control = test_non_motif()
    
    print("\n📊 RÉSUMÉ DES TESTS")
    print("=" * 30)
    print(f"007bbfb7 (tiling): {result_tiling['detected']} - {result_tiling['confidence']}")
    print(f"36d67576 (détaché): {result_detached['detected']} - {result_detached['confidence']}")
    print(f"Contrôle (aucun): {result_control['detected']} - {result_control['confidence']}")
    
    # Validation
    success = (
        result_tiling['detected'] and result_tiling['type'] == 'tiling' and
        result_detached['detected'] and result_detached['type'] == 'detached' and
        not result_control['detected']
    )
    
    if success:
        print("\n🎉 TOUS LES TESTS PASSENT !")
        print("La détection MOTIF fonctionne correctement.")
    else:
        print("\n❌ CERTAINS TESTS ÉCHOUENT")
        print("Ajustements nécessaires dans la détection MOTIF.")