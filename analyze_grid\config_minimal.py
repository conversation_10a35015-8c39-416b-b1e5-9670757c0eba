"""
Configuration minimale pour l'analyse de grilles ARC
"""

class MinimalConfig:
    """Configuration basique pour l'analyse de grilles"""
    
    def __init__(self):
        # Paramètres d'analyse
        self.max_grid_size = 30
        self.min_grid_size = 1
        
        # Seuils pour la classification
        self.expansion_threshold = 2.0
        self.contraction_threshold = 0.5
        self.complexity_threshold = 5.0
        
        # Paramètres de patterns
        self.pattern_replication_threshold = 4.0
        self.major_change_threshold = 0.5
        self.structure_preservation_threshold = 0.1
        
        # Couleurs ARC standard (0-9)
        self.arc_colors = list(range(10))
        
        # Types de transformation reconnus
        self.transformation_types = [
            "same_size",
            "multiply_uniform", 
            "multiply_asymmetric",
            "divide_uniform",
            "divide_asymmetric", 
            "row_modification",
            "column_modification",
            "row_and_column_modification",
            "complex_resize",
            "row_resize",
            "column_resize"
        ]
        
        # Patterns détectables
        self.detectable_patterns = [
            "pattern_replication",
            "systematic_expansion", 
            "fill_pattern",
            "clear_pattern",
            "border_pattern",
            "symmetry_pattern"
        ]
        
        # Commandes AGI de base
        self.agi_commands = [
            "MULTIPLY", "DIVIDE", "RESIZE",
            "FILL", "EDIT", "SURROUND", 
            "COPY", "PASTE", "MOTIF",
            "FLOODFILL"
        ]