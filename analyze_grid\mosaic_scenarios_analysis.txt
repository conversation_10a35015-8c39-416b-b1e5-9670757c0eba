ANALYSE DÉTAILLÉE DES SCÉNARIOS AGI - PUZZLES MOSAIC
============================================================

0dfd9992 - VRAI MOSAIC PROBABLE
--------------------------------------------------
Type de scénario: TRANSFERT_BLOCK
Commande initiale: TRANSFERT
Complexité: 24
Nombre de commandes: 13
Commandes uniques: 3
Contient MOTIF: False
Commandes groupées: False
Indicateurs MOSAIC: clipboard_PASTE, multiple_coordinates
Commandes principales: EDIT, PASTE, INIT

SCÉNARIO INTELLIGENT (entre INIT/TRANSFERT et END):
----------------------------------------
INIT 21x21; EDIT 9 ([0,0] [0,4] [0,9] [0,13] [0,18] [1,6] [1,7] [1,15] [1,16] [3,6] [3,7] [3,15] [3,16] [4,0] [4,4] [4,9] [4,13] [4,18] [6,1] [6,3] [6,10] [6,12] [6,19] [7,1] [7,3] [7,10] [7,12] [7,19] [9,0] [9,9] [9,13] [9,18] [10,15] [10,16] [12,6] [12,7] [12,16] [13,0] [13,4] [13,9] [13,13] [13,18] [15,3] [15,10] [15,12] [15,19] [16,3] [16,10] [16,12] [16,19] [18,0] [18,9] [18,13] [18,18] [19,7] [19,15] [19,16]); EDIT 6 ([0,1] [0,3] [0,10] [0,12] [0,19] [1,0] [1,4] [1,18] [3,0] [3,4] [3,18] [4,1] [4,3] [4,10] [4,12] [4,19] [6,6] [6,7] [6,15] [6,16] [7,6] [7,7] [7,15] [7,16] [9,1] [9,3] [9,10] [9,12] [9,19] [10,0] [10,9] [10,13] [10,18] [12,0] [12,4] [12,9] [12,13] [12,18] [13,1] [13,3] [13,10] [13,12] [13,19] [15,7] [15,16] [16,7] [16,15] [16,16] [18,1] [18,3] [18,10] [18,12] [18,19] [19,0] [19,9] [19,13] [19,18]); EDIT 5 ([0,2] [0,5] [0,8] [0,11] [0,14] [0,17] [0,20] [2,0] [2,4] [2,18] [4,2] [4,5] [4,8] [4,11] [4,14] [4,17] [4,20] [5,0] [5,4] [5,9] [5,13] [5,18] [8,0] [8,9] [8,13] [8,18] [9,2] [9,8] [9,11] [9,14] [9,17] [9,20] [11,0] [11,4] [11,9] [11,13] [11,18] [13,2] [13,5] [13,8] [13,11] [13,17] [13,20] [14,0] [14,4] [14,9] [14,13] [14,18] [17,0] [17,9] [17,13] [17,18] [18,2] [18,8] [18,11] [18,14] [18,17] [18,20] [20,0] [20,4] [20,9] [20,13] [20,18]); EDIT 3 ([0,6] [0,7] [0,15] [0,16] [1,1] [1,3] [1,19] [3,1] [3,3] [3,19] [4,6] [4,7] [4,15] [4,16] [6,0] [6,4] [6,9] [6,13] [6,18] [7,0] [7,4] [7,9] [7,13] [7,18] [9,15] [9,16] [10,1] [10,3] [10,10] [10,12] [10,19] [12,1] [12,3] [12,10] [12,12] [12,19] [13,6] [13,7] [13,16] [15,9] [15,13] [15,18] [16,9] [16,13] [16,18] [18,7] [18,15] [18,16] [19,1] [19,3] [19,10] [19,12] [19,19]); EDIT 2 ([1,2] [1,5] [1,8] [1,14] [1,17] [1,20] [2,1] [2,3] [2,19] [3,2] [3,5] [3,8] [3,14] [3,17] [3,20] [5,1] [5,3] [5,10] [5,12] [5,19] [8,1] [8,3] [8,10] [8,12] [8,19] [10,2] [10,8] [10,11] [10,14] [10,17] [10,20] [11,1] [11,3] [11,10] [11,12] [11,19] [12,2] [12,5] [12,8] [12,11] [12,17] [12,20] [14,1] [14,3] [14,10] [14,12] [14,19] [17,1] [17,3] [17,10] [17,12] [17,19] [19,2] [19,8] [19,11] [19,14] [19,17] [19,20] [20,1] [20,3] [20,10] [20,12] [20,19]); EDIT 1 ([2,2] [2,5] [2,8] [2,14] [2,17] [2,20] [5,2] [5,5] [5,8] [5,11] [5,14] [5,17] [5,20] [8,2] [8,8] [8,11] [8,14] [8,17] [8,20] [11,2] [11,5] [11,8] [11,11] [11,14] [11,17] [11,20] [14,2] [14,5] [14,8] [14,11] [14,17] [14,20] [17,2] [17,8] [17,11] [17,14] [17,17] [17,20] [20,2] [20,5] [20,8] [20,11] [20,14] [20,17] [20,20]); EDIT 8 ([2,6] [2,7] [2,15] [2,16] [5,6] [5,7] [5,15] [5,16] [6,2] [6,5] [6,8] [6,11] [6,14] [6,17] [6,20] [7,2] [7,5] [7,8] [7,11] [7,14] [7,17] [7,20] [8,15] [8,16] [11,6] [11,7] [11,15] [11,16] [14,6] [14,7] [14,16] [15,2] [15,8] [15,11] [15,17] [15,20] [16,2] [16,8] [16,11] [16,14] [16,17] [16,20] [17,7] [17,15] [17,16] [20,6] [20,7] [20,15] [20,16])} MOTIF {COPY [8,13 10,16]; PASTE [8,4]} MOTIF {COPY [3,14 6,15]; PASTE [12,14]} MOTIF {COPY [15,13 19,15]; PASTE [15,4]} MOTIF {COPY [6,0 7,1]; PASTE [15,0]} MOTIF {COPY [10,9 12,13]; PASTE [1,9]

DÉTAIL DES COMMANDES:
------------------------------
 1. INIT 21x21
 2. EDIT 9 ([0,0] [0,4] [0,9] [0,13] [0,18] [1,6] [1,7] [1,15] [1,16] [3,6] [3,7] [3,15] [3,16] [4,0] [4,4] [4,9] [4,13] [4,18] [6,1] [6,3] [6,10] [6,12] [6,19] [7,1] [7,3] [7,10] [7,12] [7,19] [9,0] [9,9] [9,13] [9,18] [10,15] [10,16] [12,6] [12,7] [12,16] [13,0] [13,4] [13,9] [13,13] [13,18] [15,3] [15,10] [15,12] [15,19] [16,3] [16,10] [16,12] [16,19] [18,0] [18,9] [18,13] [18,18] [19,7] [19,15] [19,16])
 3. EDIT 6 ([0,1] [0,3] [0,10] [0,12] [0,19] [1,0] [1,4] [1,18] [3,0] [3,4] [3,18] [4,1] [4,3] [4,10] [4,12] [4,19] [6,6] [6,7] [6,15] [6,16] [7,6] [7,7] [7,15] [7,16] [9,1] [9,3] [9,10] [9,12] [9,19] [10,0] [10,9] [10,13] [10,18] [12,0] [12,4] [12,9] [12,13] [12,18] [13,1] [13,3] [13,10] [13,12] [13,19] [15,7] [15,16] [16,7] [16,15] [16,16] [18,1] [18,3] [18,10] [18,12] [18,19] [19,0] [19,9] [19,13] [19,18])
 4. EDIT 5 ([0,2] [0,5] [0,8] [0,11] [0,14] [0,17] [0,20] [2,0] [2,4] [2,18] [4,2] [4,5] [4,8] [4,11] [4,14] [4,17] [4,20] [5,0] [5,4] [5,9] [5,13] [5,18] [8,0] [8,9] [8,13] [8,18] [9,2] [9,8] [9,11] [9,14] [9,17] [9,20] [11,0] [11,4] [11,9] [11,13] [11,18] [13,2] [13,5] [13,8] [13,11] [13,17] [13,20] [14,0] [14,4] [14,9] [14,13] [14,18] [17,0] [17,9] [17,13] [17,18] [18,2] [18,8] [18,11] [18,14] [18,17] [18,20] [20,0] [20,4] [20,9] [20,13] [20,18])
 5. EDIT 3 ([0,6] [0,7] [0,15] [0,16] [1,1] [1,3] [1,19] [3,1] [3,3] [3,19] [4,6] [4,7] [4,15] [4,16] [6,0] [6,4] [6,9] [6,13] [6,18] [7,0] [7,4] [7,9] [7,13] [7,18] [9,15] [9,16] [10,1] [10,3] [10,10] [10,12] [10,19] [12,1] [12,3] [12,10] [12,12] [12,19] [13,6] [13,7] [13,16] [15,9] [15,13] [15,18] [16,9] [16,13] [16,18] [18,7] [18,15] [18,16] [19,1] [19,3] [19,10] [19,12] [19,19])
 6. EDIT 2 ([1,2] [1,5] [1,8] [1,14] [1,17] [1,20] [2,1] [2,3] [2,19] [3,2] [3,5] [3,8] [3,14] [3,17] [3,20] [5,1] [5,3] [5,10] [5,12] [5,19] [8,1] [8,3] [8,10] [8,12] [8,19] [10,2] [10,8] [10,11] [10,14] [10,17] [10,20] [11,1] [11,3] [11,10] [11,12] [11,19] [12,2] [12,5] [12,8] [12,11] [12,17] [12,20] [14,1] [14,3] [14,10] [14,12] [14,19] [17,1] [17,3] [17,10] [17,12] [17,19] [19,2] [19,8] [19,11] [19,14] [19,17] [19,20] [20,1] [20,3] [20,10] [20,12] [20,19])
 7. EDIT 1 ([2,2] [2,5] [2,8] [2,14] [2,17] [2,20] [5,2] [5,5] [5,8] [5,11] [5,14] [5,17] [5,20] [8,2] [8,8] [8,11] [8,14] [8,17] [8,20] [11,2] [11,5] [11,8] [11,11] [11,14] [11,17] [11,20] [14,2] [14,5] [14,8] [14,11] [14,17] [14,20] [17,2] [17,8] [17,11] [17,14] [17,17] [17,20] [20,2] [20,5] [20,8] [20,11] [20,14] [20,17] [20,20])
 8. EDIT 8 ([2,6] [2,7] [2,15] [2,16] [5,6] [5,7] [5,15] [5,16] [6,2] [6,5] [6,8] [6,11] [6,14] [6,17] [6,20] [7,2] [7,5] [7,8] [7,11] [7,14] [7,17] [7,20] [8,15] [8,16] [11,6] [11,7] [11,15] [11,16] [14,6] [14,7] [14,16] [15,2] [15,8] [15,11] [15,17] [15,20] [16,2] [16,8] [16,11] [16,14] [16,17] [16,20] [17,7] [17,15] [17,16] [20,6] [20,7] [20,15] [20,16])} MOTIF {COPY [8,13 10,16]
 9. PASTE [8,4]} MOTIF {COPY [3,14 6,15]
10. PASTE [12,14]} MOTIF {COPY [15,13 19,15]
11. PASTE [15,4]} MOTIF {COPY [6,0 7,1]
12. PASTE [15,0]} MOTIF {COPY [10,9 12,13]
13. PASTE [1,9]


============================================================

29ec7d0e - VRAI MOSAIC PROBABLE
--------------------------------------------------
Type de scénario: TRANSFERT_BLOCK
Commande initiale: TRANSFERT
Complexité: 24
Nombre de commandes: 13
Commandes uniques: 3
Contient MOTIF: False
Commandes groupées: False
Indicateurs MOSAIC: clipboard_PASTE, multiple_coordinates
Commandes principales: EDIT, PASTE, INIT

SCÉNARIO INTELLIGENT (entre INIT/TRANSFERT et END):
----------------------------------------
INIT 18x18; EDIT 1 ([0,0] [0,1] [0,2] [0,3] [0,4] [0,5] [0,6] [0,7] [0,8] [0,9] [0,10] [0,11] [0,12] [0,16] [0,17] [1,0] [1,9] [2,0] [2,9] [3,0] [3,3] [3,6] [3,9] [3,12] [3,15] [4,0] [4,9] [5,0] [5,9] [6,0] [6,6] [7,0] [8,0] [9,0] [9,1] [9,2] [9,3] [9,4] [9,5] [9,6] [9,7] [9,8] [9,9] [9,10] [9,11] [9,12] [9,13] [9,14] [9,15] [9,16] [9,17] [10,0] [10,9] [11,0] [11,9] [12,0] [12,3] [12,6] [12,9] [12,12] [12,15] [13,0] [13,9] [14,0] [14,9] [15,0] [15,6] [15,9] [15,12] [15,15] [16,0] [16,9] [17,0] [17,9]); EDIT 2 ([1,1] [1,10] [2,5] [4,7] [4,16] [5,2] [5,11] [7,13] [8,8] [8,17] [10,1] [10,10] [11,5] [11,14] [13,7] [13,16] [14,11] [16,4] [16,13] [17,8] [17,17]); EDIT 3 ([1,2] [1,11] [2,1] [2,10] [4,5] [4,14] [7,8] [7,17] [8,7] [8,16] [10,2] [10,11] [11,1] [11,10] [13,5] [13,14] [14,4] [14,13] [16,8] [16,17] [17,7] [17,16]); EDIT 4 ([1,3] [1,12] [2,6] [3,1] [3,4] [3,7] [3,10] [3,13] [3,16] [4,3] [4,12] [5,6] [6,2] [6,8] [6,17] [8,6] [8,15] [10,3] [10,12] [11,6] [11,15] [12,1] [12,4] [12,7] [12,10] [12,13] [12,16] [13,12] [14,6] [14,15] [15,5] [15,8] [15,11] [15,14] [15,17] [16,12] [17,6] [17,15]); EDIT 5 ([1,4] [2,2] [2,11] [4,1] [4,10] [5,8] [5,17] [7,7] [7,16] [8,14] [10,4] [10,13] [11,2] [11,11] [13,10] [14,8] [14,17] [16,7] [16,16] [17,5] [17,14]); EDIT 6 ([1,5] [2,7] [2,16] [4,8] [4,17] [5,1] [5,10] [7,2] [8,13] [10,5] [10,14] [11,7] [11,16] [13,8] [13,17] [14,10] [16,11] [17,4] [17,13]); EDIT 7 ([1,6] [2,3] [2,12] [3,2] [3,5] [3,8] [3,11] [3,14] [3,17] [4,6] [4,15] [5,12] [6,1] [6,7] [6,16] [7,6] [7,15] [10,6] [10,15] [11,3] [11,12] [12,2] [12,5] [12,8] [12,11] [12,14] [12,17] [13,6] [13,15] [14,12] [15,4] [15,7] [15,10] [15,13] [15,16] [16,6] [16,15] [17,3] [17,12]); EDIT 8 ([1,7] [1,16] [2,8] [2,17] [4,4] [4,13] [7,1] [8,2] [10,7] [10,16] [11,8] [11,17] [13,4] [13,13] [14,5] [14,14] [16,10] [17,2] [17,11]); EDIT 9 ([1,8] [1,17] [2,4] [4,2] [4,11] [5,7] [5,16] [7,14] [8,1] [10,8] [10,17] [11,4] [11,13] [13,11] [14,7] [14,16] [16,5] [16,14] [17,1] [17,10])} MOTIF {COPY [9,9 17,17]; PASTE [0,9]} MOTIF {COPY [0,9 8,17]; PASTE [0,0]} MOTIF {COPY [0,0 8,8]; PASTE [9,0]

DÉTAIL DES COMMANDES:
------------------------------
 1. INIT 18x18
 2. EDIT 1 ([0,0] [0,1] [0,2] [0,3] [0,4] [0,5] [0,6] [0,7] [0,8] [0,9] [0,10] [0,11] [0,12] [0,16] [0,17] [1,0] [1,9] [2,0] [2,9] [3,0] [3,3] [3,6] [3,9] [3,12] [3,15] [4,0] [4,9] [5,0] [5,9] [6,0] [6,6] [7,0] [8,0] [9,0] [9,1] [9,2] [9,3] [9,4] [9,5] [9,6] [9,7] [9,8] [9,9] [9,10] [9,11] [9,12] [9,13] [9,14] [9,15] [9,16] [9,17] [10,0] [10,9] [11,0] [11,9] [12,0] [12,3] [12,6] [12,9] [12,12] [12,15] [13,0] [13,9] [14,0] [14,9] [15,0] [15,6] [15,9] [15,12] [15,15] [16,0] [16,9] [17,0] [17,9])
 3. EDIT 2 ([1,1] [1,10] [2,5] [4,7] [4,16] [5,2] [5,11] [7,13] [8,8] [8,17] [10,1] [10,10] [11,5] [11,14] [13,7] [13,16] [14,11] [16,4] [16,13] [17,8] [17,17])
 4. EDIT 3 ([1,2] [1,11] [2,1] [2,10] [4,5] [4,14] [7,8] [7,17] [8,7] [8,16] [10,2] [10,11] [11,1] [11,10] [13,5] [13,14] [14,4] [14,13] [16,8] [16,17] [17,7] [17,16])
 5. EDIT 4 ([1,3] [1,12] [2,6] [3,1] [3,4] [3,7] [3,10] [3,13] [3,16] [4,3] [4,12] [5,6] [6,2] [6,8] [6,17] [8,6] [8,15] [10,3] [10,12] [11,6] [11,15] [12,1] [12,4] [12,7] [12,10] [12,13] [12,16] [13,12] [14,6] [14,15] [15,5] [15,8] [15,11] [15,14] [15,17] [16,12] [17,6] [17,15])
 6. EDIT 5 ([1,4] [2,2] [2,11] [4,1] [4,10] [5,8] [5,17] [7,7] [7,16] [8,14] [10,4] [10,13] [11,2] [11,11] [13,10] [14,8] [14,17] [16,7] [16,16] [17,5] [17,14])
 7. EDIT 6 ([1,5] [2,7] [2,16] [4,8] [4,17] [5,1] [5,10] [7,2] [8,13] [10,5] [10,14] [11,7] [11,16] [13,8] [13,17] [14,10] [16,11] [17,4] [17,13])
 8. EDIT 7 ([1,6] [2,3] [2,12] [3,2] [3,5] [3,8] [3,11] [3,14] [3,17] [4,6] [4,15] [5,12] [6,1] [6,7] [6,16] [7,6] [7,15] [10,6] [10,15] [11,3] [11,12] [12,2] [12,5] [12,8] [12,11] [12,14] [12,17] [13,6] [13,15] [14,12] [15,4] [15,7] [15,10] [15,13] [15,16] [16,6] [16,15] [17,3] [17,12])
 9. EDIT 8 ([1,7] [1,16] [2,8] [2,17] [4,4] [4,13] [7,1] [8,2] [10,7] [10,16] [11,8] [11,17] [13,4] [13,13] [14,5] [14,14] [16,10] [17,2] [17,11])
10. EDIT 9 ([1,8] [1,17] [2,4] [4,2] [4,11] [5,7] [5,16] [7,14] [8,1] [10,8] [10,17] [11,4] [11,13] [13,11] [14,7] [14,16] [16,5] [16,14] [17,1] [17,10])} MOTIF {COPY [9,9 17,17]
11. PASTE [0,9]} MOTIF {COPY [0,9 8,17]
12. PASTE [0,0]} MOTIF {COPY [0,0 8,8]
13. PASTE [9,0]


============================================================

484b58aa - VRAI MOSAIC PROBABLE
--------------------------------------------------
Type de scénario: TRANSFERT_BLOCK
Commande initiale: TRANSFERT
Complexité: 23
Nombre de commandes: 12
Commandes uniques: 3
Contient MOTIF: False
Commandes groupées: False
Indicateurs MOSAIC: clipboard_PASTE, multiple_coordinates
Commandes principales: EDIT, PASTE, INIT

SCÉNARIO INTELLIGENT (entre INIT/TRANSFERT et END):
----------------------------------------
INIT 29x29; EDIT 8 ([0,0] [0,18] [1,1] [1,10] [1,19] [1,28] [2,3] [2,10] [2,12] [2,21] [2,28] [3,24] [4,25] [5,0] [5,9] [5,18] [5,27] [6,12] [7,4] [7,13] [7,22] [8,4] [8,6] [8,15] [8,22] [8,24] [9,0] [9,18] [10,1] [10,10] [10,19] [10,28] [11,3] [11,10] [11,12] [11,21] [11,28] [12,6] [12,24] [13,7] [13,16] [13,25] [14,0] [14,9] [14,16] [14,18] [14,27] [15,12] [16,13] [16,22] [17,6] [17,15] [17,22] [17,24] [18,0] [18,18] [19,1] [19,10] [19,19] [19,28] [20,3] [20,10] [20,12] [20,21] [20,28] [21,6] [21,24] [22,7] [22,16] [22,25] [23,0] [23,9] [23,16] [23,18] [23,27] [24,12] [25,4] [25,13] [25,22] [26,4] [26,6] [26,15] [26,22] [26,24] [27,0] [27,18] [28,1] [28,10] [28,19] [28,28]); EDIT 1 ([0,1] [0,4] [0,10] [0,13] [0,16] [0,19] [0,22] [0,25] [0,28] [1,0] [1,3] [1,12] [1,18] [1,21] [1,24] [1,27] [2,2] [2,5] [2,11] [2,17] [2,20] [2,23] [2,26] [3,1] [3,4] [3,10] [3,13] [3,19] [3,22] [3,25] [3,28] [4,0] [4,3] [4,12] [4,18] [4,21] [4,24] [4,27] [5,2] [5,5] [5,8] [5,11] [5,17] [5,20] [5,23] [5,26] [6,1] [6,4] [6,7] [6,10] [6,13] [6,19] [6,22] [6,25] [6,28] [7,0] [7,3] [7,6] [7,9] [7,12] [7,18] [7,21] [7,24] [7,27] [8,2] [8,5] [8,8] [8,11] [8,14] [8,17] [8,20] [8,23] [8,26] [9,1] [9,4] [9,7] [9,10] [9,13] [9,16] [9,19] [9,22] [9,25] [9,28] [10,0] [10,3] [10,6] [10,9] [10,12] [10,15] [10,18] [10,21] [10,24] [10,27] [11,2] [11,5] [11,8] [11,11] [11,14] [11,17] [11,20] [11,23] [11,26] [12,1] [12,4] [12,7] [12,10] [12,16] [12,19] [12,22] [12,25] [12,28] [13,0] [13,6] [13,9] [13,12] [13,18] [13,21] [13,24] [13,27] [14,5] [14,8] [14,11] [14,14] [14,17] [14,20] [14,23] [14,26] [15,1] [15,7] [15,10] [15,13] [15,16] [15,19] [15,22] [15,25] [15,28] [16,0] [16,6] [16,9] [16,12] [16,15] [16,18] [16,21] [16,24] [16,27] [17,2] [17,5] [17,8] [17,11] [17,14] [17,17] [17,20] [17,23] [17,26] [18,1] [18,7] [18,10] [18,13] [18,16] [18,19] [18,22] [18,25] [18,28] [19,0] [19,3] [19,6] [19,9] [19,12] [19,15] [19,18] [19,21] [19,24] [19,27] [20,2] [20,5] [20,8] [20,11] [20,14] [20,17] [20,20] [20,23] [20,26] [21,1] [21,4] [21,7] [21,10] [21,13] [21,16] [21,19] [21,22] [21,25] [21,28] [22,0] [22,3] [22,6] [22,9] [22,12] [22,15] [22,18] [22,21] [22,24] [22,27] [23,2] [23,5] [23,8] [23,11] [23,14] [23,17] [23,20] [23,23] [23,26] [24,1] [24,4] [24,7] [24,10] [24,13] [24,16] [24,19] [24,22] [24,25] [24,28] [25,0] [25,3] [25,6] [25,9] [25,12] [25,15] [25,18] [25,21] [25,24] [25,27] [26,2] [26,5] [26,8] [26,11] [26,14] [26,17] [26,20] [26,23] [26,26] [27,1] [27,4] [27,7] [27,10] [27,13] [27,16] [27,19] [27,22] [27,25] [27,28] [28,0] [28,3] [28,6] [28,9] [28,12] [28,15] [28,18] [28,21] [28,24] [28,27]); EDIT 2 ([0,2] [0,5] [0,11] [0,14] [0,17] [0,20] [0,23] [0,24] [0,26] [1,2] [1,20] [1,25] [1,26] [2,4] [2,22] [2,24] [3,2] [3,5] [3,11] [3,12] [3,17] [3,20] [3,23] [3,26] [4,2] [4,4] [4,13] [4,20] [4,22] [4,26] [5,3] [5,10] [5,12] [5,21] [5,28] [6,0] [6,2] [6,5] [6,8] [6,11] [6,17] [6,18] [6,20] [6,23] [6,26] [7,1] [7,2] [7,8] [7,10] [7,19] [7,20] [7,26] [7,28] [8,0] [8,9] [8,16] [8,18] [8,27] [9,2] [9,5] [9,6] [9,8] [9,11] [9,14] [9,17] [9,20] [9,23] [9,24] [9,26] [10,2] [10,7] [10,8] [10,14] [10,16] [10,20] [10,25] [10,26] [11,4] [11,6] [11,15] [11,22] [11,24] [12,5] [12,8] [12,11] [12,12] [12,17] [12,20] [12,23] [12,26] [13,4] [13,8] [13,20] [13,22] [13,26] [14,10] [14,12] [14,21] [14,28] [15,0] [15,5] [15,8] [15,11] [15,14] [15,17] [15,18] [15,20] [15,23] [15,26] [16,1] [16,8] [16,10] [16,14] [16,19] [16,20] [16,26] [16,28] [17,0] [17,9] [17,16] [17,18] [17,27] [18,2] [18,5] [18,6] [18,8] [18,11] [18,14] [18,17] [18,20] [18,23] [18,24] [18,26] [19,2] [19,7] [19,8] [19,14] [19,16] [19,20] [19,25] [19,26] [20,4] [20,6] [20,15] [20,22] [20,24] [21,2] [21,5] [21,8] [21,11] [21,12] [21,14] [21,17] [21,20] [21,23] [21,26] [22,2] [22,4] [22,8] [22,13] [22,14] [22,20] [22,22] [22,26] [23,3] [23,10] [23,12] [23,21] [23,28] [24,0] [24,2] [24,5] [24,8] [24,11] [24,14] [24,17] [24,18] [24,20] [24,23] [24,26] [25,1] [25,2] [25,8] [25,10] [25,14] [25,19] [25,20] [25,26] [25,28] [26,0] [26,9] [26,16] [26,18] [26,27] [27,2] [27,5] [27,6] [27,8] [27,11] [27,14] [27,17] [27,20] [27,23] [27,24] [27,26] [28,2] [28,7] [28,8] [28,14] [28,16] [28,20] [28,25] [28,26]); EDIT 6 ([0,3] [0,21] [2,25] [3,27] [5,13] [8,1] [8,19] [9,3] [9,21] [11,7] [11,25] [12,9] [12,27] [14,13] [15,15] [17,1] [17,19] [18,21] [20,7] [20,25] [21,9] [21,27] [23,13] [24,15] [26,1] [26,19] [27,3] [27,21]); EDIT 5 ([0,12] [1,4] [1,13] [1,22] [2,0] [2,18] [2,27] [3,0] [3,18] [4,1] [4,10] [4,19] [4,28] [5,4] [5,6] [5,22] [5,24] [6,6] [6,24] [7,7] [7,25] [8,3] [8,10] [8,12] [8,21] [8,28] [9,12] [10,4] [10,13] [10,22] [11,0] [11,9] [11,16] [11,18] [11,27] [12,0] [12,18] [13,1] [13,10] [13,19] [13,28] [14,4] [14,6] [14,15] [14,22] [14,24] [15,6] [15,24] [16,7] [16,16] [16,25] [17,10] [17,12] [17,21] [17,28] [18,12] [19,4] [19,13] [19,22] [20,0] [20,9] [20,16] [20,18] [20,27] [21,0] [21,18] [22,1] [22,10] [22,19] [22,28] [23,4] [23,6] [23,15] [23,22] [23,24] [24,6] [24,24] [25,7] [25,16] [25,25] [26,3] [26,10] [26,12] [26,21] [26,28] [27,12] [28,4] [28,13] [28,22]); EDIT 9 ([0,15] [1,5] [1,11] [1,17] [1,23] [2,13] [3,3] [3,21] [4,5] [4,11] [4,17] [4,23] [5,1] [5,19] [6,9] [6,27] [7,5] [7,11] [7,17] [7,23] [8,7] [8,25] [9,15] [10,5] [10,11] [10,17] [10,23] [11,13] [12,21] [13,5] [13,11] [13,17] [13,23] [14,1] [14,19] [15,9] [15,27] [16,5] [16,11] [16,17] [16,23] [17,7] [17,25] [18,15] [19,5] [19,11] [19,17] [19,23] [20,13] [21,3] [21,21] [22,5] [22,11] [22,17] [22,23] [23,1] [23,19] [24,9] [24,27] [25,5] [25,11] [25,17] [25,23] [26,7] [26,25] [27,15] [28,5] [28,11] [28,17] [28,23]); EDIT 3 ([0,27] [2,1] [2,19] [5,7] [5,25] [6,3] [6,21] [8,13] [9,9] [9,27] [11,1] [11,19] [14,7] [14,25] [15,21] [17,13] [18,9] [18,27] [20,1] [20,19] [21,15] [23,7] [23,25] [24,3] [24,21] [26,13] [27,9] [27,27])} MOTIF {COPY [21,13 22,15]; PASTE [12,13]} MOTIF {COPY [12,20 18,22]; PASTE [12,2]} MOTIF {COPY [10,14 16,16]; PASTE [1,14]} MOTIF {COPY [9,6 13,9]; PASTE [0,6]

DÉTAIL DES COMMANDES:
------------------------------
 1. INIT 29x29
 2. EDIT 8 ([0,0] [0,18] [1,1] [1,10] [1,19] [1,28] [2,3] [2,10] [2,12] [2,21] [2,28] [3,24] [4,25] [5,0] [5,9] [5,18] [5,27] [6,12] [7,4] [7,13] [7,22] [8,4] [8,6] [8,15] [8,22] [8,24] [9,0] [9,18] [10,1] [10,10] [10,19] [10,28] [11,3] [11,10] [11,12] [11,21] [11,28] [12,6] [12,24] [13,7] [13,16] [13,25] [14,0] [14,9] [14,16] [14,18] [14,27] [15,12] [16,13] [16,22] [17,6] [17,15] [17,22] [17,24] [18,0] [18,18] [19,1] [19,10] [19,19] [19,28] [20,3] [20,10] [20,12] [20,21] [20,28] [21,6] [21,24] [22,7] [22,16] [22,25] [23,0] [23,9] [23,16] [23,18] [23,27] [24,12] [25,4] [25,13] [25,22] [26,4] [26,6] [26,15] [26,22] [26,24] [27,0] [27,18] [28,1] [28,10] [28,19] [28,28])
 3. EDIT 1 ([0,1] [0,4] [0,10] [0,13] [0,16] [0,19] [0,22] [0,25] [0,28] [1,0] [1,3] [1,12] [1,18] [1,21] [1,24] [1,27] [2,2] [2,5] [2,11] [2,17] [2,20] [2,23] [2,26] [3,1] [3,4] [3,10] [3,13] [3,19] [3,22] [3,25] [3,28] [4,0] [4,3] [4,12] [4,18] [4,21] [4,24] [4,27] [5,2] [5,5] [5,8] [5,11] [5,17] [5,20] [5,23] [5,26] [6,1] [6,4] [6,7] [6,10] [6,13] [6,19] [6,22] [6,25] [6,28] [7,0] [7,3] [7,6] [7,9] [7,12] [7,18] [7,21] [7,24] [7,27] [8,2] [8,5] [8,8] [8,11] [8,14] [8,17] [8,20] [8,23] [8,26] [9,1] [9,4] [9,7] [9,10] [9,13] [9,16] [9,19] [9,22] [9,25] [9,28] [10,0] [10,3] [10,6] [10,9] [10,12] [10,15] [10,18] [10,21] [10,24] [10,27] [11,2] [11,5] [11,8] [11,11] [11,14] [11,17] [11,20] [11,23] [11,26] [12,1] [12,4] [12,7] [12,10] [12,16] [12,19] [12,22] [12,25] [12,28] [13,0] [13,6] [13,9] [13,12] [13,18] [13,21] [13,24] [13,27] [14,5] [14,8] [14,11] [14,14] [14,17] [14,20] [14,23] [14,26] [15,1] [15,7] [15,10] [15,13] [15,16] [15,19] [15,22] [15,25] [15,28] [16,0] [16,6] [16,9] [16,12] [16,15] [16,18] [16,21] [16,24] [16,27] [17,2] [17,5] [17,8] [17,11] [17,14] [17,17] [17,20] [17,23] [17,26] [18,1] [18,7] [18,10] [18,13] [18,16] [18,19] [18,22] [18,25] [18,28] [19,0] [19,3] [19,6] [19,9] [19,12] [19,15] [19,18] [19,21] [19,24] [19,27] [20,2] [20,5] [20,8] [20,11] [20,14] [20,17] [20,20] [20,23] [20,26] [21,1] [21,4] [21,7] [21,10] [21,13] [21,16] [21,19] [21,22] [21,25] [21,28] [22,0] [22,3] [22,6] [22,9] [22,12] [22,15] [22,18] [22,21] [22,24] [22,27] [23,2] [23,5] [23,8] [23,11] [23,14] [23,17] [23,20] [23,23] [23,26] [24,1] [24,4] [24,7] [24,10] [24,13] [24,16] [24,19] [24,22] [24,25] [24,28] [25,0] [25,3] [25,6] [25,9] [25,12] [25,15] [25,18] [25,21] [25,24] [25,27] [26,2] [26,5] [26,8] [26,11] [26,14] [26,17] [26,20] [26,23] [26,26] [27,1] [27,4] [27,7] [27,10] [27,13] [27,16] [27,19] [27,22] [27,25] [27,28] [28,0] [28,3] [28,6] [28,9] [28,12] [28,15] [28,18] [28,21] [28,24] [28,27])
 4. EDIT 2 ([0,2] [0,5] [0,11] [0,14] [0,17] [0,20] [0,23] [0,24] [0,26] [1,2] [1,20] [1,25] [1,26] [2,4] [2,22] [2,24] [3,2] [3,5] [3,11] [3,12] [3,17] [3,20] [3,23] [3,26] [4,2] [4,4] [4,13] [4,20] [4,22] [4,26] [5,3] [5,10] [5,12] [5,21] [5,28] [6,0] [6,2] [6,5] [6,8] [6,11] [6,17] [6,18] [6,20] [6,23] [6,26] [7,1] [7,2] [7,8] [7,10] [7,19] [7,20] [7,26] [7,28] [8,0] [8,9] [8,16] [8,18] [8,27] [9,2] [9,5] [9,6] [9,8] [9,11] [9,14] [9,17] [9,20] [9,23] [9,24] [9,26] [10,2] [10,7] [10,8] [10,14] [10,16] [10,20] [10,25] [10,26] [11,4] [11,6] [11,15] [11,22] [11,24] [12,5] [12,8] [12,11] [12,12] [12,17] [12,20] [12,23] [12,26] [13,4] [13,8] [13,20] [13,22] [13,26] [14,10] [14,12] [14,21] [14,28] [15,0] [15,5] [15,8] [15,11] [15,14] [15,17] [15,18] [15,20] [15,23] [15,26] [16,1] [16,8] [16,10] [16,14] [16,19] [16,20] [16,26] [16,28] [17,0] [17,9] [17,16] [17,18] [17,27] [18,2] [18,5] [18,6] [18,8] [18,11] [18,14] [18,17] [18,20] [18,23] [18,24] [18,26] [19,2] [19,7] [19,8] [19,14] [19,16] [19,20] [19,25] [19,26] [20,4] [20,6] [20,15] [20,22] [20,24] [21,2] [21,5] [21,8] [21,11] [21,12] [21,14] [21,17] [21,20] [21,23] [21,26] [22,2] [22,4] [22,8] [22,13] [22,14] [22,20] [22,22] [22,26] [23,3] [23,10] [23,12] [23,21] [23,28] [24,0] [24,2] [24,5] [24,8] [24,11] [24,14] [24,17] [24,18] [24,20] [24,23] [24,26] [25,1] [25,2] [25,8] [25,10] [25,14] [25,19] [25,20] [25,26] [25,28] [26,0] [26,9] [26,16] [26,18] [26,27] [27,2] [27,5] [27,6] [27,8] [27,11] [27,14] [27,17] [27,20] [27,23] [27,24] [27,26] [28,2] [28,7] [28,8] [28,14] [28,16] [28,20] [28,25] [28,26])
 5. EDIT 6 ([0,3] [0,21] [2,25] [3,27] [5,13] [8,1] [8,19] [9,3] [9,21] [11,7] [11,25] [12,9] [12,27] [14,13] [15,15] [17,1] [17,19] [18,21] [20,7] [20,25] [21,9] [21,27] [23,13] [24,15] [26,1] [26,19] [27,3] [27,21])
 6. EDIT 5 ([0,12] [1,4] [1,13] [1,22] [2,0] [2,18] [2,27] [3,0] [3,18] [4,1] [4,10] [4,19] [4,28] [5,4] [5,6] [5,22] [5,24] [6,6] [6,24] [7,7] [7,25] [8,3] [8,10] [8,12] [8,21] [8,28] [9,12] [10,4] [10,13] [10,22] [11,0] [11,9] [11,16] [11,18] [11,27] [12,0] [12,18] [13,1] [13,10] [13,19] [13,28] [14,4] [14,6] [14,15] [14,22] [14,24] [15,6] [15,24] [16,7] [16,16] [16,25] [17,10] [17,12] [17,21] [17,28] [18,12] [19,4] [19,13] [19,22] [20,0] [20,9] [20,16] [20,18] [20,27] [21,0] [21,18] [22,1] [22,10] [22,19] [22,28] [23,4] [23,6] [23,15] [23,22] [23,24] [24,6] [24,24] [25,7] [25,16] [25,25] [26,3] [26,10] [26,12] [26,21] [26,28] [27,12] [28,4] [28,13] [28,22])
 7. EDIT 9 ([0,15] [1,5] [1,11] [1,17] [1,23] [2,13] [3,3] [3,21] [4,5] [4,11] [4,17] [4,23] [5,1] [5,19] [6,9] [6,27] [7,5] [7,11] [7,17] [7,23] [8,7] [8,25] [9,15] [10,5] [10,11] [10,17] [10,23] [11,13] [12,21] [13,5] [13,11] [13,17] [13,23] [14,1] [14,19] [15,9] [15,27] [16,5] [16,11] [16,17] [16,23] [17,7] [17,25] [18,15] [19,5] [19,11] [19,17] [19,23] [20,13] [21,3] [21,21] [22,5] [22,11] [22,17] [22,23] [23,1] [23,19] [24,9] [24,27] [25,5] [25,11] [25,17] [25,23] [26,7] [26,25] [27,15] [28,5] [28,11] [28,17] [28,23])
 8. EDIT 3 ([0,27] [2,1] [2,19] [5,7] [5,25] [6,3] [6,21] [8,13] [9,9] [9,27] [11,1] [11,19] [14,7] [14,25] [15,21] [17,13] [18,9] [18,27] [20,1] [20,19] [21,15] [23,7] [23,25] [24,3] [24,21] [26,13] [27,9] [27,27])} MOTIF {COPY [21,13 22,15]
 9. PASTE [12,13]} MOTIF {COPY [12,20 18,22]
10. PASTE [12,2]} MOTIF {COPY [10,14 16,16]
11. PASTE [1,14]} MOTIF {COPY [9,6 13,9]
12. PASTE [0,6]


============================================================

73251a56 - VRAI MOSAIC PROBABLE
--------------------------------------------------
Type de scénario: TRANSFERT_BLOCK
Commande initiale: TRANSFERT
Complexité: 26
Nombre de commandes: 15
Commandes uniques: 3
Contient MOTIF: False
Commandes groupées: False
Indicateurs MOSAIC: multiple_coordinates
Commandes principales: EDIT, FLOODFILL, INIT

SCÉNARIO INTELLIGENT (entre INIT/TRANSFERT et END):
----------------------------------------
INIT 21x21; EDIT 4 ([0,0] [0,2] [0,3] [0,20] [1,1] [1,4] [1,5] [1,6] [2,0] [2,2] [2,6] [2,7] [2,8] [2,9] [3,0] [3,3] [3,8] [3,9] [3,12] [4,1] [4,4] [4,12] [4,13] [4,14] [4,15] [5,1] [5,5] [5,12] [5,13] [5,14] [5,15] [5,16] [5,17] [5,18] [6,1] [6,2] [6,6] [6,14] [6,15] [6,16] [6,17] [6,18] [6,19] [6,20] [7,2] [7,7] [7,16] [7,17] [7,18] [7,19] [7,20] [8,8] [8,18] [8,19] [8,20] [9,9] [9,20] [10,3] [10,4] [10,10] [11,3] [11,4] [11,11] [12,3] [12,4] [12,5] [12,12] [13,4] [13,5] [13,13] [14,4] [14,5] [14,6] [14,14] [17,17] [18,5] [18,6] [18,7] [18,8] [18,18] [19,6] [19,7] [19,8] [19,19] [20,0] [20,6] [20,7] [20,8] [20,9] [20,20]); EDIT 3 ([0,1] [0,18] [0,19] [1,0] [1,2] [1,3] [2,1] [2,3] [2,4] [2,5] [3,1] [3,2] [3,4] [3,5] [3,6] [3,7] [4,2] [4,3] [4,5] [4,6] [4,7] [4,8] [4,9] [5,2] [5,3] [5,4] [5,6] [5,7] [5,8] [5,9] [6,3] [6,4] [6,5] [6,7] [6,8] [6,9] [6,10] [6,11] [6,12] [6,13] [7,3] [7,4] [7,5] [7,6] [7,8] [7,9] [7,10] [7,11] [7,12] [7,13] [7,14] [7,15] [8,5] [8,6] [8,7] [8,9] [8,10] [8,11] [8,12] [8,13] [8,14] [8,15] [8,16] [8,17] [9,5] [9,6] [9,7] [9,8] [9,10] [9,11] [9,12] [9,13] [9,14] [9,15] [9,16] [9,17] [9,18] [9,19] [10,5] [10,6] [10,7] [10,8] [10,9] [10,11] [10,12] [10,13] [10,14] [10,15] [10,16] [10,17] [10,18] [10,19] [10,20] [11,5] [11,6] [11,7] [11,8] [11,9] [11,10] [11,12] [11,13] [11,14] [11,15] [11,16] [11,17] [11,18] [11,19] [11,20] [12,6] [12,7] [12,8] [12,9] [12,10] [12,11] [12,13] [12,14] [12,15] [12,16] [12,17] [12,18] [12,19] [12,20] [13,6] [13,7] [13,8] [13,9] [13,10] [13,11] [13,12] [13,14] [13,15] [13,16] [13,17] [13,18] [13,19] [13,20] [14,7] [14,8] [14,9] [14,10] [14,11] [14,12] [14,13] [14,15] [14,16] [14,17] [14,18] [14,19] [14,20] [15,9] [15,10] [15,11] [15,12] [15,13] [15,14] [15,20] [16,9] [16,10] [16,11] [16,12] [16,13] [16,14] [16,20] [17,9] [17,10] [17,11] [17,12] [17,13] [17,14] [17,15] [17,16] [17,18] [17,19] [17,20] [18,0] [18,9] [18,10] [18,11] [18,17] [18,19] [18,20] [19,0] [19,9] [19,10] [19,11] [19,17] [19,18] [19,20] [20,10] [20,11] [20,12] [20,13] [20,14] [20,15] [20,16] [20,17] [20,18] [20,19]); EDIT 5 ([0,4] [0,5] [1,7] [1,8] [1,9] [2,12] [2,13] [3,13] [3,14] [3,15] [3,16] [3,17] [4,0] [4,16] [4,17] [4,18] [4,19] [4,20] [5,0] [5,19] [5,20] [7,1] [10,2] [11,2] [12,2] [13,2] [13,3] [14,3] [15,3] [16,3] [17,3] [18,4] [19,4] [19,5] [20,4] [20,5]); EDIT 6 ([0,6] [0,7] [1,12] [2,14] [2,15] [2,16] [2,17] [3,18] [3,19] [3,20] [6,0] [7,0] [10,1] [11,1] [12,1] [14,2] [15,2] [16,2] [17,2] [18,3] [19,3] [20,3]); EDIT 7 ([0,8] [0,9] [1,13] [1,14] [1,15] [2,18] [2,19] [2,20] [13,1] [14,1] [15,1] [18,2] [19,2] [20,2]); EDIT 8 ([0,10] [0,11] [1,16] [1,17] [1,18] [10,0] [11,0] [16,1] [17,1] [18,1]); EDIT 9 ([0,12] [0,13] [1,19] [1,20] [12,0] [13,0] [19,1] [20,1]); EDIT 1 ([0,14] [0,15] [14,0] [15,0]); EDIT 2 ([0,16] [0,17] [16,0] [17,0])} EDITS {EDIT 3 ([9,2] [8,4] [9,4] [5,10] [5,11] [15,7] [15,8] [16,8] [17,8]); EDIT 4 ([8,3] [9,3] [9,2] [13,3] [3,10] [3,11] [4,11] [4,10] [15,15] [16,16] [8,2]); EDIT 5 ([8,1] [9,1] [13,3] [16,4] [17,4] [2,11] [2,10]); EDIT 6 ([1,11] [1,10]); EDIT 7 ([9,0] [8,0])} FLOODFILLS {FLOODFILL 3 ([19,14] [15,18] [16,15]); FLOODFILL 4 [16,7]

DÉTAIL DES COMMANDES:
------------------------------
 1. INIT 21x21
 2. EDIT 4 ([0,0] [0,2] [0,3] [0,20] [1,1] [1,4] [1,5] [1,6] [2,0] [2,2] [2,6] [2,7] [2,8] [2,9] [3,0] [3,3] [3,8] [3,9] [3,12] [4,1] [4,4] [4,12] [4,13] [4,14] [4,15] [5,1] [5,5] [5,12] [5,13] [5,14] [5,15] [5,16] [5,17] [5,18] [6,1] [6,2] [6,6] [6,14] [6,15] [6,16] [6,17] [6,18] [6,19] [6,20] [7,2] [7,7] [7,16] [7,17] [7,18] [7,19] [7,20] [8,8] [8,18] [8,19] [8,20] [9,9] [9,20] [10,3] [10,4] [10,10] [11,3] [11,4] [11,11] [12,3] [12,4] [12,5] [12,12] [13,4] [13,5] [13,13] [14,4] [14,5] [14,6] [14,14] [17,17] [18,5] [18,6] [18,7] [18,8] [18,18] [19,6] [19,7] [19,8] [19,19] [20,0] [20,6] [20,7] [20,8] [20,9] [20,20])
 3. EDIT 3 ([0,1] [0,18] [0,19] [1,0] [1,2] [1,3] [2,1] [2,3] [2,4] [2,5] [3,1] [3,2] [3,4] [3,5] [3,6] [3,7] [4,2] [4,3] [4,5] [4,6] [4,7] [4,8] [4,9] [5,2] [5,3] [5,4] [5,6] [5,7] [5,8] [5,9] [6,3] [6,4] [6,5] [6,7] [6,8] [6,9] [6,10] [6,11] [6,12] [6,13] [7,3] [7,4] [7,5] [7,6] [7,8] [7,9] [7,10] [7,11] [7,12] [7,13] [7,14] [7,15] [8,5] [8,6] [8,7] [8,9] [8,10] [8,11] [8,12] [8,13] [8,14] [8,15] [8,16] [8,17] [9,5] [9,6] [9,7] [9,8] [9,10] [9,11] [9,12] [9,13] [9,14] [9,15] [9,16] [9,17] [9,18] [9,19] [10,5] [10,6] [10,7] [10,8] [10,9] [10,11] [10,12] [10,13] [10,14] [10,15] [10,16] [10,17] [10,18] [10,19] [10,20] [11,5] [11,6] [11,7] [11,8] [11,9] [11,10] [11,12] [11,13] [11,14] [11,15] [11,16] [11,17] [11,18] [11,19] [11,20] [12,6] [12,7] [12,8] [12,9] [12,10] [12,11] [12,13] [12,14] [12,15] [12,16] [12,17] [12,18] [12,19] [12,20] [13,6] [13,7] [13,8] [13,9] [13,10] [13,11] [13,12] [13,14] [13,15] [13,16] [13,17] [13,18] [13,19] [13,20] [14,7] [14,8] [14,9] [14,10] [14,11] [14,12] [14,13] [14,15] [14,16] [14,17] [14,18] [14,19] [14,20] [15,9] [15,10] [15,11] [15,12] [15,13] [15,14] [15,20] [16,9] [16,10] [16,11] [16,12] [16,13] [16,14] [16,20] [17,9] [17,10] [17,11] [17,12] [17,13] [17,14] [17,15] [17,16] [17,18] [17,19] [17,20] [18,0] [18,9] [18,10] [18,11] [18,17] [18,19] [18,20] [19,0] [19,9] [19,10] [19,11] [19,17] [19,18] [19,20] [20,10] [20,11] [20,12] [20,13] [20,14] [20,15] [20,16] [20,17] [20,18] [20,19])
 4. EDIT 5 ([0,4] [0,5] [1,7] [1,8] [1,9] [2,12] [2,13] [3,13] [3,14] [3,15] [3,16] [3,17] [4,0] [4,16] [4,17] [4,18] [4,19] [4,20] [5,0] [5,19] [5,20] [7,1] [10,2] [11,2] [12,2] [13,2] [13,3] [14,3] [15,3] [16,3] [17,3] [18,4] [19,4] [19,5] [20,4] [20,5])
 5. EDIT 6 ([0,6] [0,7] [1,12] [2,14] [2,15] [2,16] [2,17] [3,18] [3,19] [3,20] [6,0] [7,0] [10,1] [11,1] [12,1] [14,2] [15,2] [16,2] [17,2] [18,3] [19,3] [20,3])
 6. EDIT 7 ([0,8] [0,9] [1,13] [1,14] [1,15] [2,18] [2,19] [2,20] [13,1] [14,1] [15,1] [18,2] [19,2] [20,2])
 7. EDIT 8 ([0,10] [0,11] [1,16] [1,17] [1,18] [10,0] [11,0] [16,1] [17,1] [18,1])
 8. EDIT 9 ([0,12] [0,13] [1,19] [1,20] [12,0] [13,0] [19,1] [20,1])
 9. EDIT 1 ([0,14] [0,15] [14,0] [15,0])
10. EDIT 2 ([0,16] [0,17] [16,0] [17,0])} EDITS {EDIT 3 ([9,2] [8,4] [9,4] [5,10] [5,11] [15,7] [15,8] [16,8] [17,8])
11. EDIT 4 ([8,3] [9,3] [9,2] [13,3] [3,10] [3,11] [4,11] [4,10] [15,15] [16,16] [8,2])
12. EDIT 5 ([8,1] [9,1] [13,3] [16,4] [17,4] [2,11] [2,10])
13. EDIT 6 ([1,11] [1,10])
14. EDIT 7 ([9,0] [8,0])} FLOODFILLS {FLOODFILL 3 ([19,14] [15,18] [16,15])
15. FLOODFILL 4 [16,7]


============================================================

780d0b14 - FAUX POSITIF SUSPECTÉ
--------------------------------------------------
Type de scénario: INIT
Commande initiale: INIT 3x3
Complexité: 25
Nombre de commandes: 6
Commandes uniques: 2
Contient MOTIF: False
Commandes groupées: True
Indicateurs MOSAIC: multiple_coordinates, grouped_EDITS
Commandes principales: EDIT, EDITS

SCÉNARIO INTELLIGENT (entre INIT/TRANSFERT et END):
----------------------------------------
EDITS {EDIT 3 ([0,0] [2,1]); EDIT 2 ([0,1] [2,2]); EDIT 4 [0,2]; EDIT 1 ([1,0] [1,1]); EDIT 8 [1,2]; EDIT 7 [2,0]}

DÉTAIL DES COMMANDES:
------------------------------
 1. EDITS {EDIT 3 ([0,0] [2,1])
 2. EDIT 2 ([0,1] [2,2])
 3. EDIT 4 [0,2]
 4. EDIT 1 ([1,0] [1,1])
 5. EDIT 8 [1,2]
 6. EDIT 7 [2,0]}


============================================================

85c4e7cd - FAUX POSITIF SUSPECTÉ
--------------------------------------------------
Type de scénario: TRANSFERT_BLOCK
Commande initiale: TRANSFERT
Complexité: 24
Nombre de commandes: 13
Commandes uniques: 3
Contient MOTIF: False
Commandes groupées: False
Indicateurs MOSAIC: multiple_coordinates
Commandes principales: EDIT, FLOODFILL, INIT

SCÉNARIO INTELLIGENT (entre INIT/TRANSFERT et END):
----------------------------------------
INIT 14x14; EDIT 8 ([0,0] [0,1] [0,2] [0,3] [0,4] [0,5] [0,6] [0,7] [0,8] [0,9] [0,10] [0,11] [0,12] [0,13] [1,0] [1,13] [2,0] [2,13] [3,0] [3,13] [4,0] [4,13] [5,0] [5,13] [6,0] [6,13] [7,0] [7,13] [8,0] [8,13] [9,0] [9,13] [10,0] [10,13] [11,0] [11,13] [12,0] [12,13] [13,0] [13,1] [13,2] [13,3] [13,4] [13,5] [13,6] [13,7] [13,8] [13,9] [13,10] [13,11] [13,12] [13,13]); EDIT 2 ([1,1] [1,2] [1,3] [1,4] [1,5] [1,6] [1,7] [1,8] [1,9] [1,10] [1,11] [1,12] [2,1] [2,12] [3,1] [3,12] [4,1] [4,12] [5,1] [5,12] [6,1] [6,12] [7,1] [7,12] [8,1] [8,12] [9,1] [9,12] [10,1] [10,12] [11,1] [11,12] [12,1] [12,2] [12,3] [12,4] [12,5] [12,6] [12,7] [12,8] [12,9] [12,10] [12,11] [12,12]); EDIT 4 ([2,2] [2,3] [2,4] [2,5] [2,6] [2,7] [2,8] [2,9] [2,10] [2,11] [3,2] [3,11] [4,2] [4,11] [5,2] [5,11] [6,2] [6,11] [7,2] [7,11] [8,2] [8,11] [9,2] [9,11] [10,2] [10,11] [11,2] [11,3] [11,4] [11,5] [11,6] [11,7] [11,8] [11,9] [11,10] [11,11]); EDIT 3 ([3,3] [3,4] [3,5] [3,6] [3,7] [3,8] [3,9] [3,10] [4,3] [4,10] [5,3] [5,10] [6,3] [6,10] [7,3] [7,10] [8,3] [8,10] [9,3] [9,10] [10,3] [10,4] [10,5] [10,6] [10,7] [10,8] [10,9] [10,10]); EDIT 7 ([4,4] [4,5] [4,6] [4,7] [4,8] [4,9] [5,4] [5,9] [6,4] [6,9] [7,4] [7,9] [8,4] [8,9] [9,4] [9,5] [9,6] [9,7] [9,8] [9,9]); EDIT 6 ([5,5] [5,6] [5,7] [5,8] [6,5] [6,8] [7,5] [7,8] [8,5] [8,6] [8,7] [8,8]); EDIT 5 ([6,6] [6,7] [7,6] [7,7])} FLOODFILLS {FLOODFILL 8 [6,6]; FLOODFILL 2 [5,7]; FLOODFILL 4 [4,7]; FLOODFILL 5 [0,9]; FLOODFILL 6 [1,8]; FLOODFILL 7 [2,8]

DÉTAIL DES COMMANDES:
------------------------------
 1. INIT 14x14
 2. EDIT 8 ([0,0] [0,1] [0,2] [0,3] [0,4] [0,5] [0,6] [0,7] [0,8] [0,9] [0,10] [0,11] [0,12] [0,13] [1,0] [1,13] [2,0] [2,13] [3,0] [3,13] [4,0] [4,13] [5,0] [5,13] [6,0] [6,13] [7,0] [7,13] [8,0] [8,13] [9,0] [9,13] [10,0] [10,13] [11,0] [11,13] [12,0] [12,13] [13,0] [13,1] [13,2] [13,3] [13,4] [13,5] [13,6] [13,7] [13,8] [13,9] [13,10] [13,11] [13,12] [13,13])
 3. EDIT 2 ([1,1] [1,2] [1,3] [1,4] [1,5] [1,6] [1,7] [1,8] [1,9] [1,10] [1,11] [1,12] [2,1] [2,12] [3,1] [3,12] [4,1] [4,12] [5,1] [5,12] [6,1] [6,12] [7,1] [7,12] [8,1] [8,12] [9,1] [9,12] [10,1] [10,12] [11,1] [11,12] [12,1] [12,2] [12,3] [12,4] [12,5] [12,6] [12,7] [12,8] [12,9] [12,10] [12,11] [12,12])
 4. EDIT 4 ([2,2] [2,3] [2,4] [2,5] [2,6] [2,7] [2,8] [2,9] [2,10] [2,11] [3,2] [3,11] [4,2] [4,11] [5,2] [5,11] [6,2] [6,11] [7,2] [7,11] [8,2] [8,11] [9,2] [9,11] [10,2] [10,11] [11,2] [11,3] [11,4] [11,5] [11,6] [11,7] [11,8] [11,9] [11,10] [11,11])
 5. EDIT 3 ([3,3] [3,4] [3,5] [3,6] [3,7] [3,8] [3,9] [3,10] [4,3] [4,10] [5,3] [5,10] [6,3] [6,10] [7,3] [7,10] [8,3] [8,10] [9,3] [9,10] [10,3] [10,4] [10,5] [10,6] [10,7] [10,8] [10,9] [10,10])
 6. EDIT 7 ([4,4] [4,5] [4,6] [4,7] [4,8] [4,9] [5,4] [5,9] [6,4] [6,9] [7,4] [7,9] [8,4] [8,9] [9,4] [9,5] [9,6] [9,7] [9,8] [9,9])
 7. EDIT 6 ([5,5] [5,6] [5,7] [5,8] [6,5] [6,8] [7,5] [7,8] [8,5] [8,6] [8,7] [8,8])
 8. EDIT 5 ([6,6] [6,7] [7,6] [7,7])} FLOODFILLS {FLOODFILL 8 [6,6]
 9. FLOODFILL 2 [5,7]
10. FLOODFILL 4 [4,7]
11. FLOODFILL 5 [0,9]
12. FLOODFILL 6 [1,8]
13. FLOODFILL 7 [2,8]


============================================================

8731374e - VRAI MOSAIC PROBABLE
--------------------------------------------------
Type de scénario: TRANSFERT_SIMPLE
Commande initiale: TRANSFERT
Complexité: 19
Nombre de commandes: 10
Commandes uniques: 2
Contient MOTIF: False
Commandes groupées: False
Indicateurs MOSAIC: multiple_coordinates
Commandes principales: EDIT, {INIT

SCÉNARIO INTELLIGENT (entre INIT/TRANSFERT et END):
----------------------------------------
{INIT 17x19; EDIT 2 ([0,0] [0,2] [0,4] [1,11] [1,16] [2,15] [4,1] [5,1] [7,2] [7,15] [10,12] [12,10] [12,14] [13,13] [13,16] [14,1] [14,2] [16,0] [16,2] [16,5] [16,15] [17,4] [17,9] [18,1] [18,12]); EDIT 7 ([0,1] [1,3] [1,5] [2,1] [2,11] [3,11] [3,12] [5,14] [6,12] [6,14] [6,16] [8,15] [9,1] [9,2] [9,12] [11,1] [11,2] [11,13] [11,14] [12,3] [12,13] [13,0] [13,10] [13,11] [15,15] [15,16] [16,9] [16,11] [17,0] [18,3] [18,5]); EDIT 6 ([0,5] [2,12] [3,0] [3,16] [5,2] [5,13] [7,1] [7,11] [8,11] [8,14] [8,16] [9,0] [12,0] [12,1] [12,4] [12,11] [13,6] [13,9] [14,12] [14,15] [15,0] [15,3] [15,6] [15,7] [15,9] [17,8] [18,7]); EDIT 3 ([0,6] [0,8] [0,11] [0,13] [1,15] [3,2] [3,15] [5,12] [6,15] [7,12] [10,2] [10,14] [11,0] [13,1] [13,14] [14,6] [15,4] [15,12] [16,13] [17,7] [17,14]); EDIT 9 ([0,9] [1,6] [1,9] [2,2] [3,1] [4,2] [4,11] [4,14] [5,15] [7,16] [8,0] [12,9] [13,8] [13,12] [14,0] [14,7] [14,8] [14,9] [14,11] [14,16] [15,2] [16,4] [16,12] [17,2] [17,16] [18,11] [18,14]); EDIT 1 ([0,10] [1,7] [2,13] [2,16] [3,14] [4,9] [4,13] [4,16] [6,13] [7,5] [7,13] [7,14] [9,13] [11,15] [12,6] [12,8] [13,3] [13,5] [14,13] [15,1] [15,5] [15,11] [16,1] [16,6] [16,8] [16,16] [17,1] [18,0] [18,6] [18,15]); EDIT 5 ([0,12] [0,16] [1,10] [1,14] [2,14] [4,12] [5,0] [5,16] [9,14] [10,16] [13,2] [14,3] [14,10] [16,7] [17,13] [17,15] [18,2] [18,8] [18,16]); EDIT 4 ([0,15] [1,0] [1,1] [1,8] [4,15] [6,1] [6,2] [8,1] [8,12] [9,11] [9,16] [10,0] [10,11] [10,13] [10,15] [11,11] [12,2] [13,4] [13,7] [14,4] [14,14] [15,13] [16,3] [17,3] [17,6]); EDIT 8 ([1,2] [1,13] [2,0] [2,3] [2,4] [2,5] [2,6] [2,7] [2,8] [2,9] [2,10] [3,3] [3,4] [3,5] [3,6] [3,7] [3,8] [3,9] [3,10] [3,13] [4,3] [4,4] [4,5] [4,6] [4,7] [4,8] [4,10] [5,3] [5,4] [5,5] [5,6] [5,7] [5,8] [5,9] [5,10] [5,11] [6,0] [6,3] [6,4] [6,5] [6,6] [6,7] [6,8] [6,9] [6,10] [6,11] [7,0] [7,3] [7,4] [7,6] [7,7] [7,8] [7,9] [7,10] [8,3] [8,4] [8,5] [8,6] [8,7] [8,8] [8,9] [8,10] [9,3] [9,4] [9,5] [9,6] [9,7] [9,8] [9,9] [9,10] [9,15] [10,3] [10,4] [10,5] [10,6] [10,7] [10,8] [10,9] [10,10] [11,3] [11,4] [11,5] [11,6] [11,7] [11,8] [11,9] [11,10] [11,12] [11,16] [12,5] [12,7] [12,12] [12,15] [12,16] [14,5] [15,8] [15,14] [16,14] [17,5] [17,10] [17,12] [18,4] [18,9] [18,13])} EXTRACT [2,3 11,10] FILL 1 ([2,0 2,7] [5,0 5,7] [0,6 9,6] [0,2 9,2])

DÉTAIL DES COMMANDES:
------------------------------
 1. {INIT 17x19
 2. EDIT 2 ([0,0] [0,2] [0,4] [1,11] [1,16] [2,15] [4,1] [5,1] [7,2] [7,15] [10,12] [12,10] [12,14] [13,13] [13,16] [14,1] [14,2] [16,0] [16,2] [16,5] [16,15] [17,4] [17,9] [18,1] [18,12])
 3. EDIT 7 ([0,1] [1,3] [1,5] [2,1] [2,11] [3,11] [3,12] [5,14] [6,12] [6,14] [6,16] [8,15] [9,1] [9,2] [9,12] [11,1] [11,2] [11,13] [11,14] [12,3] [12,13] [13,0] [13,10] [13,11] [15,15] [15,16] [16,9] [16,11] [17,0] [18,3] [18,5])
 4. EDIT 6 ([0,5] [2,12] [3,0] [3,16] [5,2] [5,13] [7,1] [7,11] [8,11] [8,14] [8,16] [9,0] [12,0] [12,1] [12,4] [12,11] [13,6] [13,9] [14,12] [14,15] [15,0] [15,3] [15,6] [15,7] [15,9] [17,8] [18,7])
 5. EDIT 3 ([0,6] [0,8] [0,11] [0,13] [1,15] [3,2] [3,15] [5,12] [6,15] [7,12] [10,2] [10,14] [11,0] [13,1] [13,14] [14,6] [15,4] [15,12] [16,13] [17,7] [17,14])
 6. EDIT 9 ([0,9] [1,6] [1,9] [2,2] [3,1] [4,2] [4,11] [4,14] [5,15] [7,16] [8,0] [12,9] [13,8] [13,12] [14,0] [14,7] [14,8] [14,9] [14,11] [14,16] [15,2] [16,4] [16,12] [17,2] [17,16] [18,11] [18,14])
 7. EDIT 1 ([0,10] [1,7] [2,13] [2,16] [3,14] [4,9] [4,13] [4,16] [6,13] [7,5] [7,13] [7,14] [9,13] [11,15] [12,6] [12,8] [13,3] [13,5] [14,13] [15,1] [15,5] [15,11] [16,1] [16,6] [16,8] [16,16] [17,1] [18,0] [18,6] [18,15])
 8. EDIT 5 ([0,12] [0,16] [1,10] [1,14] [2,14] [4,12] [5,0] [5,16] [9,14] [10,16] [13,2] [14,3] [14,10] [16,7] [17,13] [17,15] [18,2] [18,8] [18,16])
 9. EDIT 4 ([0,15] [1,0] [1,1] [1,8] [4,15] [6,1] [6,2] [8,1] [8,12] [9,11] [9,16] [10,0] [10,11] [10,13] [10,15] [11,11] [12,2] [13,4] [13,7] [14,4] [14,14] [15,13] [16,3] [17,3] [17,6])
10. EDIT 8 ([1,2] [1,13] [2,0] [2,3] [2,4] [2,5] [2,6] [2,7] [2,8] [2,9] [2,10] [3,3] [3,4] [3,5] [3,6] [3,7] [3,8] [3,9] [3,10] [3,13] [4,3] [4,4] [4,5] [4,6] [4,7] [4,8] [4,10] [5,3] [5,4] [5,5] [5,6] [5,7] [5,8] [5,9] [5,10] [5,11] [6,0] [6,3] [6,4] [6,5] [6,6] [6,7] [6,8] [6,9] [6,10] [6,11] [7,0] [7,3] [7,4] [7,6] [7,7] [7,8] [7,9] [7,10] [8,3] [8,4] [8,5] [8,6] [8,7] [8,8] [8,9] [8,10] [9,3] [9,4] [9,5] [9,6] [9,7] [9,8] [9,9] [9,10] [9,15] [10,3] [10,4] [10,5] [10,6] [10,7] [10,8] [10,9] [10,10] [11,3] [11,4] [11,5] [11,6] [11,7] [11,8] [11,9] [11,10] [11,12] [11,16] [12,5] [12,7] [12,12] [12,15] [12,16] [14,5] [15,8] [15,14] [16,14] [17,5] [17,10] [17,12] [18,4] [18,9] [18,13])} EXTRACT [2,3 11,10] FILL 1 ([2,0 2,7] [5,0 5,7] [0,6 9,6] [0,2 9,2])


============================================================

9ecd008a - VRAI MOSAIC PROBABLE
--------------------------------------------------
Type de scénario: TRANSFERT_BLOCK
Commande initiale: TRANSFERT
Complexité: 24
Nombre de commandes: 11
Commandes uniques: 4
Contient MOTIF: False
Commandes groupées: False
Indicateurs MOSAIC: clipboard_PASTE, multiple_coordinates, transform_FLIP
Commandes principales: EDIT, FLIP, PASTE, INIT

SCÉNARIO INTELLIGENT (entre INIT/TRANSFERT et END):
----------------------------------------
INIT 16x16; EDIT 4 ([0,0] [0,15] [4,5] [4,10] [5,4] [5,5] [5,10] [5,11] [10,4] [10,5] [10,10] [10,11] [11,5] [11,10] [15,0] [15,15]); EDIT 8 ([0,1] [0,14] [1,0] [1,15] [14,0] [14,15] [15,1] [15,14]); EDIT 9 ([0,2] [0,3] [0,12] [0,13] [1,2] [1,3] [1,12] [1,13] [2,0] [2,1] [2,14] [2,15] [3,0] [3,1] [3,7] [3,8] [3,14] [3,15] [6,6] [6,9] [7,7] [7,8] [7,12] [8,3] [8,7] [8,8] [8,12] [9,6] [9,9] [12,0] [12,1] [12,7] [12,8] [12,14] [12,15] [13,0] [13,1] [13,14] [13,15] [14,2] [14,3] [14,12] [14,13] [15,2] [15,3] [15,12] [15,13]); EDIT 6 ([0,4] [0,5] [0,10] [0,11] [1,1] [1,4] [1,11] [1,14] [4,0] [4,1] [4,14] [4,15] [5,0] [5,15] [10,0] [10,15] [11,0] [11,1] [11,14] [11,15] [14,1] [14,4] [14,11] [14,14] [15,4] [15,5] [15,10] [15,11]); EDIT 5 ([0,6] [0,9] [1,7] [1,8] [2,2] [2,4] [2,6] [2,7] [2,8] [2,9] [2,11] [2,13] [3,5] [3,6] [3,9] [3,10] [4,2] [4,6] [4,9] [4,13] [5,12] [6,0] [6,4] [6,7] [6,8] [6,11] [6,12] [6,13] [6,15] [7,6] [7,9] [7,13] [7,14] [8,1] [8,2] [8,6] [8,9] [8,13] [8,14] [9,0] [9,2] [9,3] [9,4] [9,7] [9,8] [9,11] [9,12] [9,13] [9,15] [10,3] [10,12] [11,2] [11,6] [11,9] [11,13] [12,5] [12,6] [12,9] [12,10] [13,2] [13,4] [13,6] [13,7] [13,8] [13,9] [13,11] [13,13] [14,7] [14,8] [15,6] [15,9]); EDIT 1 ([0,7] [0,8] [1,6] [1,9] [2,5] [2,10] [3,4] [3,11] [4,3] [4,4] [4,11] [4,12] [5,13] [6,14] [7,0] [7,15] [8,0] [8,15] [9,1] [9,14] [10,2] [10,13] [11,3] [11,4] [11,11] [11,12] [12,4] [12,11] [13,5] [13,10] [14,6] [14,9] [15,7] [15,8]); EDIT 7 ([1,5] [1,10] [5,7] [5,8] [5,14] [7,5] [7,10] [8,5] [8,10] [10,1] [10,7] [10,8] [10,14] [14,5] [14,10]); EDIT 2 ([2,3] [2,12] [3,2] [3,3] [3,12] [3,13] [4,7] [4,8] [5,6] [5,9] [6,5] [6,10] [7,4] [7,11] [8,4] [8,11] [9,5] [9,10] [10,6] [10,9] [11,7] [11,8] [12,2] [12,3] [12,12] [12,13] [13,3] [13,12])} EXTRACT [5,12 7,14] MOTIF {CUT [0,0 2,2]; FLIP HORIZONTAL [0,0 2,2]; PASTE [0,0]

DÉTAIL DES COMMANDES:
------------------------------
 1. INIT 16x16
 2. EDIT 4 ([0,0] [0,15] [4,5] [4,10] [5,4] [5,5] [5,10] [5,11] [10,4] [10,5] [10,10] [10,11] [11,5] [11,10] [15,0] [15,15])
 3. EDIT 8 ([0,1] [0,14] [1,0] [1,15] [14,0] [14,15] [15,1] [15,14])
 4. EDIT 9 ([0,2] [0,3] [0,12] [0,13] [1,2] [1,3] [1,12] [1,13] [2,0] [2,1] [2,14] [2,15] [3,0] [3,1] [3,7] [3,8] [3,14] [3,15] [6,6] [6,9] [7,7] [7,8] [7,12] [8,3] [8,7] [8,8] [8,12] [9,6] [9,9] [12,0] [12,1] [12,7] [12,8] [12,14] [12,15] [13,0] [13,1] [13,14] [13,15] [14,2] [14,3] [14,12] [14,13] [15,2] [15,3] [15,12] [15,13])
 5. EDIT 6 ([0,4] [0,5] [0,10] [0,11] [1,1] [1,4] [1,11] [1,14] [4,0] [4,1] [4,14] [4,15] [5,0] [5,15] [10,0] [10,15] [11,0] [11,1] [11,14] [11,15] [14,1] [14,4] [14,11] [14,14] [15,4] [15,5] [15,10] [15,11])
 6. EDIT 5 ([0,6] [0,9] [1,7] [1,8] [2,2] [2,4] [2,6] [2,7] [2,8] [2,9] [2,11] [2,13] [3,5] [3,6] [3,9] [3,10] [4,2] [4,6] [4,9] [4,13] [5,12] [6,0] [6,4] [6,7] [6,8] [6,11] [6,12] [6,13] [6,15] [7,6] [7,9] [7,13] [7,14] [8,1] [8,2] [8,6] [8,9] [8,13] [8,14] [9,0] [9,2] [9,3] [9,4] [9,7] [9,8] [9,11] [9,12] [9,13] [9,15] [10,3] [10,12] [11,2] [11,6] [11,9] [11,13] [12,5] [12,6] [12,9] [12,10] [13,2] [13,4] [13,6] [13,7] [13,8] [13,9] [13,11] [13,13] [14,7] [14,8] [15,6] [15,9])
 7. EDIT 1 ([0,7] [0,8] [1,6] [1,9] [2,5] [2,10] [3,4] [3,11] [4,3] [4,4] [4,11] [4,12] [5,13] [6,14] [7,0] [7,15] [8,0] [8,15] [9,1] [9,14] [10,2] [10,13] [11,3] [11,4] [11,11] [11,12] [12,4] [12,11] [13,5] [13,10] [14,6] [14,9] [15,7] [15,8])
 8. EDIT 7 ([1,5] [1,10] [5,7] [5,8] [5,14] [7,5] [7,10] [8,5] [8,10] [10,1] [10,7] [10,8] [10,14] [14,5] [14,10])
 9. EDIT 2 ([2,3] [2,12] [3,2] [3,3] [3,12] [3,13] [4,7] [4,8] [5,6] [5,9] [6,5] [6,10] [7,4] [7,11] [8,4] [8,11] [9,5] [9,10] [10,6] [10,9] [11,7] [11,8] [12,2] [12,3] [12,12] [12,13] [13,3] [13,12])} EXTRACT [5,12 7,14] MOTIF {CUT [0,0 2,2]
10. FLIP HORIZONTAL [0,0 2,2]
11. PASTE [0,0]


============================================================

b8825c91 - VRAI MOSAIC PROBABLE
--------------------------------------------------
Type de scénario: TRANSFERT_BLOCK
Commande initiale: TRANSFERT
Complexité: 26
Nombre de commandes: 13
Commandes uniques: 4
Contient MOTIF: False
Commandes groupées: False
Indicateurs MOSAIC: clipboard_PASTE, multiple_coordinates, transform_FLIP
Commandes principales: EDIT, FLIP, PASTE, INIT

SCÉNARIO INTELLIGENT (entre INIT/TRANSFERT et END):
----------------------------------------
INIT 16x16; EDIT 7 ([0,0] [0,1] [0,14] [0,15] [1,0] [1,15] [4,5] [5,4] [5,5] [7,7] [7,8] [8,7] [8,8] [10,4] [10,5] [10,10] [10,11] [11,5] [11,10] [14,0] [14,15] [15,0] [15,1] [15,14] [15,15]); EDIT 8 ([0,2] [0,5] [0,10] [0,13] [1,3] [1,4] [1,5] [1,10] [1,11] [1,12] [2,0] [2,15] [3,1] [3,14] [4,1] [4,4] [4,14] [5,0] [5,1] [5,14] [5,15] [10,0] [10,1] [10,14] [10,15] [11,1] [11,4] [11,11] [11,14] [12,1] [12,14] [13,0] [13,15] [14,3] [14,4] [14,5] [14,10] [14,11] [14,12] [15,2] [15,5] [15,10] [15,13]); EDIT 1 ([0,3] [0,12] [1,1] [1,2] [1,13] [1,14] [2,1] [2,14] [3,0] [3,3] [3,9] [3,12] [3,15] [6,3] [7,2] [7,3] [8,2] [8,3] [8,12] [8,13] [9,3] [9,12] [12,0] [12,3] [12,6] [12,7] [12,8] [12,9] [12,12] [12,15] [13,1] [13,7] [13,8] [13,14] [14,1] [14,2] [14,13] [14,14] [15,3] [15,12]); EDIT 9 ([0,4] [0,11] [2,3] [2,12] [3,2] [3,13] [4,0] [4,15] [11,0] [11,15] [12,2] [12,13] [13,3] [13,12] [15,4] [15,11]); EDIT 2 ([0,6] [0,9] [2,4] [2,11] [4,2] [6,0] [6,15] [9,0] [9,15] [11,2] [11,13] [13,4] [13,11] [15,6] [15,9]); EDIT 6 ([0,7] [0,8] [1,6] [1,7] [1,8] [1,9] [2,2] [2,5] [2,9] [2,10] [2,13] [3,4] [3,5] [3,10] [3,11] [4,3] [4,9] [5,2] [5,3] [5,6] [5,9] [6,1] [6,2] [6,4] [6,5] [6,14] [7,0] [7,1] [7,4] [7,14] [7,15] [8,0] [8,1] [8,4] [8,11] [8,14] [8,15] [9,1] [9,2] [9,4] [9,5] [9,10] [9,11] [9,13] [9,14] [10,2] [10,3] [10,6] [10,9] [10,12] [10,13] [11,3] [11,6] [11,7] [11,8] [11,9] [11,12] [12,4] [12,5] [12,10] [12,11] [13,2] [13,5] [13,6] [13,9] [13,10] [13,13] [14,6] [14,7] [14,8] [14,9] [15,7] [15,8]); EDIT 4 ([2,6] [2,7] [2,8] [3,6] [3,7] [3,8] [4,6] [4,7] [4,8] [4,10] [4,11] [4,12] [4,13] [5,10] [5,11] [5,12] [5,13] [6,10] [6,11] [6,12] [6,13] [7,10] [7,11] [7,12] [7,13]); EDIT 5 ([5,7] [5,8] [6,6] [6,7] [6,8] [6,9] [7,5] [7,6] [7,9] [8,5] [8,6] [8,9] [8,10] [9,6] [9,7] [9,8] [9,9] [10,7] [10,8])} MOTIF {COPY [11,6 13,8]; FLIP VERTICAL [11,6 13,8]; PASTE [2,6]} MOTIF {COPY [4,2 7,5]; FLIP HORIZONTAL [4,2 7,5]; PASTE [4,10]

DÉTAIL DES COMMANDES:
------------------------------
 1. INIT 16x16
 2. EDIT 7 ([0,0] [0,1] [0,14] [0,15] [1,0] [1,15] [4,5] [5,4] [5,5] [7,7] [7,8] [8,7] [8,8] [10,4] [10,5] [10,10] [10,11] [11,5] [11,10] [14,0] [14,15] [15,0] [15,1] [15,14] [15,15])
 3. EDIT 8 ([0,2] [0,5] [0,10] [0,13] [1,3] [1,4] [1,5] [1,10] [1,11] [1,12] [2,0] [2,15] [3,1] [3,14] [4,1] [4,4] [4,14] [5,0] [5,1] [5,14] [5,15] [10,0] [10,1] [10,14] [10,15] [11,1] [11,4] [11,11] [11,14] [12,1] [12,14] [13,0] [13,15] [14,3] [14,4] [14,5] [14,10] [14,11] [14,12] [15,2] [15,5] [15,10] [15,13])
 4. EDIT 1 ([0,3] [0,12] [1,1] [1,2] [1,13] [1,14] [2,1] [2,14] [3,0] [3,3] [3,9] [3,12] [3,15] [6,3] [7,2] [7,3] [8,2] [8,3] [8,12] [8,13] [9,3] [9,12] [12,0] [12,3] [12,6] [12,7] [12,8] [12,9] [12,12] [12,15] [13,1] [13,7] [13,8] [13,14] [14,1] [14,2] [14,13] [14,14] [15,3] [15,12])
 5. EDIT 9 ([0,4] [0,11] [2,3] [2,12] [3,2] [3,13] [4,0] [4,15] [11,0] [11,15] [12,2] [12,13] [13,3] [13,12] [15,4] [15,11])
 6. EDIT 2 ([0,6] [0,9] [2,4] [2,11] [4,2] [6,0] [6,15] [9,0] [9,15] [11,2] [11,13] [13,4] [13,11] [15,6] [15,9])
 7. EDIT 6 ([0,7] [0,8] [1,6] [1,7] [1,8] [1,9] [2,2] [2,5] [2,9] [2,10] [2,13] [3,4] [3,5] [3,10] [3,11] [4,3] [4,9] [5,2] [5,3] [5,6] [5,9] [6,1] [6,2] [6,4] [6,5] [6,14] [7,0] [7,1] [7,4] [7,14] [7,15] [8,0] [8,1] [8,4] [8,11] [8,14] [8,15] [9,1] [9,2] [9,4] [9,5] [9,10] [9,11] [9,13] [9,14] [10,2] [10,3] [10,6] [10,9] [10,12] [10,13] [11,3] [11,6] [11,7] [11,8] [11,9] [11,12] [12,4] [12,5] [12,10] [12,11] [13,2] [13,5] [13,6] [13,9] [13,10] [13,13] [14,6] [14,7] [14,8] [14,9] [15,7] [15,8])
 8. EDIT 4 ([2,6] [2,7] [2,8] [3,6] [3,7] [3,8] [4,6] [4,7] [4,8] [4,10] [4,11] [4,12] [4,13] [5,10] [5,11] [5,12] [5,13] [6,10] [6,11] [6,12] [6,13] [7,10] [7,11] [7,12] [7,13])
 9. EDIT 5 ([5,7] [5,8] [6,6] [6,7] [6,8] [6,9] [7,5] [7,6] [7,9] [8,5] [8,6] [8,9] [8,10] [9,6] [9,7] [9,8] [9,9] [10,7] [10,8])} MOTIF {COPY [11,6 13,8]
10. FLIP VERTICAL [11,6 13,8]
11. PASTE [2,6]} MOTIF {COPY [4,2 7,5]
12. FLIP HORIZONTAL [4,2 7,5]
13. PASTE [4,10]


============================================================

c3f564a4 - VRAI MOSAIC PROBABLE
--------------------------------------------------
Type de scénario: TRANSFERT_BLOCK
Commande initiale: TRANSFERT
Complexité: 23
Nombre de commandes: 12
Commandes uniques: 3
Contient MOTIF: False
Commandes groupées: False
Indicateurs MOSAIC: clipboard_PASTE, multiple_coordinates
Commandes principales: EDIT, PASTE, INIT

SCÉNARIO INTELLIGENT (entre INIT/TRANSFERT et END):
----------------------------------------
INIT 16x16; EDIT 1 ([0,0] [1,15] [2,6] [2,14] [3,5] [3,13] [4,12] [5,11] [6,10] [7,1] [7,9] [8,0] [9,7] [9,15] [10,6] [10,14] [11,5] [11,13] [12,4] [12,12] [13,3] [13,11] [14,2] [14,10] [15,1] [15,9]); EDIT 2 ([0,1] [1,0] [2,7] [2,15] [3,6] [3,14] [4,13] [5,12] [6,11] [7,10] [8,1] [8,9] [9,0] [9,8] [10,7] [10,15] [11,6] [11,14] [12,5] [12,13] [13,4] [13,12] [14,3] [14,11] [15,2] [15,10]); EDIT 3 ([0,2] [0,10] [1,1] [2,0] [2,8] [3,7] [3,15] [4,6] [4,14] [5,13] [6,12] [7,11] [8,2] [8,10] [9,1] [9,9] [10,0] [10,8] [11,7] [11,15] [12,6] [12,14] [13,5] [13,13] [14,4] [14,12] [15,3] [15,11]); EDIT 4 ([0,3] [0,11] [1,2] [1,10] [2,1] [2,9] [3,0] [3,8] [4,7] [4,15] [5,14] [6,13] [7,12] [8,3] [8,11] [9,2] [9,10] [10,1] [10,9] [11,0] [11,8] [12,15] [13,6] [13,14] [14,5] [14,13] [15,4] [15,12]); EDIT 5 ([0,4] [0,12] [1,3] [1,11] [2,2] [2,10] [3,1] [3,9] [4,0] [4,8] [5,15] [6,14] [7,13] [8,4] [8,12] [9,3] [9,11] [10,2] [10,10] [11,1] [11,9] [12,0] [13,15] [14,6] [14,14] [15,5] [15,13]); EDIT 6 ([0,5] [0,13] [1,4] [1,12] [2,3] [2,11] [3,2] [3,10] [4,1] [4,9] [5,0] [6,15] [7,14] [8,13] [9,4] [9,12] [10,3] [10,11] [11,2] [11,10] [12,1] [12,9] [13,0] [14,7] [14,15] [15,6] [15,14]); EDIT 7 ([0,6] [0,14] [1,5] [1,13] [2,4] [2,12] [3,3] [3,11] [4,10] [5,1] [5,9] [6,0] [7,15] [8,14] [9,5] [9,13] [10,4] [10,12] [11,3] [11,11] [12,2] [12,10] [13,1] [13,9] [14,0] [14,8] [15,7] [15,15]); EDIT 8 ([0,15] [1,6] [1,14] [2,5] [2,13] [3,4] [3,12] [4,11] [5,10] [6,1] [6,9] [7,0] [8,15] [9,6] [9,14] [10,5] [10,13] [11,4] [11,12] [12,3] [12,11] [13,2] [13,10] [14,1] [14,9] [15,0] [15,8])} MOTIF {COPY [8,11 9,12]; PASTE [12,7]} MOTIF {COPY [10,4 14,10]; PASTE [4,2]} MOTIF {COPY [4,3 5,5]; PASTE [0,7]

DÉTAIL DES COMMANDES:
------------------------------
 1. INIT 16x16
 2. EDIT 1 ([0,0] [1,15] [2,6] [2,14] [3,5] [3,13] [4,12] [5,11] [6,10] [7,1] [7,9] [8,0] [9,7] [9,15] [10,6] [10,14] [11,5] [11,13] [12,4] [12,12] [13,3] [13,11] [14,2] [14,10] [15,1] [15,9])
 3. EDIT 2 ([0,1] [1,0] [2,7] [2,15] [3,6] [3,14] [4,13] [5,12] [6,11] [7,10] [8,1] [8,9] [9,0] [9,8] [10,7] [10,15] [11,6] [11,14] [12,5] [12,13] [13,4] [13,12] [14,3] [14,11] [15,2] [15,10])
 4. EDIT 3 ([0,2] [0,10] [1,1] [2,0] [2,8] [3,7] [3,15] [4,6] [4,14] [5,13] [6,12] [7,11] [8,2] [8,10] [9,1] [9,9] [10,0] [10,8] [11,7] [11,15] [12,6] [12,14] [13,5] [13,13] [14,4] [14,12] [15,3] [15,11])
 5. EDIT 4 ([0,3] [0,11] [1,2] [1,10] [2,1] [2,9] [3,0] [3,8] [4,7] [4,15] [5,14] [6,13] [7,12] [8,3] [8,11] [9,2] [9,10] [10,1] [10,9] [11,0] [11,8] [12,15] [13,6] [13,14] [14,5] [14,13] [15,4] [15,12])
 6. EDIT 5 ([0,4] [0,12] [1,3] [1,11] [2,2] [2,10] [3,1] [3,9] [4,0] [4,8] [5,15] [6,14] [7,13] [8,4] [8,12] [9,3] [9,11] [10,2] [10,10] [11,1] [11,9] [12,0] [13,15] [14,6] [14,14] [15,5] [15,13])
 7. EDIT 6 ([0,5] [0,13] [1,4] [1,12] [2,3] [2,11] [3,2] [3,10] [4,1] [4,9] [5,0] [6,15] [7,14] [8,13] [9,4] [9,12] [10,3] [10,11] [11,2] [11,10] [12,1] [12,9] [13,0] [14,7] [14,15] [15,6] [15,14])
 8. EDIT 7 ([0,6] [0,14] [1,5] [1,13] [2,4] [2,12] [3,3] [3,11] [4,10] [5,1] [5,9] [6,0] [7,15] [8,14] [9,5] [9,13] [10,4] [10,12] [11,3] [11,11] [12,2] [12,10] [13,1] [13,9] [14,0] [14,8] [15,7] [15,15])
 9. EDIT 8 ([0,15] [1,6] [1,14] [2,5] [2,13] [3,4] [3,12] [4,11] [5,10] [6,1] [6,9] [7,0] [8,15] [9,6] [9,14] [10,5] [10,13] [11,4] [11,12] [12,3] [12,11] [13,2] [13,10] [14,1] [14,9] [15,0] [15,8])} MOTIF {COPY [8,11 9,12]
10. PASTE [12,7]} MOTIF {COPY [10,4 14,10]
11. PASTE [4,2]} MOTIF {COPY [4,3 5,5]
12. PASTE [0,7]


============================================================

dc0a314f - VRAI MOSAIC PROBABLE
--------------------------------------------------
Type de scénario: TRANSFERT_SIMPLE
Commande initiale: TRANSFERT
Complexité: 23
Nombre de commandes: 10
Commandes uniques: 4
Contient MOTIF: False
Commandes groupées: False
Indicateurs MOSAIC: clipboard_PASTE, multiple_coordinates, transform_FLIP
Commandes principales: EDIT, FLIP, {INIT, PASTE

SCÉNARIO INTELLIGENT (entre INIT/TRANSFERT et END):
----------------------------------------
{INIT 16x16; EDIT 5 ([0,0] [0,1] [0,3] [0,5] [0,6] [0,7] [0,8] [0,9] [0,10] [0,12] [0,14] [0,15] [1,0] [1,3] [1,4] [1,5] [1,10] [1,11] [1,12] [1,15] [2,2] [2,4] [2,7] [2,8] [2,11] [2,13] [3,0] [3,1] [3,3] [3,4] [3,6] [3,7] [3,8] [3,9] [3,11] [3,12] [3,14] [3,15] [4,1] [4,2] [4,3] [4,13] [4,14] [5,0] [5,1] [5,14] [5,15] [6,0] [6,3] [6,15] [7,0] [7,2] [7,3] [7,13] [7,15] [8,0] [8,2] [8,3] [8,13] [8,15] [9,0] [9,3] [9,12] [9,15] [10,0] [10,1] [10,14] [10,15] [11,1] [11,2] [11,3] [11,12] [11,13] [11,14] [12,0] [12,1] [12,3] [12,4] [12,6] [12,7] [12,8] [12,9] [12,11] [12,12] [12,14] [12,15] [13,2] [13,4] [13,7] [13,8] [13,11] [13,13] [14,0] [14,3] [14,4] [14,5] [14,10] [14,11] [14,12] [14,15] [15,0] [15,1] [15,3] [15,5] [15,6] [15,7] [15,8] [15,9] [15,10] [15,12] [15,14] [15,15]); EDIT 2 ([0,2] [0,4] [0,11] [0,13] [1,1] [1,2] [1,6] [1,7] [1,8] [1,9] [1,13] [1,14] [2,0] [2,1] [2,5] [2,6] [2,9] [2,10] [2,14] [2,15] [3,5] [3,10] [4,0] [4,15] [5,2] [5,3] [5,13] [6,1] [6,2] [6,13] [6,14] [7,1] [7,14] [8,1] [8,14] [9,1] [9,2] [9,13] [9,14] [10,2] [10,3] [10,12] [10,13] [11,0] [11,15] [12,5] [12,10] [13,0] [13,1] [13,5] [13,6] [13,9] [13,10] [13,14] [13,15] [14,1] [14,2] [14,6] [14,7] [14,8] [14,9] [14,13] [14,14] [15,2] [15,4] [15,11] [15,13]); EDIT 8 ([2,3] [2,12] [3,2] [3,13] [12,2] [12,13] [13,3] [13,12]); EDIT 4 ([4,4] [11,4] [11,11]); EDIT 6 ([4,5] [4,6] [5,4] [5,5] [6,4] [6,6] [9,4] [9,6] [9,9] [9,11] [10,4] [10,5] [10,10] [10,11] [11,5] [11,6] [11,9] [11,10]); EDIT 9 ([4,7] [5,6] [5,7] [6,5] [6,7] [7,4] [7,5] [7,6] [7,7] [8,4] [8,5] [8,6] [8,7] [9,5] [9,7] [9,8] [9,10] [10,6] [10,7] [10,8] [10,9] [11,7] [11,8]); EDIT 3 ([4,8] [4,9] [4,10] [4,11] [4,12] [5,8] [5,9] [5,10] [5,11] [5,12] [6,8] [6,9] [6,10] [6,11] [6,12] [7,8] [7,9] [7,10] [7,11] [7,12] [8,8] [8,9] [8,10] [8,11] [8,12])} MOTIF {COPY [4,3 8,7]; FLIP HORIZONTAL [4,8 8,12]; PASTE [4,8]} EXTRACT [4,8 8,12]

DÉTAIL DES COMMANDES:
------------------------------
 1. {INIT 16x16
 2. EDIT 5 ([0,0] [0,1] [0,3] [0,5] [0,6] [0,7] [0,8] [0,9] [0,10] [0,12] [0,14] [0,15] [1,0] [1,3] [1,4] [1,5] [1,10] [1,11] [1,12] [1,15] [2,2] [2,4] [2,7] [2,8] [2,11] [2,13] [3,0] [3,1] [3,3] [3,4] [3,6] [3,7] [3,8] [3,9] [3,11] [3,12] [3,14] [3,15] [4,1] [4,2] [4,3] [4,13] [4,14] [5,0] [5,1] [5,14] [5,15] [6,0] [6,3] [6,15] [7,0] [7,2] [7,3] [7,13] [7,15] [8,0] [8,2] [8,3] [8,13] [8,15] [9,0] [9,3] [9,12] [9,15] [10,0] [10,1] [10,14] [10,15] [11,1] [11,2] [11,3] [11,12] [11,13] [11,14] [12,0] [12,1] [12,3] [12,4] [12,6] [12,7] [12,8] [12,9] [12,11] [12,12] [12,14] [12,15] [13,2] [13,4] [13,7] [13,8] [13,11] [13,13] [14,0] [14,3] [14,4] [14,5] [14,10] [14,11] [14,12] [14,15] [15,0] [15,1] [15,3] [15,5] [15,6] [15,7] [15,8] [15,9] [15,10] [15,12] [15,14] [15,15])
 3. EDIT 2 ([0,2] [0,4] [0,11] [0,13] [1,1] [1,2] [1,6] [1,7] [1,8] [1,9] [1,13] [1,14] [2,0] [2,1] [2,5] [2,6] [2,9] [2,10] [2,14] [2,15] [3,5] [3,10] [4,0] [4,15] [5,2] [5,3] [5,13] [6,1] [6,2] [6,13] [6,14] [7,1] [7,14] [8,1] [8,14] [9,1] [9,2] [9,13] [9,14] [10,2] [10,3] [10,12] [10,13] [11,0] [11,15] [12,5] [12,10] [13,0] [13,1] [13,5] [13,6] [13,9] [13,10] [13,14] [13,15] [14,1] [14,2] [14,6] [14,7] [14,8] [14,9] [14,13] [14,14] [15,2] [15,4] [15,11] [15,13])
 4. EDIT 8 ([2,3] [2,12] [3,2] [3,13] [12,2] [12,13] [13,3] [13,12])
 5. EDIT 4 ([4,4] [11,4] [11,11])
 6. EDIT 6 ([4,5] [4,6] [5,4] [5,5] [6,4] [6,6] [9,4] [9,6] [9,9] [9,11] [10,4] [10,5] [10,10] [10,11] [11,5] [11,6] [11,9] [11,10])
 7. EDIT 9 ([4,7] [5,6] [5,7] [6,5] [6,7] [7,4] [7,5] [7,6] [7,7] [8,4] [8,5] [8,6] [8,7] [9,5] [9,7] [9,8] [9,10] [10,6] [10,7] [10,8] [10,9] [11,7] [11,8])
 8. EDIT 3 ([4,8] [4,9] [4,10] [4,11] [4,12] [5,8] [5,9] [5,10] [5,11] [5,12] [6,8] [6,9] [6,10] [6,11] [6,12] [7,8] [7,9] [7,10] [7,11] [7,12] [8,8] [8,9] [8,10] [8,11] [8,12])} MOTIF {COPY [4,3 8,7]
 9. FLIP HORIZONTAL [4,8 8,12]
10. PASTE [4,8]} EXTRACT [4,8 8,12]


============================================================

e26a3af2 - FAUX POSITIF SUSPECTÉ
--------------------------------------------------
Type de scénario: TRANSFERT_BLOCK
Commande initiale: TRANSFERT
Complexité: 25
Nombre de commandes: 14
Commandes uniques: 3
Contient MOTIF: False
Commandes groupées: False
Indicateurs MOSAIC: multiple_coordinates
Commandes principales: EDIT, REPLACE, INIT

SCÉNARIO INTELLIGENT (entre INIT/TRANSFERT et END):
----------------------------------------
INIT 15x15; EDIT 6 ([0,0] [3,5] [5,5] [5,6] [6,10] [14,1]); EDIT 1 ([0,1] [0,2] [0,4] [0,5] [0,6] [0,7] [0,8] [0,9] [0,10] [0,11] [0,12] [0,13] [0,14] [1,0] [1,2] [1,3] [1,5] [1,6] [1,7] [1,8] [1,10] [1,11] [1,12] [1,13] [1,14] [2,1] [2,2] [2,3] [2,5] [2,6] [2,7] [2,8] [2,9] [2,10] [2,11] [2,12] [2,13] [2,14] [4,4] [7,0] [8,4] [10,12] [13,2]); EDIT 5 ([0,3] [1,9] [2,0] [5,1] [5,4] [5,12] [10,8]); EDIT 4 ([1,1] [2,4] [3,10] [3,12] [9,10] [11,0] [11,1] [11,2] [11,3] [11,4] [11,5] [11,8] [11,9] [11,10] [11,11] [11,12] [11,14] [12,0] [12,1] [12,3] [12,4] [12,5] [12,6] [12,7] [12,8] [12,9] [12,11] [12,12] [12,13] [12,14]); EDIT 9 ([1,4] [3,7] [4,2] [7,6] [8,14] [13,3]); EDIT 7 ([3,0] [4,13] [8,10] [11,6] [12,2]); EDIT 2 ([3,1] [3,2] [3,3] [3,4] [3,6] [3,8] [3,9] [3,11] [3,13] [3,14] [4,0] [4,1] [4,3] [4,5] [4,6] [4,7] [4,9] [4,10] [4,12] [4,14] [5,0] [5,2] [5,3] [5,7] [5,8] [5,9] [5,11] [5,13] [5,14] [6,0] [6,1] [6,2] [6,3] [6,4] [6,5] [6,6] [6,7] [6,8] [6,9] [6,11] [6,13] [6,14] [11,13]); EDIT 3 ([4,8] [5,10] [11,7] [13,0] [13,1] [13,4] [13,5] [13,6] [13,7] [13,8] [13,9] [13,10] [13,11] [13,12] [13,13] [13,14] [14,2] [14,3] [14,5] [14,6] [14,7] [14,8] [14,9] [14,10] [14,11] [14,12] [14,13] [14,14]); EDIT 8 ([4,11] [6,12] [7,1] [7,2] [7,3] [7,4] [7,5] [7,7] [7,8] [7,9] [7,10] [7,11] [7,12] [7,13] [7,14] [8,0] [8,1] [8,2] [8,3] [8,5] [8,6] [8,7] [8,8] [8,9] [8,11] [8,12] [8,13] [9,0] [9,1] [9,2] [9,3] [9,4] [9,5] [9,6] [9,7] [9,8] [9,9] [9,11] [9,12] [9,13] [9,14] [10,0] [10,1] [10,2] [10,3] [10,4] [10,5] [10,6] [10,7] [10,9] [10,10] [10,11] [10,13] [10,14] [12,10] [14,0] [14,4])} REPLACES {REPLACE 4,5,6,9 1 [0,0 2,14]; REPLACE 6,9,1,3,4,5,7,8 2 [3,0 6,14]; REPLACE 6,9,1,3,4,5,7 8 [7,0 10,14]; REPLACE 6,9,1,3,5,7,8,2 4 [11,0 12,14]; REPLACE 6,9,1,5,7,8,2 3 [13,0 14,14]

DÉTAIL DES COMMANDES:
------------------------------
 1. INIT 15x15
 2. EDIT 6 ([0,0] [3,5] [5,5] [5,6] [6,10] [14,1])
 3. EDIT 1 ([0,1] [0,2] [0,4] [0,5] [0,6] [0,7] [0,8] [0,9] [0,10] [0,11] [0,12] [0,13] [0,14] [1,0] [1,2] [1,3] [1,5] [1,6] [1,7] [1,8] [1,10] [1,11] [1,12] [1,13] [1,14] [2,1] [2,2] [2,3] [2,5] [2,6] [2,7] [2,8] [2,9] [2,10] [2,11] [2,12] [2,13] [2,14] [4,4] [7,0] [8,4] [10,12] [13,2])
 4. EDIT 5 ([0,3] [1,9] [2,0] [5,1] [5,4] [5,12] [10,8])
 5. EDIT 4 ([1,1] [2,4] [3,10] [3,12] [9,10] [11,0] [11,1] [11,2] [11,3] [11,4] [11,5] [11,8] [11,9] [11,10] [11,11] [11,12] [11,14] [12,0] [12,1] [12,3] [12,4] [12,5] [12,6] [12,7] [12,8] [12,9] [12,11] [12,12] [12,13] [12,14])
 6. EDIT 9 ([1,4] [3,7] [4,2] [7,6] [8,14] [13,3])
 7. EDIT 7 ([3,0] [4,13] [8,10] [11,6] [12,2])
 8. EDIT 2 ([3,1] [3,2] [3,3] [3,4] [3,6] [3,8] [3,9] [3,11] [3,13] [3,14] [4,0] [4,1] [4,3] [4,5] [4,6] [4,7] [4,9] [4,10] [4,12] [4,14] [5,0] [5,2] [5,3] [5,7] [5,8] [5,9] [5,11] [5,13] [5,14] [6,0] [6,1] [6,2] [6,3] [6,4] [6,5] [6,6] [6,7] [6,8] [6,9] [6,11] [6,13] [6,14] [11,13])
 9. EDIT 3 ([4,8] [5,10] [11,7] [13,0] [13,1] [13,4] [13,5] [13,6] [13,7] [13,8] [13,9] [13,10] [13,11] [13,12] [13,13] [13,14] [14,2] [14,3] [14,5] [14,6] [14,7] [14,8] [14,9] [14,10] [14,11] [14,12] [14,13] [14,14])
10. EDIT 8 ([4,11] [6,12] [7,1] [7,2] [7,3] [7,4] [7,5] [7,7] [7,8] [7,9] [7,10] [7,11] [7,12] [7,13] [7,14] [8,0] [8,1] [8,2] [8,3] [8,5] [8,6] [8,7] [8,8] [8,9] [8,11] [8,12] [8,13] [9,0] [9,1] [9,2] [9,3] [9,4] [9,5] [9,6] [9,7] [9,8] [9,9] [9,11] [9,12] [9,13] [9,14] [10,0] [10,1] [10,2] [10,3] [10,4] [10,5] [10,6] [10,7] [10,9] [10,10] [10,11] [10,13] [10,14] [12,10] [14,0] [14,4])} REPLACES {REPLACE 4,5,6,9 1 [0,0 2,14]
11. REPLACE 6,9,1,3,4,5,7,8 2 [3,0 6,14]
12. REPLACE 6,9,1,3,4,5,7 8 [7,0 10,14]
13. REPLACE 6,9,1,3,5,7,8,2 4 [11,0 12,14]
14. REPLACE 6,9,1,5,7,8,2 3 [13,0 14,14]


============================================================

09629e4f - FAUX POSITIF SUSPECTÉ
--------------------------------------------------
Type de scénario: TRANSFERT_BLOCK
Commande initiale: TRANSFERT
Complexité: 21
Nombre de commandes: 10
Commandes uniques: 3
Contient MOTIF: False
Commandes groupées: False
Indicateurs MOSAIC: multiple_coordinates
Commandes principales: EDIT, FILL, INIT

SCÉNARIO INTELLIGENT (entre INIT/TRANSFERT et END):
----------------------------------------
INIT 11x11; EDIT 6 ([0,0] [1,6] [2,8] [4,9] [5,2] [6,6] [8,5] [9,1] [9,10]); EDIT 4 ([0,1] [0,9] [2,4] [5,1] [5,5] [5,10] [8,2] [8,6] [9,8]); EDIT 5 ([0,3] [0,7] [1,3] [1,7] [2,3] [2,7] [3,0] [3,1] [3,2] [3,3] [3,4] [3,5] [3,6] [3,7] [3,8] [3,9] [3,10] [4,3] [4,7] [5,3] [5,7] [6,3] [6,7] [7,0] [7,1] [7,2] [7,3] [7,4] [7,5] [7,6] [7,7] [7,8] [7,9] [7,10] [8,3] [8,7] [9,3] [9,7] [10,3] [10,7]); EDIT 3 ([0,5] [1,2] [2,9] [4,5] [4,8] [5,0] [9,2] [9,6] [10,8]); EDIT 2 ([1,4] [1,10] [2,0] [4,0] [4,10] [5,6] [8,1] [8,9] [10,4]); EDIT 8 ([1,5] [1,8] [2,2] [5,4] [6,1] [6,8] [8,10] [10,6])} CLEAR ([0,0 2,2] [4,0 6,2] [8,0 10,2] [8,4 10,6] [8,8 10,10]) FILLS {FILL 2 [0,4 2,6]; FILL 4 [0,8 2,10]; FILL 6 [4,4 6,6]; FILL 3 [4,8 6,10]

DÉTAIL DES COMMANDES:
------------------------------
 1. INIT 11x11
 2. EDIT 6 ([0,0] [1,6] [2,8] [4,9] [5,2] [6,6] [8,5] [9,1] [9,10])
 3. EDIT 4 ([0,1] [0,9] [2,4] [5,1] [5,5] [5,10] [8,2] [8,6] [9,8])
 4. EDIT 5 ([0,3] [0,7] [1,3] [1,7] [2,3] [2,7] [3,0] [3,1] [3,2] [3,3] [3,4] [3,5] [3,6] [3,7] [3,8] [3,9] [3,10] [4,3] [4,7] [5,3] [5,7] [6,3] [6,7] [7,0] [7,1] [7,2] [7,3] [7,4] [7,5] [7,6] [7,7] [7,8] [7,9] [7,10] [8,3] [8,7] [9,3] [9,7] [10,3] [10,7])
 5. EDIT 3 ([0,5] [1,2] [2,9] [4,5] [4,8] [5,0] [9,2] [9,6] [10,8])
 6. EDIT 2 ([1,4] [1,10] [2,0] [4,0] [4,10] [5,6] [8,1] [8,9] [10,4])
 7. EDIT 8 ([1,5] [1,8] [2,2] [5,4] [6,1] [6,8] [8,10] [10,6])} CLEAR ([0,0 2,2] [4,0 6,2] [8,0 10,2] [8,4 10,6] [8,8 10,10]) FILLS {FILL 2 [0,4 2,6]
 8. FILL 4 [0,8 2,10]
 9. FILL 6 [4,4 6,6]
10. FILL 3 [4,8 6,10]


============================================================

1c786137 - FAUX POSITIF SUSPECTÉ
--------------------------------------------------
Type de scénario: TRANSFERT_SIMPLE
Commande initiale: TRANSFERT
Complexité: 14
Nombre de commandes: 5
Commandes uniques: 2
Contient MOTIF: False
Commandes groupées: False
Indicateurs MOSAIC: multiple_coordinates
Commandes principales: EDIT, {INIT

SCÉNARIO INTELLIGENT (entre INIT/TRANSFERT et END):
----------------------------------------
{INIT 16x17; EDIT 8 ([0,3] [0,6] [0,9] [0,11] [0,15] [1,3] [1,4] [1,10] [2,2] [2,3] [2,6] [2,7] [2,10] [2,12] [3,4] [3,7] [3,12] [3,13] [3,14] [4,0] [4,2] [5,2] [5,6] [6,1] [6,2] [6,4] [6,7] [6,9] [6,12] [6,15] [7,2] [7,4] [7,11] [7,14] [7,15] [8,1] [8,6] [8,7] [8,10] [8,12] [9,6] [9,7] [9,9] [9,14] [10,1] [10,2] [10,5] [10,6] [10,8] [10,9] [10,10] [10,11] [10,12] [10,14] [10,15] [11,4] [11,11] [11,12] [11,14] [12,1] [12,6] [12,7] [12,14] [13,0] [14,0] [14,6] [14,7] [14,11] [14,12] [14,13] [14,15] [15,0] [15,6] [15,10] [16,0] [16,2] [16,4] [16,6] [16,10] [16,12] [16,14] [16,15]); EDIT 1 ([0,4] [0,5] [1,1] [1,5] [1,7] [1,8] [1,11] [1,12] [2,4] [2,5] [2,8] [2,9] [2,15] [3,0] [3,2] [3,6] [3,8] [3,10] [3,11] [5,0] [5,7] [5,8] [5,9] [5,11] [6,5] [6,10] [6,14] [7,0] [7,9] [7,10] [7,12] [8,5] [8,8] [8,9] [8,11] [8,15] [9,0] [9,5] [9,15] [11,0] [11,1] [11,2] [12,0] [12,8] [12,15] [13,1] [13,2] [14,1] [14,9] [14,14] [15,1] [15,4] [15,5] [15,8] [15,11] [15,13] [16,9] [16,11] [16,13]); EDIT 2 ([1,9] [1,13] [1,15] [4,15] [5,4] [6,8] [6,11] [7,5] [7,7] [8,14] [9,11] [10,7] [11,6] [12,5] [12,9] [12,12] [13,15] [14,10] [15,9] [15,14] [16,3] [16,7]); EDIT 3 ([4,3] [4,4] [4,5] [4,6] [4,7] [4,8] [4,9] [4,10] [4,11] [4,12] [4,13] [5,3] [5,13] [6,3] [6,13] [7,3] [7,13] [8,3] [8,13] [9,3] [9,13] [10,3] [10,13] [11,3] [11,13] [12,3] [12,13] [13,3] [13,4] [13,5] [13,6] [13,7] [13,8] [13,9] [13,10] [13,11] [13,12] [13,13])} EXTRACT [5,4 12,12]

DÉTAIL DES COMMANDES:
------------------------------
 1. {INIT 16x17
 2. EDIT 8 ([0,3] [0,6] [0,9] [0,11] [0,15] [1,3] [1,4] [1,10] [2,2] [2,3] [2,6] [2,7] [2,10] [2,12] [3,4] [3,7] [3,12] [3,13] [3,14] [4,0] [4,2] [5,2] [5,6] [6,1] [6,2] [6,4] [6,7] [6,9] [6,12] [6,15] [7,2] [7,4] [7,11] [7,14] [7,15] [8,1] [8,6] [8,7] [8,10] [8,12] [9,6] [9,7] [9,9] [9,14] [10,1] [10,2] [10,5] [10,6] [10,8] [10,9] [10,10] [10,11] [10,12] [10,14] [10,15] [11,4] [11,11] [11,12] [11,14] [12,1] [12,6] [12,7] [12,14] [13,0] [14,0] [14,6] [14,7] [14,11] [14,12] [14,13] [14,15] [15,0] [15,6] [15,10] [16,0] [16,2] [16,4] [16,6] [16,10] [16,12] [16,14] [16,15])
 3. EDIT 1 ([0,4] [0,5] [1,1] [1,5] [1,7] [1,8] [1,11] [1,12] [2,4] [2,5] [2,8] [2,9] [2,15] [3,0] [3,2] [3,6] [3,8] [3,10] [3,11] [5,0] [5,7] [5,8] [5,9] [5,11] [6,5] [6,10] [6,14] [7,0] [7,9] [7,10] [7,12] [8,5] [8,8] [8,9] [8,11] [8,15] [9,0] [9,5] [9,15] [11,0] [11,1] [11,2] [12,0] [12,8] [12,15] [13,1] [13,2] [14,1] [14,9] [14,14] [15,1] [15,4] [15,5] [15,8] [15,11] [15,13] [16,9] [16,11] [16,13])
 4. EDIT 2 ([1,9] [1,13] [1,15] [4,15] [5,4] [6,8] [6,11] [7,5] [7,7] [8,14] [9,11] [10,7] [11,6] [12,5] [12,9] [12,12] [13,15] [14,10] [15,9] [15,14] [16,3] [16,7])
 5. EDIT 3 ([4,3] [4,4] [4,5] [4,6] [4,7] [4,8] [4,9] [4,10] [4,11] [4,12] [4,13] [5,3] [5,13] [6,3] [6,13] [7,3] [7,13] [8,3] [8,13] [9,3] [9,13] [10,3] [10,13] [11,3] [11,13] [12,3] [12,13] [13,3] [13,4] [13,5] [13,6] [13,7] [13,8] [13,9] [13,10] [13,11] [13,12] [13,13])} EXTRACT [5,4 12,12]


============================================================

4be741c5 - FAUX POSITIF SUSPECTÉ
--------------------------------------------------
Type de scénario: INIT
Commande initiale: INIT 4x1
Complexité: 18
Nombre de commandes: 4
Commandes uniques: 2
Contient MOTIF: False
Commandes groupées: True
Indicateurs MOSAIC: grouped_EDITS
Commandes principales: EDIT, EDITS

SCÉNARIO INTELLIGENT (entre INIT/TRANSFERT et END):
----------------------------------------
EDITS {EDIT 3 [0,0]; EDIT 2 [0,1]; EDIT 1 [0,2]; EDIT 8 [0,3]}

DÉTAIL DES COMMANDES:
------------------------------
 1. EDITS {EDIT 3 [0,0]
 2. EDIT 2 [0,1]
 3. EDIT 1 [0,2]
 4. EDIT 8 [0,3]}


============================================================

662c240a - FAUX POSITIF SUSPECTÉ
--------------------------------------------------
Type de scénario: TRANSFERT_SIMPLE
Commande initiale: TRANSFERT
Complexité: 16
Nombre de commandes: 7
Commandes uniques: 2
Contient MOTIF: False
Commandes groupées: False
Indicateurs MOSAIC: multiple_coordinates
Commandes principales: EDIT, {INIT

SCÉNARIO INTELLIGENT (entre INIT/TRANSFERT et END):
----------------------------------------
{INIT 3x9; EDIT 5 ([0,0] [1,1] [2,1]); EDIT 4 ([0,1] [0,2] [1,0] [1,2] [2,0] [2,2]); EDIT 3 ([3,0] [3,1] [4,0] [4,1] [5,2]); EDIT 2 ([3,2] [4,2] [5,0] [5,1]); EDIT 1 ([6,0] [6,1] [6,2] [7,0] [8,0]); EDIT 8 ([7,1] [7,2] [8,1] [8,2])} RESIZE 3x3

DÉTAIL DES COMMANDES:
------------------------------
 1. {INIT 3x9
 2. EDIT 5 ([0,0] [1,1] [2,1])
 3. EDIT 4 ([0,1] [0,2] [1,0] [1,2] [2,0] [2,2])
 4. EDIT 3 ([3,0] [3,1] [4,0] [4,1] [5,2])
 5. EDIT 2 ([3,2] [4,2] [5,0] [5,1])
 6. EDIT 1 ([6,0] [6,1] [6,2] [7,0] [8,0])
 7. EDIT 8 ([7,1] [7,2] [8,1] [8,2])} RESIZE 3x3


============================================================

68b16354 - FAUX POSITIF SUSPECTÉ
--------------------------------------------------
Type de scénario: TRANSFERT_BLOCK
Commande initiale: TRANSFERT
Complexité: 22
Nombre de commandes: 9
Commandes uniques: 4
Contient MOTIF: False
Commandes groupées: False
Indicateurs MOSAIC: clipboard_PASTE, multiple_coordinates, transform_FLIP
Commandes principales: EDIT, FLIP, PASTE, INIT

SCÉNARIO INTELLIGENT (entre INIT/TRANSFERT et END):
----------------------------------------
INIT 7x7; EDIT 2 ([0,0] [0,4] [3,2] [5,1] [6,4]); EDIT 8 ([0,1] [3,4] [4,5] [5,2] [5,5]); EDIT 1 ([0,2] [0,6] [1,2] [1,3] [2,0] [2,1] [2,2] [2,3] [3,0] [3,1] [3,5] [4,1] [4,2] [4,3] [5,4] [6,0] [6,3]); EDIT 3 ([0,3] [1,5] [2,6] [3,3] [3,6] [5,0] [6,5]); EDIT 4 ([0,5] [1,0] [1,1] [1,4] [1,6] [2,4] [4,0] [4,6] [5,3] [5,6] [6,1] [6,6]); EDIT 7 ([2,5] [4,4] [6,2])} MOTIF {CUT [0,0 6,6]; FLIP VERTICAL [0,0 6,6]; PASTE [0,0]

DÉTAIL DES COMMANDES:
------------------------------
 1. INIT 7x7
 2. EDIT 2 ([0,0] [0,4] [3,2] [5,1] [6,4])
 3. EDIT 8 ([0,1] [3,4] [4,5] [5,2] [5,5])
 4. EDIT 1 ([0,2] [0,6] [1,2] [1,3] [2,0] [2,1] [2,2] [2,3] [3,0] [3,1] [3,5] [4,1] [4,2] [4,3] [5,4] [6,0] [6,3])
 5. EDIT 3 ([0,3] [1,5] [2,6] [3,3] [3,6] [5,0] [6,5])
 6. EDIT 4 ([0,5] [1,0] [1,1] [1,4] [1,6] [2,4] [4,0] [4,6] [5,3] [5,6] [6,1] [6,6])
 7. EDIT 7 ([2,5] [4,4] [6,2])} MOTIF {CUT [0,0 6,6]
 8. FLIP VERTICAL [0,0 6,6]
 9. PASTE [0,0]


============================================================

855e0971 - FAUX POSITIF SUSPECTÉ
--------------------------------------------------
Type de scénario: TRANSFERT_SIMPLE
Commande initiale: TRANSFERT
Complexité: 14
Nombre de commandes: 5
Commandes uniques: 2
Contient MOTIF: False
Commandes groupées: False
Indicateurs MOSAIC: multiple_coordinates
Commandes principales: EDIT, {INIT

SCÉNARIO INTELLIGENT (entre INIT/TRANSFERT et END):
----------------------------------------
{INIT 15x17; EDIT 8 ([0,0] [0,1] [0,2] [0,3] [0,5] [0,6] [0,7] [0,8] [0,9] [0,10] [0,11] [0,12] [0,13] [0,14] [1,0] [1,1] [1,2] [1,3] [1,4] [1,5] [1,6] [1,7] [1,8] [1,9] [1,10] [1,11] [1,12] [1,13] [1,14] [2,0] [2,1] [2,2] [2,3] [2,4] [2,5] [2,6] [2,7] [2,8] [2,9] [2,10] [2,11] [2,13] [2,14] [3,0] [3,1] [3,2] [3,3] [3,4] [3,5] [3,6] [3,7] [3,8] [3,9] [3,10] [3,11] [3,12] [3,13] [3,14]); EDIT 1 ([4,0] [4,1] [4,2] [4,3] [4,4] [4,5] [4,6] [4,7] [4,8] [4,9] [4,10] [4,11] [4,12] [4,13] [4,14] [5,0] [5,1] [5,2] [5,3] [5,4] [5,5] [5,6] [5,7] [5,8] [5,9] [5,10] [5,11] [5,12] [5,13] [5,14] [6,0] [6,1] [6,2] [6,3] [6,4] [6,5] [6,7] [6,8] [6,9] [6,10] [6,11] [6,12] [6,13] [6,14] [7,0] [7,1] [7,2] [7,3] [7,4] [7,5] [7,6] [7,7] [7,8] [7,9] [7,10] [7,11] [7,12] [7,13] [7,14]); EDIT 4 ([8,0] [8,1] [8,2] [8,3] [8,4] [8,5] [8,6] [8,7] [8,8] [8,9] [8,10] [8,11] [8,12] [8,13] [8,14] [9,0] [9,1] [9,2] [9,3] [9,4] [9,5] [9,6] [9,7] [9,8] [9,9] [9,10] [9,11] [9,12] [9,13] [9,14] [10,0] [10,1] [10,2] [10,3] [10,4] [10,5] [10,6] [10,7] [10,8] [10,9] [10,11] [10,12] [10,13] [10,14] [11,0] [11,1] [11,2] [11,3] [11,4] [11,5] [11,6] [11,7] [11,8] [11,9] [11,10] [11,11] [11,12] [11,13] [11,14] [12,0] [12,1] [12,2] [12,3] [12,4] [12,5] [12,6] [12,7] [12,8] [12,9] [12,10] [12,11] [12,12] [12,13] [12,14]); EDIT 2 ([13,0] [13,1] [13,2] [13,3] [13,4] [13,5] [13,6] [13,7] [13,8] [13,9] [13,10] [13,11] [13,12] [13,13] [13,14] [14,0] [14,1] [14,2] [14,3] [14,4] [14,5] [14,6] [14,7] [14,8] [14,9] [14,10] [14,11] [14,12] [14,13] [14,14] [15,0] [15,2] [15,3] [15,4] [15,5] [15,6] [15,7] [15,8] [15,9] [15,10] [15,11] [15,12] [15,13] [15,14] [16,0] [16,1] [16,2] [16,3] [16,4] [16,5] [16,6] [16,7] [16,8] [16,9] [16,10] [16,11] [16,12] [16,13] [16,14])} EDIT 0 ([1,4] [2,4] [3,4] [0,12] [1,12] [3,12] [4,6] [5,6] [7,6] [8,10] [9,10] [11,10] [12,10] [13,1] [14,1] [16,1])

DÉTAIL DES COMMANDES:
------------------------------
 1. {INIT 15x17
 2. EDIT 8 ([0,0] [0,1] [0,2] [0,3] [0,5] [0,6] [0,7] [0,8] [0,9] [0,10] [0,11] [0,12] [0,13] [0,14] [1,0] [1,1] [1,2] [1,3] [1,4] [1,5] [1,6] [1,7] [1,8] [1,9] [1,10] [1,11] [1,12] [1,13] [1,14] [2,0] [2,1] [2,2] [2,3] [2,4] [2,5] [2,6] [2,7] [2,8] [2,9] [2,10] [2,11] [2,13] [2,14] [3,0] [3,1] [3,2] [3,3] [3,4] [3,5] [3,6] [3,7] [3,8] [3,9] [3,10] [3,11] [3,12] [3,13] [3,14])
 3. EDIT 1 ([4,0] [4,1] [4,2] [4,3] [4,4] [4,5] [4,6] [4,7] [4,8] [4,9] [4,10] [4,11] [4,12] [4,13] [4,14] [5,0] [5,1] [5,2] [5,3] [5,4] [5,5] [5,6] [5,7] [5,8] [5,9] [5,10] [5,11] [5,12] [5,13] [5,14] [6,0] [6,1] [6,2] [6,3] [6,4] [6,5] [6,7] [6,8] [6,9] [6,10] [6,11] [6,12] [6,13] [6,14] [7,0] [7,1] [7,2] [7,3] [7,4] [7,5] [7,6] [7,7] [7,8] [7,9] [7,10] [7,11] [7,12] [7,13] [7,14])
 4. EDIT 4 ([8,0] [8,1] [8,2] [8,3] [8,4] [8,5] [8,6] [8,7] [8,8] [8,9] [8,10] [8,11] [8,12] [8,13] [8,14] [9,0] [9,1] [9,2] [9,3] [9,4] [9,5] [9,6] [9,7] [9,8] [9,9] [9,10] [9,11] [9,12] [9,13] [9,14] [10,0] [10,1] [10,2] [10,3] [10,4] [10,5] [10,6] [10,7] [10,8] [10,9] [10,11] [10,12] [10,13] [10,14] [11,0] [11,1] [11,2] [11,3] [11,4] [11,5] [11,6] [11,7] [11,8] [11,9] [11,10] [11,11] [11,12] [11,13] [11,14] [12,0] [12,1] [12,2] [12,3] [12,4] [12,5] [12,6] [12,7] [12,8] [12,9] [12,10] [12,11] [12,12] [12,13] [12,14])
 5. EDIT 2 ([13,0] [13,1] [13,2] [13,3] [13,4] [13,5] [13,6] [13,7] [13,8] [13,9] [13,10] [13,11] [13,12] [13,13] [13,14] [14,0] [14,1] [14,2] [14,3] [14,4] [14,5] [14,6] [14,7] [14,8] [14,9] [14,10] [14,11] [14,12] [14,13] [14,14] [15,0] [15,2] [15,3] [15,4] [15,5] [15,6] [15,7] [15,8] [15,9] [15,10] [15,11] [15,12] [15,13] [15,14] [16,0] [16,1] [16,2] [16,3] [16,4] [16,5] [16,6] [16,7] [16,8] [16,9] [16,10] [16,11] [16,12] [16,13] [16,14])} EDIT 0 ([1,4] [2,4] [3,4] [0,12] [1,12] [3,12] [4,6] [5,6] [7,6] [8,10] [9,10] [11,10] [12,10] [13,1] [14,1] [16,1])


============================================================

c909285e - FAUX POSITIF SUSPECTÉ
--------------------------------------------------
Type de scénario: TRANSFERT_SIMPLE
Commande initiale: TRANSFERT
Complexité: 15
Nombre de commandes: 6
Commandes uniques: 2
Contient MOTIF: False
Commandes groupées: False
Indicateurs MOSAIC: multiple_coordinates
Commandes principales: EDIT, {INIT

SCÉNARIO INTELLIGENT (entre INIT/TRANSFERT et END):
----------------------------------------
{INIT 24x24; EDIT 1 ([0,2] [0,8] [0,20] [1,2] [1,8] [1,20] [2,0] [2,1] [2,2] [2,6] [2,8] [2,10] [2,12] [2,13] [2,16] [2,18] [2,20] [2,21] [2,22] [6,2] [6,8] [6,20] [8,0] [8,1] [8,2] [8,6] [8,8] [8,10] [8,12] [8,13] [8,16] [8,18] [8,20] [8,21] [8,22] [10,2] [10,8] [10,20] [12,2] [12,8] [12,20] [13,2] [13,8] [13,20] [16,2] [16,8] [16,20] [18,2] [18,8] [18,20] [20,0] [20,1] [20,2] [20,6] [20,8] [20,10] [20,12] [20,13] [20,16] [20,18] [20,20] [20,21] [20,22] [21,2] [21,8] [21,20] [22,2] [22,8] [22,20]); EDIT 2 ([0,3] [0,7] [0,15] [1,3] [1,7] [1,15] [2,3] [2,7] [2,15] [3,0] [3,1] [3,2] [3,3] [3,6] [3,7] [3,8] [3,10] [3,12] [3,13] [3,15] [3,16] [3,18] [3,20] [3,21] [3,22] [6,3] [6,7] [6,15] [7,0] [7,1] [7,2] [7,3] [7,6] [7,7] [7,8] [7,10] [7,12] [7,13] [7,15] [7,16] [7,18] [7,20] [7,21] [7,22] [8,3] [8,7] [8,15] [10,3] [10,7] [10,15] [12,3] [12,7] [12,15] [13,3] [13,7] [13,15] [15,0] [15,1] [15,2] [15,3] [15,6] [15,7] [15,8] [15,10] [15,12] [15,13] [15,15] [15,16] [15,18] [15,20] [15,21] [15,22] [16,3] [16,7] [16,15] [18,3] [18,7] [18,15] [20,3] [20,7] [20,15] [21,3] [21,7] [21,15] [22,3] [22,7] [22,15]); EDIT 3 ([0,4] [0,9] [0,14] [0,19] [1,4] [1,9] [1,14] [1,19] [2,4] [2,9] [2,14] [2,19] [3,4] [3,9] [3,14] [3,19] [4,0] [4,1] [4,2] [4,3] [4,4] [4,6] [4,7] [4,8] [4,9] [4,10] [4,12] [4,13] [4,14] [4,15] [4,16] [4,18] [4,19] [4,20] [4,21] [4,22] [6,4] [6,9] [6,14] [6,19] [7,4] [7,9] [7,14] [7,19] [8,4] [8,9] [8,14] [8,19] [9,0] [9,1] [9,2] [9,3] [9,4] [9,6] [9,7] [9,8] [9,9] [9,10] [9,12] [9,13] [9,14] [9,15] [9,16] [9,18] [9,19] [9,20] [9,21] [9,22] [10,4] [10,9] [10,14] [10,19] [12,4] [12,9] [12,14] [12,19] [13,4] [13,9] [13,14] [13,19] [14,0] [14,1] [14,2] [14,3] [14,4] [14,6] [14,7] [14,8] [14,9] [14,10] [14,12] [14,13] [14,20] [14,21] [14,22] [15,4] [15,9] [16,4] [16,9] [18,4] [18,9] [19,0] [19,1] [19,2] [19,3] [19,4] [19,6] [19,7] [19,8] [19,9] [19,10] [19,12] [19,13] [19,20] [19,21] [19,22] [20,4] [20,9] [20,14] [20,19] [21,4] [21,9] [21,14] [21,19] [22,4] [22,9] [22,14] [22,19]); EDIT 4 ([0,5] [0,11] [0,17] [0,23] [1,5] [1,11] [1,17] [1,23] [2,5] [2,11] [2,17] [2,23] [3,5] [3,11] [3,17] [3,23] [4,5] [4,11] [4,17] [4,23] [5,0] [5,1] [5,2] [5,3] [5,4] [5,5] [5,6] [5,7] [5,8] [5,9] [5,10] [5,11] [5,12] [5,13] [5,14] [5,15] [5,16] [5,17] [5,18] [5,19] [5,20] [5,21] [5,22] [5,23] [6,5] [6,11] [6,17] [6,23] [7,5] [7,11] [7,17] [7,23] [8,5] [8,11] [8,17] [8,23] [9,5] [9,11] [9,17] [9,23] [10,5] [10,11] [10,17] [10,23] [11,0] [11,1] [11,2] [11,3] [11,4] [11,5] [11,6] [11,7] [11,8] [11,9] [11,10] [11,11] [11,12] [11,13] [11,14] [11,15] [11,16] [11,17] [11,18] [11,19] [11,20] [11,21] [11,22] [11,23] [12,5] [12,11] [12,17] [12,23] [13,5] [13,11] [13,17] [13,23] [14,5] [14,11] [14,23] [15,5] [15,11] [15,17] [15,23] [16,5] [16,11] [16,17] [16,23] [17,0] [17,1] [17,2] [17,3] [17,4] [17,5] [17,6] [17,7] [17,8] [17,9] [17,10] [17,11] [17,12] [17,13] [17,15] [17,16] [17,17] [17,18] [17,20] [17,21] [17,22] [17,23] [18,5] [18,11] [18,17] [18,23] [19,5] [19,11] [19,23] [20,5] [20,11] [20,17] [20,23] [21,5] [21,11] [21,17] [21,23] [22,5] [22,11] [22,17] [22,23] [23,0] [23,1] [23,2] [23,3] [23,4] [23,5] [23,6] [23,7] [23,8] [23,9] [23,10] [23,11] [23,12] [23,13] [23,14] [23,15] [23,16] [23,17] [23,18] [23,19] [23,20] [23,21] [23,22] [23,23]); EDIT 8 ([14,14] [14,15] [14,16] [14,17] [14,18] [14,19] [15,14] [15,19] [16,14] [16,19] [17,14] [17,19] [18,14] [18,19] [19,14] [19,15] [19,16] [19,17] [19,18] [19,19])} EXTRACT [14,14 19,19]

DÉTAIL DES COMMANDES:
------------------------------
 1. {INIT 24x24
 2. EDIT 1 ([0,2] [0,8] [0,20] [1,2] [1,8] [1,20] [2,0] [2,1] [2,2] [2,6] [2,8] [2,10] [2,12] [2,13] [2,16] [2,18] [2,20] [2,21] [2,22] [6,2] [6,8] [6,20] [8,0] [8,1] [8,2] [8,6] [8,8] [8,10] [8,12] [8,13] [8,16] [8,18] [8,20] [8,21] [8,22] [10,2] [10,8] [10,20] [12,2] [12,8] [12,20] [13,2] [13,8] [13,20] [16,2] [16,8] [16,20] [18,2] [18,8] [18,20] [20,0] [20,1] [20,2] [20,6] [20,8] [20,10] [20,12] [20,13] [20,16] [20,18] [20,20] [20,21] [20,22] [21,2] [21,8] [21,20] [22,2] [22,8] [22,20])
 3. EDIT 2 ([0,3] [0,7] [0,15] [1,3] [1,7] [1,15] [2,3] [2,7] [2,15] [3,0] [3,1] [3,2] [3,3] [3,6] [3,7] [3,8] [3,10] [3,12] [3,13] [3,15] [3,16] [3,18] [3,20] [3,21] [3,22] [6,3] [6,7] [6,15] [7,0] [7,1] [7,2] [7,3] [7,6] [7,7] [7,8] [7,10] [7,12] [7,13] [7,15] [7,16] [7,18] [7,20] [7,21] [7,22] [8,3] [8,7] [8,15] [10,3] [10,7] [10,15] [12,3] [12,7] [12,15] [13,3] [13,7] [13,15] [15,0] [15,1] [15,2] [15,3] [15,6] [15,7] [15,8] [15,10] [15,12] [15,13] [15,15] [15,16] [15,18] [15,20] [15,21] [15,22] [16,3] [16,7] [16,15] [18,3] [18,7] [18,15] [20,3] [20,7] [20,15] [21,3] [21,7] [21,15] [22,3] [22,7] [22,15])
 4. EDIT 3 ([0,4] [0,9] [0,14] [0,19] [1,4] [1,9] [1,14] [1,19] [2,4] [2,9] [2,14] [2,19] [3,4] [3,9] [3,14] [3,19] [4,0] [4,1] [4,2] [4,3] [4,4] [4,6] [4,7] [4,8] [4,9] [4,10] [4,12] [4,13] [4,14] [4,15] [4,16] [4,18] [4,19] [4,20] [4,21] [4,22] [6,4] [6,9] [6,14] [6,19] [7,4] [7,9] [7,14] [7,19] [8,4] [8,9] [8,14] [8,19] [9,0] [9,1] [9,2] [9,3] [9,4] [9,6] [9,7] [9,8] [9,9] [9,10] [9,12] [9,13] [9,14] [9,15] [9,16] [9,18] [9,19] [9,20] [9,21] [9,22] [10,4] [10,9] [10,14] [10,19] [12,4] [12,9] [12,14] [12,19] [13,4] [13,9] [13,14] [13,19] [14,0] [14,1] [14,2] [14,3] [14,4] [14,6] [14,7] [14,8] [14,9] [14,10] [14,12] [14,13] [14,20] [14,21] [14,22] [15,4] [15,9] [16,4] [16,9] [18,4] [18,9] [19,0] [19,1] [19,2] [19,3] [19,4] [19,6] [19,7] [19,8] [19,9] [19,10] [19,12] [19,13] [19,20] [19,21] [19,22] [20,4] [20,9] [20,14] [20,19] [21,4] [21,9] [21,14] [21,19] [22,4] [22,9] [22,14] [22,19])
 5. EDIT 4 ([0,5] [0,11] [0,17] [0,23] [1,5] [1,11] [1,17] [1,23] [2,5] [2,11] [2,17] [2,23] [3,5] [3,11] [3,17] [3,23] [4,5] [4,11] [4,17] [4,23] [5,0] [5,1] [5,2] [5,3] [5,4] [5,5] [5,6] [5,7] [5,8] [5,9] [5,10] [5,11] [5,12] [5,13] [5,14] [5,15] [5,16] [5,17] [5,18] [5,19] [5,20] [5,21] [5,22] [5,23] [6,5] [6,11] [6,17] [6,23] [7,5] [7,11] [7,17] [7,23] [8,5] [8,11] [8,17] [8,23] [9,5] [9,11] [9,17] [9,23] [10,5] [10,11] [10,17] [10,23] [11,0] [11,1] [11,2] [11,3] [11,4] [11,5] [11,6] [11,7] [11,8] [11,9] [11,10] [11,11] [11,12] [11,13] [11,14] [11,15] [11,16] [11,17] [11,18] [11,19] [11,20] [11,21] [11,22] [11,23] [12,5] [12,11] [12,17] [12,23] [13,5] [13,11] [13,17] [13,23] [14,5] [14,11] [14,23] [15,5] [15,11] [15,17] [15,23] [16,5] [16,11] [16,17] [16,23] [17,0] [17,1] [17,2] [17,3] [17,4] [17,5] [17,6] [17,7] [17,8] [17,9] [17,10] [17,11] [17,12] [17,13] [17,15] [17,16] [17,17] [17,18] [17,20] [17,21] [17,22] [17,23] [18,5] [18,11] [18,17] [18,23] [19,5] [19,11] [19,23] [20,5] [20,11] [20,17] [20,23] [21,5] [21,11] [21,17] [21,23] [22,5] [22,11] [22,17] [22,23] [23,0] [23,1] [23,2] [23,3] [23,4] [23,5] [23,6] [23,7] [23,8] [23,9] [23,10] [23,11] [23,12] [23,13] [23,14] [23,15] [23,16] [23,17] [23,18] [23,19] [23,20] [23,21] [23,22] [23,23])
 6. EDIT 8 ([14,14] [14,15] [14,16] [14,17] [14,18] [14,19] [15,14] [15,19] [16,14] [16,19] [17,14] [17,19] [18,14] [18,19] [19,14] [19,15] [19,16] [19,17] [19,18] [19,19])} EXTRACT [14,14 19,19]


============================================================

d511f180 - FAUX POSITIF SUSPECTÉ
--------------------------------------------------
Type de scénario: TRANSFERT_SIMPLE
Commande initiale: TRANSFERT
Complexité: 18
Nombre de commandes: 9
Commandes uniques: 2
Contient MOTIF: False
Commandes groupées: False
Indicateurs MOSAIC: multiple_coordinates
Commandes principales: EDIT, {INIT

SCÉNARIO INTELLIGENT (entre INIT/TRANSFERT et END):
----------------------------------------
{INIT 4x4; EDIT 8 ([0,0] [0,1] [1,1] [3,2] [3,3]); EDIT 4 ([0,2] [3,1]); EDIT 5 ([0,3] [1,3]); EDIT 3 ([1,0] [2,0]); EDIT 7 ([1,2] [2,1]); EDIT 1 [2,2]; EDIT 9 [2,3]; EDIT 6 [3,0]} REPLACE 8 5 [0,0 3,3] EDIT 8 ([0,3] [1,3])

DÉTAIL DES COMMANDES:
------------------------------
 1. {INIT 4x4
 2. EDIT 8 ([0,0] [0,1] [1,1] [3,2] [3,3])
 3. EDIT 4 ([0,2] [3,1])
 4. EDIT 5 ([0,3] [1,3])
 5. EDIT 3 ([1,0] [2,0])
 6. EDIT 7 ([1,2] [2,1])
 7. EDIT 1 [2,2]
 8. EDIT 9 [2,3]
 9. EDIT 6 [3,0]} REPLACE 8 5 [0,0 3,3] EDIT 8 ([0,3] [1,3])


============================================================

de1cd16c - FAUX POSITIF SUSPECTÉ
--------------------------------------------------
Type de scénario: INIT
Commande initiale: INIT 1x1
Complexité: 3
Nombre de commandes: 1
Commandes uniques: 1
Contient MOTIF: False
Commandes groupées: False
Indicateurs MOSAIC: 
Commandes principales: EDIT

SCÉNARIO INTELLIGENT (entre INIT/TRANSFERT et END):
----------------------------------------
EDIT 2 [0,0]

DÉTAIL DES COMMANDES:
------------------------------
 1. EDIT 2 [0,0]


============================================================

3631a71a - VRAI MOSAIC PROBABLE
--------------------------------------------------
Type de scénario: TRANSFERT_BLOCK
Commande initiale: TRANSFERT
Complexité: 31
Nombre de commandes: 18
Commandes uniques: 4
Contient MOTIF: False
Commandes groupées: False
Indicateurs MOSAIC: clipboard_PASTE, multiple_coordinates, transform_FLIP
Commandes principales: EDIT, FLIP, PASTE, INIT

SCÉNARIO INTELLIGENT (entre INIT/TRANSFERT et END):
----------------------------------------
INIT 30x30; EDIT 8 ([0,0] [0,13] [0,18] [1,1] [1,12] [1,13] [1,18] [1,19] [2,7] [2,24] [3,25] [4,9] [5,8] [5,9] [6,3] [6,28] [7,2] [7,29] [8,5] [9,4] [9,5] [9,27] [12,1] [13,0] [13,1] [18,0] [18,1] [19,1] [22,4] [22,5] [22,26] [22,27] [23,5] [23,26] [24,2] [24,29] [25,3] [25,28] [26,8] [26,9] [26,22] [26,23] [27,9] [28,6] [28,25] [29,7] [29,24]); EDIT 7 ([0,2] [0,4] [0,5] [0,26] [0,27] [0,29] [1,4] [1,5] [1,26] [1,27] [2,10] [2,11] [2,20] [2,21] [3,10] [3,11] [5,0] [5,1] [10,2] [10,3] [10,28] [10,29] [11,2] [11,3] [11,28] [11,29] [20,2] [20,3] [20,28] [20,29] [21,2] [21,3] [21,28] [21,29] [26,0] [26,1] [27,0] [27,1] [28,10] [28,11] [29,0] [29,10] [29,11] [29,20] [29,21]); EDIT 1 ([0,6] [0,7] [0,24] [0,25] [1,6] [1,7] [1,24] [1,25] [2,26] [2,27] [3,26] [3,27] [4,28] [4,29] [5,2] [5,3] [5,28] [5,29] [6,0] [6,1] [7,0] [7,1] [24,0] [24,1] [25,0] [25,1] [26,2] [26,3] [26,28] [26,29] [27,2] [27,3] [27,28] [27,29] [28,4] [28,5] [28,26] [28,27] [29,4] [29,5] [29,26] [29,27]); EDIT 3 ([0,9] [0,22] [1,8] [1,9] [1,22] [1,23] [8,1] [8,14] [8,17] [9,0] [9,1] [9,15] [9,16] [10,12] [10,19] [11,13] [11,18] [12,10] [12,14] [12,17] [12,21] [13,11] [13,20] [14,8] [14,12] [14,19] [14,23] [15,9] [15,22] [16,9] [16,22] [17,8] [17,12] [17,19] [17,23] [18,11] [18,20] [19,10] [19,14] [19,17] [19,21] [20,13] [20,18] [21,12] [21,19] [22,0] [22,1] [22,15] [22,16] [23,1] [23,14] [23,17]); EDIT 6 ([0,11] [0,20] [1,10] [1,11] [1,20] [1,21] [2,9] [2,15] [2,16] [2,22] [3,8] [3,9] [3,14] [3,17] [4,7] [4,12] [4,13] [4,18] [4,19] [5,6] [5,7] [5,12] [5,13] [5,18] [5,19] [5,25] [6,5] [6,11] [6,26] [7,4] [7,5] [7,10] [7,27] [8,3] [8,12] [8,19] [8,28] [9,2] [9,3] [9,9] [9,28] [9,29] [10,1] [10,7] [10,15] [10,16] [11,0] [11,1] [11,6] [11,14] [11,15] [11,16] [11,17] [12,4] [12,5] [12,8] [12,23] [12,26] [12,27] [13,4] [13,5] [13,26] [13,27] [14,3] [14,11] [14,20] [14,28] [15,2] [15,10] [15,11] [15,20] [15,21] [15,29] [16,2] [16,10] [16,11] [16,20] [16,21] [16,29] [17,3] [17,11] [17,20] [17,28] [18,4] [18,5] [18,26] [18,27] [19,4] [19,5] [19,8] [19,23] [19,26] [19,27] [20,0] [20,1] [20,6] [20,14] [20,15] [20,16] [20,17] [20,25] [21,1] [21,7] [21,15] [21,16] [21,24] [22,2] [22,3] [22,9] [22,22] [22,28] [22,29] [23,3] [23,12] [23,19] [23,28] [24,4] [24,5] [24,10] [24,21] [24,26] [24,27] [25,5] [25,11] [25,20] [25,26] [26,6] [26,7] [26,12] [26,13] [26,18] [26,19] [26,24] [26,25] [27,7] [27,12] [27,13] [28,8] [28,9] [28,14] [28,17] [29,9] [29,15] [29,16] [29,22]); EDIT 9 ([2,0] [2,1] [2,2] [2,3] [2,4] [2,5] [2,6] [3,0] [3,1] [3,2] [3,3] [3,4] [3,5] [3,6] [3,20] [3,21] [3,22] [3,23] [3,24] [4,0] [4,1] [4,2] [4,3] [4,4] [4,5] [4,6] [4,20] [4,21] [4,22] [4,23] [4,24] [5,20] [5,21] [5,22] [5,23] [5,24] [6,20] [6,21] [6,22] [6,23] [6,24] [7,20] [7,21] [7,22] [7,23] [7,24] [7,25] [7,26] [8,22] [8,23] [8,24] [8,25] [8,26] [9,22] [9,23] [9,24] [9,25] [9,26] [10,22] [10,23] [10,24] [10,25] [10,26] [11,22] [11,23] [11,24] [11,25] [11,26] [27,18] [27,19] [27,20] [27,21] [27,22] [27,23] [27,24] [28,18] [28,19] [28,20] [28,21] [28,22] [28,23] [28,24]); EDIT 2 ([2,29] [6,6] [6,25] [7,7] [14,14] [14,17] [15,15] [15,16] [16,15] [16,16] [17,14] [17,17] [24,7] [24,24] [25,6] [25,25] [29,2] [29,29]); EDIT 5 ([5,5] [5,26] [6,14] [6,15] [6,16] [6,17] [7,14] [7,17] [9,11] [9,20] [11,9] [14,6] [14,7] [14,24] [14,25] [15,6] [15,25] [16,6] [16,25] [17,6] [17,7] [17,24] [17,25] [20,9] [20,22] [22,11] [22,20] [24,14] [24,17] [25,14] [25,15] [25,16] [25,17] [26,5] [26,26]); EDIT 4 ([12,13] [12,18] [13,12] [13,19] [18,12] [18,19] [19,13] [19,18])} MOTIF {COPY [27,7 28,13]; FLIP HORIZONTAL [27,7 28,13]; PASTE [27,18]} MOTIF {COPY [27,0 29,6]; FLIP VERTICAL [27,0 29,6]; PASTE [2,0]} MOTIF {COPY [3,7 7,11]; FLIP HORIZONTAL [3,7 7,11]; PASTE [3,20]} MOTIF {COPY [7,5 11,9]; FLIP HORIZONTAL [7,5 11,9]; PASTE [7,22]

DÉTAIL DES COMMANDES:
------------------------------
 1. INIT 30x30
 2. EDIT 8 ([0,0] [0,13] [0,18] [1,1] [1,12] [1,13] [1,18] [1,19] [2,7] [2,24] [3,25] [4,9] [5,8] [5,9] [6,3] [6,28] [7,2] [7,29] [8,5] [9,4] [9,5] [9,27] [12,1] [13,0] [13,1] [18,0] [18,1] [19,1] [22,4] [22,5] [22,26] [22,27] [23,5] [23,26] [24,2] [24,29] [25,3] [25,28] [26,8] [26,9] [26,22] [26,23] [27,9] [28,6] [28,25] [29,7] [29,24])
 3. EDIT 7 ([0,2] [0,4] [0,5] [0,26] [0,27] [0,29] [1,4] [1,5] [1,26] [1,27] [2,10] [2,11] [2,20] [2,21] [3,10] [3,11] [5,0] [5,1] [10,2] [10,3] [10,28] [10,29] [11,2] [11,3] [11,28] [11,29] [20,2] [20,3] [20,28] [20,29] [21,2] [21,3] [21,28] [21,29] [26,0] [26,1] [27,0] [27,1] [28,10] [28,11] [29,0] [29,10] [29,11] [29,20] [29,21])
 4. EDIT 1 ([0,6] [0,7] [0,24] [0,25] [1,6] [1,7] [1,24] [1,25] [2,26] [2,27] [3,26] [3,27] [4,28] [4,29] [5,2] [5,3] [5,28] [5,29] [6,0] [6,1] [7,0] [7,1] [24,0] [24,1] [25,0] [25,1] [26,2] [26,3] [26,28] [26,29] [27,2] [27,3] [27,28] [27,29] [28,4] [28,5] [28,26] [28,27] [29,4] [29,5] [29,26] [29,27])
 5. EDIT 3 ([0,9] [0,22] [1,8] [1,9] [1,22] [1,23] [8,1] [8,14] [8,17] [9,0] [9,1] [9,15] [9,16] [10,12] [10,19] [11,13] [11,18] [12,10] [12,14] [12,17] [12,21] [13,11] [13,20] [14,8] [14,12] [14,19] [14,23] [15,9] [15,22] [16,9] [16,22] [17,8] [17,12] [17,19] [17,23] [18,11] [18,20] [19,10] [19,14] [19,17] [19,21] [20,13] [20,18] [21,12] [21,19] [22,0] [22,1] [22,15] [22,16] [23,1] [23,14] [23,17])
 6. EDIT 6 ([0,11] [0,20] [1,10] [1,11] [1,20] [1,21] [2,9] [2,15] [2,16] [2,22] [3,8] [3,9] [3,14] [3,17] [4,7] [4,12] [4,13] [4,18] [4,19] [5,6] [5,7] [5,12] [5,13] [5,18] [5,19] [5,25] [6,5] [6,11] [6,26] [7,4] [7,5] [7,10] [7,27] [8,3] [8,12] [8,19] [8,28] [9,2] [9,3] [9,9] [9,28] [9,29] [10,1] [10,7] [10,15] [10,16] [11,0] [11,1] [11,6] [11,14] [11,15] [11,16] [11,17] [12,4] [12,5] [12,8] [12,23] [12,26] [12,27] [13,4] [13,5] [13,26] [13,27] [14,3] [14,11] [14,20] [14,28] [15,2] [15,10] [15,11] [15,20] [15,21] [15,29] [16,2] [16,10] [16,11] [16,20] [16,21] [16,29] [17,3] [17,11] [17,20] [17,28] [18,4] [18,5] [18,26] [18,27] [19,4] [19,5] [19,8] [19,23] [19,26] [19,27] [20,0] [20,1] [20,6] [20,14] [20,15] [20,16] [20,17] [20,25] [21,1] [21,7] [21,15] [21,16] [21,24] [22,2] [22,3] [22,9] [22,22] [22,28] [22,29] [23,3] [23,12] [23,19] [23,28] [24,4] [24,5] [24,10] [24,21] [24,26] [24,27] [25,5] [25,11] [25,20] [25,26] [26,6] [26,7] [26,12] [26,13] [26,18] [26,19] [26,24] [26,25] [27,7] [27,12] [27,13] [28,8] [28,9] [28,14] [28,17] [29,9] [29,15] [29,16] [29,22])
 7. EDIT 9 ([2,0] [2,1] [2,2] [2,3] [2,4] [2,5] [2,6] [3,0] [3,1] [3,2] [3,3] [3,4] [3,5] [3,6] [3,20] [3,21] [3,22] [3,23] [3,24] [4,0] [4,1] [4,2] [4,3] [4,4] [4,5] [4,6] [4,20] [4,21] [4,22] [4,23] [4,24] [5,20] [5,21] [5,22] [5,23] [5,24] [6,20] [6,21] [6,22] [6,23] [6,24] [7,20] [7,21] [7,22] [7,23] [7,24] [7,25] [7,26] [8,22] [8,23] [8,24] [8,25] [8,26] [9,22] [9,23] [9,24] [9,25] [9,26] [10,22] [10,23] [10,24] [10,25] [10,26] [11,22] [11,23] [11,24] [11,25] [11,26] [27,18] [27,19] [27,20] [27,21] [27,22] [27,23] [27,24] [28,18] [28,19] [28,20] [28,21] [28,22] [28,23] [28,24])
 8. EDIT 2 ([2,29] [6,6] [6,25] [7,7] [14,14] [14,17] [15,15] [15,16] [16,15] [16,16] [17,14] [17,17] [24,7] [24,24] [25,6] [25,25] [29,2] [29,29])
 9. EDIT 5 ([5,5] [5,26] [6,14] [6,15] [6,16] [6,17] [7,14] [7,17] [9,11] [9,20] [11,9] [14,6] [14,7] [14,24] [14,25] [15,6] [15,25] [16,6] [16,25] [17,6] [17,7] [17,24] [17,25] [20,9] [20,22] [22,11] [22,20] [24,14] [24,17] [25,14] [25,15] [25,16] [25,17] [26,5] [26,26])
10. EDIT 4 ([12,13] [12,18] [13,12] [13,19] [18,12] [18,19] [19,13] [19,18])} MOTIF {COPY [27,7 28,13]
11. FLIP HORIZONTAL [27,7 28,13]
12. PASTE [27,18]} MOTIF {COPY [27,0 29,6]
13. FLIP VERTICAL [27,0 29,6]
14. PASTE [2,0]} MOTIF {COPY [3,7 7,11]
15. FLIP HORIZONTAL [3,7 7,11]
16. PASTE [3,20]} MOTIF {COPY [7,5 11,9]
17. FLIP HORIZONTAL [7,5 11,9]
18. PASTE [7,22]


============================================================

8e1813be - FAUX POSITIF SUSPECTÉ
--------------------------------------------------
Type de scénario: INIT
Commande initiale: INIT 7x7
Complexité: 21
Nombre de commandes: 7
Commandes uniques: 2
Contient MOTIF: False
Commandes groupées: True
Indicateurs MOSAIC: grouped_FILLS
Commandes principales: FILL, FILLS

SCÉNARIO INTELLIGENT (entre INIT/TRANSFERT et END):
----------------------------------------
FILLS {FILL 2 [0,0 0,6]; FILL 3 [1,0 1,6]; FILL 8 [2,0 2,6]; FILL 4 [3,0 3,6]; FILL 6 [4,0 4,6]; FILL 1 [5,0 5,6]; FILL 7 [6,0 6,6]}

DÉTAIL DES COMMANDES:
------------------------------
 1. FILLS {FILL 2 [0,0 0,6]
 2. FILL 3 [1,0 1,6]
 3. FILL 8 [2,0 2,6]
 4. FILL 4 [3,0 3,6]
 5. FILL 6 [4,0 4,6]
 6. FILL 1 [5,0 5,6]
 7. FILL 7 [6,0 6,6]}


============================================================

90c28cc7 - FAUX POSITIF SUSPECTÉ
--------------------------------------------------
Type de scénario: INIT
Commande initiale: INIT 3x3
Complexité: 24
Nombre de commandes: 5
Commandes uniques: 2
Contient MOTIF: False
Commandes groupées: True
Indicateurs MOSAIC: multiple_coordinates, grouped_EDITS
Commandes principales: EDIT, EDITS

SCÉNARIO INTELLIGENT (entre INIT/TRANSFERT et END):
----------------------------------------
EDITS {EDIT 2 ([0,0] [2,0] [2,2]); EDIT 4 ([0,1] [2,1]); EDIT 8 ([1,0] [1,2]); EDIT 1 [0,2]; EDIT 3 [1,1]}

DÉTAIL DES COMMANDES:
------------------------------
 1. EDITS {EDIT 2 ([0,0] [2,0] [2,2])
 2. EDIT 4 ([0,1] [2,1])
 3. EDIT 8 ([1,0] [1,2])
 4. EDIT 1 [0,2]
 5. EDIT 3 [1,1]}


============================================================

91714a58 - FAUX POSITIF SUSPECTÉ
--------------------------------------------------
Type de scénario: TRANSFERT_SIMPLE
Commande initiale: TRANSFERT
Complexité: 19
Nombre de commandes: 10
Commandes uniques: 2
Contient MOTIF: False
Commandes groupées: False
Indicateurs MOSAIC: multiple_coordinates
Commandes principales: EDIT, {INIT

SCÉNARIO INTELLIGENT (entre INIT/TRANSFERT et END):
----------------------------------------
{INIT 16x16; EDIT 1 ([0,2] [0,10] [2,5] [2,8] [4,4] [6,1] [6,2] [8,4] [9,5] [9,14] [11,12] [12,6] [12,14] [13,6]); EDIT 7 ([0,3] [0,14] [2,12] [5,11] [6,15] [7,11] [7,13] [8,10] [13,1] [13,9] [14,6] [14,7]); EDIT 3 ([0,4] [1,3] [3,1] [3,6] [4,10] [5,10] [10,11] [11,2] [12,7] [13,11] [14,5]); EDIT 2 ([0,11] [7,10] [8,3] [12,2] [12,11]); EDIT 4 ([0,13] [2,14] [8,1] [10,3] [11,13]); EDIT 6 ([1,6] [2,0] [11,4] [12,10] [13,7] [13,13] [14,9]); EDIT 8 ([1,7] [2,3] [2,15] [3,2] [3,8] [4,7] [4,11] [5,1] [6,11] [10,1] [14,12] [15,6]); EDIT 5 ([4,0] [4,14] [6,13] [9,15] [10,5] [10,6] [10,7] [10,8] [10,9] [10,10] [11,5] [11,6] [11,7] [11,8] [11,9] [11,10] [12,1] [12,5] [12,15] [15,0]); EDIT 9 ([7,7] [8,0] [8,14] [10,2] [12,0] [14,2] [15,15])} CLEAR (INVERT [10,5 11,10])

DÉTAIL DES COMMANDES:
------------------------------
 1. {INIT 16x16
 2. EDIT 1 ([0,2] [0,10] [2,5] [2,8] [4,4] [6,1] [6,2] [8,4] [9,5] [9,14] [11,12] [12,6] [12,14] [13,6])
 3. EDIT 7 ([0,3] [0,14] [2,12] [5,11] [6,15] [7,11] [7,13] [8,10] [13,1] [13,9] [14,6] [14,7])
 4. EDIT 3 ([0,4] [1,3] [3,1] [3,6] [4,10] [5,10] [10,11] [11,2] [12,7] [13,11] [14,5])
 5. EDIT 2 ([0,11] [7,10] [8,3] [12,2] [12,11])
 6. EDIT 4 ([0,13] [2,14] [8,1] [10,3] [11,13])
 7. EDIT 6 ([1,6] [2,0] [11,4] [12,10] [13,7] [13,13] [14,9])
 8. EDIT 8 ([1,7] [2,3] [2,15] [3,2] [3,8] [4,7] [4,11] [5,1] [6,11] [10,1] [14,12] [15,6])
 9. EDIT 5 ([4,0] [4,14] [6,13] [9,15] [10,5] [10,6] [10,7] [10,8] [10,9] [10,10] [11,5] [11,6] [11,7] [11,8] [11,9] [11,10] [12,1] [12,5] [12,15] [15,0])
10. EDIT 9 ([7,7] [8,0] [8,14] [10,2] [12,0] [14,2] [15,15])} CLEAR (INVERT [10,5 11,10])


============================================================

9edfc990 - FAUX POSITIF SUSPECTÉ
--------------------------------------------------
Type de scénario: TRANSFERT_SIMPLE
Commande initiale: TRANSFERT
Complexité: 19
Nombre de commandes: 10
Commandes uniques: 2
Contient MOTIF: False
Commandes groupées: False
Indicateurs MOSAIC: multiple_coordinates
Commandes principales: EDIT, {INIT

SCÉNARIO INTELLIGENT (entre INIT/TRANSFERT et END):
----------------------------------------
{INIT 16x16; EDIT 8 ([0,3] [1,9] [3,2] [3,10] [5,2] [5,11] [6,4] [6,14] [7,9] [8,12] [9,11] [11,14] [12,8] [12,15] [15,1] [15,12]); EDIT 5 ([0,5] [0,15] [2,6] [3,1] [5,4] [6,13] [8,11] [10,11] [11,3] [12,0] [13,14]); EDIT 9 ([0,8] [3,4] [4,12] [4,13] [4,14] [5,13] [6,0] [6,1] [10,1] [10,4] [11,4] [12,13] [14,1] [14,11] [15,5] [15,13]); EDIT 6 ([0,10] [1,0] [1,2] [1,11] [2,12] [4,10] [5,14] [5,15] [7,4] [9,3] [9,5] [10,13] [11,0] [11,11] [14,0]); EDIT 7 ([1,1] [2,3] [2,11] [3,15] [4,6] [8,4] [11,2] [11,6] [13,9] [14,4] [14,5] [15,6] [15,9]); EDIT 4 ([1,4] [2,7] [3,12] [4,0] [4,3] [4,11] [7,13] [8,2] [8,5] [8,8] [8,14] [9,14] [9,15] [11,10] [12,7] [13,4]); EDIT 2 ([1,6] [1,12] [3,7] [3,8] [4,4] [4,5] [4,8] [5,8] [6,3] [6,10] [11,13] [12,6] [13,3] [14,8] [15,8] [15,10]); EDIT 3 ([1,10] [4,7] [6,7] [7,1] [12,11] [14,14] [15,2]); EDIT 1 ([2,8] [2,10] [5,1] [7,0] [7,3] [7,5] [9,13] [10,10] [12,4] [13,0] [13,8] [14,6] [14,12] [15,0])} FLOODFILL 1 ([9,1] [13,7] [13,1] [8,13] [1,7] [4,1] [5,0] [7,2] [8,3])

DÉTAIL DES COMMANDES:
------------------------------
 1. {INIT 16x16
 2. EDIT 8 ([0,3] [1,9] [3,2] [3,10] [5,2] [5,11] [6,4] [6,14] [7,9] [8,12] [9,11] [11,14] [12,8] [12,15] [15,1] [15,12])
 3. EDIT 5 ([0,5] [0,15] [2,6] [3,1] [5,4] [6,13] [8,11] [10,11] [11,3] [12,0] [13,14])
 4. EDIT 9 ([0,8] [3,4] [4,12] [4,13] [4,14] [5,13] [6,0] [6,1] [10,1] [10,4] [11,4] [12,13] [14,1] [14,11] [15,5] [15,13])
 5. EDIT 6 ([0,10] [1,0] [1,2] [1,11] [2,12] [4,10] [5,14] [5,15] [7,4] [9,3] [9,5] [10,13] [11,0] [11,11] [14,0])
 6. EDIT 7 ([1,1] [2,3] [2,11] [3,15] [4,6] [8,4] [11,2] [11,6] [13,9] [14,4] [14,5] [15,6] [15,9])
 7. EDIT 4 ([1,4] [2,7] [3,12] [4,0] [4,3] [4,11] [7,13] [8,2] [8,5] [8,8] [8,14] [9,14] [9,15] [11,10] [12,7] [13,4])
 8. EDIT 2 ([1,6] [1,12] [3,7] [3,8] [4,4] [4,5] [4,8] [5,8] [6,3] [6,10] [11,13] [12,6] [13,3] [14,8] [15,8] [15,10])
 9. EDIT 3 ([1,10] [4,7] [6,7] [7,1] [12,11] [14,14] [15,2])
10. EDIT 1 ([2,8] [2,10] [5,1] [7,0] [7,3] [7,5] [9,13] [10,10] [12,4] [13,0] [13,8] [14,6] [14,12] [15,0])} FLOODFILL 1 ([9,1] [13,7] [13,1] [8,13] [1,7] [4,1] [5,0] [7,2] [8,3])


============================================================

b9b7f026 - FAUX POSITIF SUSPECTÉ
--------------------------------------------------
Type de scénario: INIT
Commande initiale: INIT 1x1
Complexité: 3
Nombre de commandes: 1
Commandes uniques: 1
Contient MOTIF: False
Commandes groupées: False
Indicateurs MOSAIC: 
Commandes principales: EDIT

SCÉNARIO INTELLIGENT (entre INIT/TRANSFERT et END):
----------------------------------------
EDIT 7 [0,0]

DÉTAIL DES COMMANDES:
------------------------------
 1. EDIT 7 [0,0]


============================================================

d687bc17 - FAUX POSITIF SUSPECTÉ
--------------------------------------------------
Type de scénario: TRANSFERT_SIMPLE
Commande initiale: TRANSFERT
Complexité: 20
Nombre de commandes: 11
Commandes uniques: 2
Contient MOTIF: False
Commandes groupées: False
Indicateurs MOSAIC: multiple_coordinates
Commandes principales: EDIT, {INIT

SCÉNARIO INTELLIGENT (entre INIT/TRANSFERT et END):
----------------------------------------
{INIT 17x14; EDIT 4 ([0,1] [0,2] [0,3] [0,4] [0,5] [0,6] [0,7] [0,8] [0,9] [0,10] [0,11] [0,12] [0,13] [0,14] [0,15] [9,8] [9,14]); EDIT 1 ([1,0] [2,0] [2,12] [3,0] [4,0] [5,0] [5,6] [6,0] [7,0] [8,0] [9,0] [10,0] [10,11] [11,0] [12,0]); EDIT 2 ([1,16] [2,16] [3,3] [3,16] [4,16] [5,16] [6,16] [7,4] [7,16] [8,16] [9,16] [10,16] [11,16] [12,16]); EDIT 8 ([2,7] [5,13] [10,3] [13,1] [13,2] [13,3] [13,4] [13,5] [13,6] [13,7] [13,8] [13,9] [13,10] [13,11] [13,12] [13,13] [13,14] [13,15]); EDIT 3 [3,14]; EDIT 7 [5,10]; EDIT 6 [9,5]} EDITS {EDIT 4 ([1,8] [1,14]); EDIT 2 ([3,15] [7,15]); EDIT 8 ([12,13] [12,7] [12,3]); EDIT 1 ([10,1] [5,1] [2,1])} CLEAR [2,2 11,14]

DÉTAIL DES COMMANDES:
------------------------------
 1. {INIT 17x14
 2. EDIT 4 ([0,1] [0,2] [0,3] [0,4] [0,5] [0,6] [0,7] [0,8] [0,9] [0,10] [0,11] [0,12] [0,13] [0,14] [0,15] [9,8] [9,14])
 3. EDIT 1 ([1,0] [2,0] [2,12] [3,0] [4,0] [5,0] [5,6] [6,0] [7,0] [8,0] [9,0] [10,0] [10,11] [11,0] [12,0])
 4. EDIT 2 ([1,16] [2,16] [3,3] [3,16] [4,16] [5,16] [6,16] [7,4] [7,16] [8,16] [9,16] [10,16] [11,16] [12,16])
 5. EDIT 8 ([2,7] [5,13] [10,3] [13,1] [13,2] [13,3] [13,4] [13,5] [13,6] [13,7] [13,8] [13,9] [13,10] [13,11] [13,12] [13,13] [13,14] [13,15])
 6. EDIT 3 [3,14]
 7. EDIT 7 [5,10]
 8. EDIT 6 [9,5]} EDITS {EDIT 4 ([1,8] [1,14])
 9. EDIT 2 ([3,15] [7,15])
10. EDIT 8 ([12,13] [12,7] [12,3])
11. EDIT 1 ([10,1] [5,1] [2,1])} CLEAR [2,2 11,14]


============================================================

