#!/usr/bin/env python3
"""
Vérification des résultats du scan sur les 8 tâches MOSAIC validées
"""

import json
import os
from detect_mosaic_pattern import detect_mosaic_pattern
import numpy as np

def load_arc_puzzle(puzzle_id, data_dir="../arcdata/training"):
    """Charge un puzzle ARC depuis les fichiers JSON"""
    json_file = os.path.join(data_dir, f"{puzzle_id}.json")
    
    try:
        with open(json_file, 'r') as f:
            return json.load(f)
    except Exception as e:
        print(f"Erreur chargement {puzzle_id}: {e}")
        return None

def analyze_validated_mosaics():
    """Analyse les 8 tâches MOSAIC validées"""
    
    # Les 8 tâches validées comme vrais MOSAIC
    validated_mosaic_ids = [
        "0dfd9992",  # Confirmé
        "29ec7d0e",  # Confirmé mais ensemble de petite mosaic
        "3631a71a",  # Confirmé mais mosaic non centré dans le cadre
        "484b58aa",  # Confirmé
        "9ecd008a",  # Confirmé
        "b8825c91",  # Confirm<PERSON>
        "c3f564a4",  # Confirmé
        "dc0a314f"   # Confirmé
    ]
    
    print("🎨 RÉSULTATS SUR LES 8 TÂCHES MOSAIC VALIDÉES")
    print("=" * 60)
    
    for puzzle_id in validated_mosaic_ids:
        print(f"\n🔍 ANALYSE DE {puzzle_id}")
        print("-" * 40)
        
        # Charger le puzzle
        puzzle_data = load_arc_puzzle(puzzle_id)
        if not puzzle_data:
            print("❌ Impossible de charger le puzzle")
            continue
        
        # Analyser tous les exemples
        total_mosaics = 0
        max_confidence = 0
        
        # Exemples d'entraînement
        for i, example in enumerate(puzzle_data.get('train', [])):
            input_grid = np.array(example['input'])
            output_grid = np.array(example['output'])
            
            # Analyser input
            input_result = detect_mosaic_pattern(input_grid)
            if input_result['is_mosaic']:
                total_mosaics += 1
                max_confidence = max(max_confidence, input_result['confidence'])
                print(f"  ✅ train{i}_input ({input_grid.shape[0]}x{input_grid.shape[1]}): {input_result['confidence']:.1%}")
                print(f"     Preuves: {', '.join(input_result['evidence'][:2])}")
            else:
                print(f"  ❌ train{i}_input ({input_grid.shape[0]}x{input_grid.shape[1]}): {input_result['confidence']:.1%} (non détecté)")
            
            # Analyser output
            output_result = detect_mosaic_pattern(output_grid)
            if output_result['is_mosaic']:
                total_mosaics += 1
                max_confidence = max(max_confidence, output_result['confidence'])
                print(f"  ✅ train{i}_output ({output_grid.shape[0]}x{output_grid.shape[1]}): {output_result['confidence']:.1%}")
                print(f"     Preuves: {', '.join(output_result['evidence'][:2])}")
            else:
                print(f"  ❌ train{i}_output ({output_grid.shape[0]}x{output_grid.shape[1]}): {output_result['confidence']:.1%} (non détecté)")
        
        # Exemples de test
        for i, example in enumerate(puzzle_data.get('test', [])):
            input_grid = np.array(example['input'])
            
            # Analyser input de test
            input_result = detect_mosaic_pattern(input_grid)
            if input_result['is_mosaic']:
                total_mosaics += 1
                max_confidence = max(max_confidence, input_result['confidence'])
                print(f"  ✅ test{i}_input ({input_grid.shape[0]}x{input_grid.shape[1]}): {input_result['confidence']:.1%}")
                print(f"     Preuves: {', '.join(input_result['evidence'][:2])}")
            else:
                print(f"  ❌ test{i}_input ({input_grid.shape[0]}x{input_grid.shape[1]}): {input_result['confidence']:.1%} (non détecté)")
            
            # Analyser output de test s'il existe
            if 'output' in example:
                output_grid = np.array(example['output'])
                output_result = detect_mosaic_pattern(output_grid)
                if output_result['is_mosaic']:
                    total_mosaics += 1
                    max_confidence = max(max_confidence, output_result['confidence'])
                    print(f"  ✅ test{i}_output ({output_grid.shape[0]}x{output_grid.shape[1]}): {output_result['confidence']:.1%}")
                    print(f"     Preuves: {', '.join(output_result['evidence'][:2])}")
                else:
                    print(f"  ❌ test{i}_output ({output_grid.shape[0]}x{output_grid.shape[1]}): {output_result['confidence']:.1%} (non détecté)")
        
        # Résumé pour ce puzzle
        if total_mosaics > 0:
            print(f"  📊 RÉSUMÉ: {total_mosaics} mosaïques détectées, confiance max: {max_confidence:.1%}")
        else:
            print(f"  📊 RÉSUMÉ: Aucune mosaïque détectée dans ce puzzle !")

def check_scan_results():
    """Vérifie les résultats du scan complet pour les 8 tâches validées"""
    
    validated_mosaic_ids = [
        "0dfd9992", "29ec7d0e", "3631a71a", "484b58aa", 
        "9ecd008a", "b8825c91", "c3f564a4", "dc0a314f"
    ]
    
    print(f"\n📋 VÉRIFICATION DANS LES RÉSULTATS DU SCAN COMPLET")
    print("=" * 60)
    
    # Charger les résultats du scan
    try:
        with open("arc_mosaic_scan_results.json", 'r') as f:
            scan_results = json.load(f)
    except:
        print("❌ Impossible de charger arc_mosaic_scan_results.json")
        return
    
    # Grouper par puzzle_id
    scan_by_puzzle = {}
    for result in scan_results:
        puzzle_id = result['puzzle_id']
        if puzzle_id not in scan_by_puzzle:
            scan_by_puzzle[puzzle_id] = []
        scan_by_puzzle[puzzle_id].append(result)
    
    # Vérifier chaque puzzle validé
    detected_count = 0
    for puzzle_id in validated_mosaic_ids:
        if puzzle_id in scan_by_puzzle:
            detected_count += 1
            results = scan_by_puzzle[puzzle_id]
            max_conf = max(r['confidence'] for r in results)
            print(f"✅ {puzzle_id}: {len(results)} mosaïques détectées (max: {max_conf:.1%})")
            
            # Détail des détections
            for result in results:
                grid_info = f"{result['example_type']}{result['example_index']}_{result['grid_type']}"
                shape_info = f"{result['grid_shape'][0]}x{result['grid_shape'][1]}"
                print(f"   • {grid_info} ({shape_info}): {result['confidence']:.1%}")
        else:
            print(f"❌ {puzzle_id}: Aucune mosaïque détectée")
    
    print(f"\n📊 BILAN: {detected_count}/8 puzzles validés détectés dans le scan ({detected_count/8*100:.1f}%)")

def detailed_analysis_one_puzzle(puzzle_id="0dfd9992"):
    """Analyse détaillée d'un puzzle spécifique"""
    
    print(f"\n🔬 ANALYSE DÉTAILLÉE DE {puzzle_id}")
    print("=" * 50)
    
    puzzle_data = load_arc_puzzle(puzzle_id)
    if not puzzle_data:
        print("❌ Impossible de charger le puzzle")
        return
    
    # Analyser le premier exemple d'entraînement
    if puzzle_data.get('train'):
        example = puzzle_data['train'][0]
        input_grid = np.array(example['input'])
        output_grid = np.array(example['output'])
        
        print(f"📋 Exemple train0:")
        print(f"   Input: {input_grid.shape}")
        print(f"   Output: {output_grid.shape}")
        
        # Analyse détaillée de l'input
        print(f"\n🔍 ANALYSE INPUT:")
        input_result = detect_mosaic_pattern(input_grid)
        print(f"   Est mosaïque: {input_result['is_mosaic']}")
        print(f"   Confiance: {input_result['confidence']:.1%}")
        print(f"   Preuves:")
        for evidence in input_result['evidence']:
            print(f"     • {evidence}")
        print(f"   Blocs de motifs: {len(input_result['pattern_blocks'])}")
        print(f"   Régions vides: {len(input_result['empty_regions'])}")
        print(f"   Symétries: {len(input_result['symmetries'])}")
        
        # Afficher la grille input
        print(f"\n📊 GRILLE INPUT:")
        for row in input_grid:
            print("   " + " ".join(f"{cell:2d}" for cell in row))
        
        # Analyse détaillée de l'output
        print(f"\n🔍 ANALYSE OUTPUT:")
        output_result = detect_mosaic_pattern(output_grid)
        print(f"   Est mosaïque: {output_result['is_mosaic']}")
        print(f"   Confiance: {output_result['confidence']:.1%}")
        print(f"   Preuves:")
        for evidence in output_result['evidence']:
            print(f"     • {evidence}")
        print(f"   Blocs de motifs: {len(output_result['pattern_blocks'])}")
        print(f"   Régions vides: {len(output_result['empty_regions'])}")
        print(f"   Symétries: {len(output_result['symmetries'])}")

def main():
    """Point d'entrée principal"""
    
    # Analyse directe des 8 puzzles validés
    analyze_validated_mosaics()
    
    # Vérification dans les résultats du scan
    check_scan_results()
    
    # Analyse détaillée d'un exemple
    detailed_analysis_one_puzzle("0dfd9992")

if __name__ == "__main__":
    main()