#!/usr/bin/env python3
"""
Debug de la détection tiling pour 007bbfb7
"""

import json
import numpy as np
from analyze_grid.arc_analyzer import ARCAnalyzer

def debug_007bbfb7():
    """Debug détaillé pour 007bbfb7"""
    print("🔍 DEBUG TILING 007bbfb7")
    
    # Charger le puzzle
    with open('../arcdata/training/007bbfb7.json', 'r') as f:
        puzzle = json.load(f)
    
    analyzer = ARCAnalyzer()
    
    print(f"Nombre d'exemples: {len(puzzle['train'])}")
    
    for i, example in enumerate(puzzle['train']):
        input_grid = np.array(example['input'])
        output_grid = np.array(example['output'])
        
        print(f"\nExemple {i}:")
        print(f"  Input shape: {input_grid.shape}")
        print(f"  Output shape: {output_grid.shape}")
        
        in_h, in_w = input_grid.shape
        out_h, out_w = output_grid.shape
        
        if out_h % in_h == 0 and out_w % in_w == 0:
            h_factor = out_h // in_h
            w_factor = out_w // in_w
            print(f"  Facteurs: ({h_factor}, {w_factor})")
            
            # Test de validation
            is_valid = analyzer.validate_tiling_permissive(input_grid, output_grid, h_factor, w_factor)
            print(f"  Tiling valide: {is_valid}")
            
            # Analyser les couleurs
            input_colors = set(input_grid.flatten())
            output_colors = set(output_grid.flatten())
            print(f"  Couleurs input: {input_colors}")
            print(f"  Couleurs output: {output_colors}")
            
            # Tester chaque tile
            print("  Analyse des tiles:")
            for ti in range(h_factor):
                for tj in range(w_factor):
                    start_i, start_j = ti * in_h, tj * in_w
                    end_i, end_j = start_i + in_h, start_j + in_w
                    
                    tile = output_grid[start_i:end_i, start_j:end_j]
                    tile_colors = set(tile.flatten())
                    
                    color_overlap = len(input_colors.intersection(tile_colors)) / len(input_colors.union(tile_colors))
                    print(f"    Tile ({ti},{tj}): couleurs {tile_colors}, overlap {color_overlap:.2f}")
        else:
            print(f"  Pas de multiple exact")
    
    # Test complet
    result = analyzer.detect_tiling_motif(puzzle)
    print(f"\nRésultat final: {result}")

if __name__ == "__main__":
    debug_007bbfb7()