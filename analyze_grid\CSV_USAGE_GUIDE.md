# 📊 Guide d'Utilisation du Fichier CSV des Métriques ARC

## 🎯 Vue d'Ensemble

Le fichier `arc_complete_metrics.csv` contient **76 colonnes de métriques** pour **401 puzzles ARC**, extrait avec l'ARCAnalyzer. C'est votre base de données complète pour l'analyse approfondie des puzzles.

## 📋 Structure du Fichier

### Informations Générales
- **401 lignes** (puzzles)
- **76 colonnes** (75 métriques + ID)
- **Taille** : 0.26 MB
- **Format** : CSV avec headers
- **Encodage** : UTF-8

## 🔍 Catégories de Métriques

### 1. Identification
- `puzzle_id` : Identifiant unique du puzzle

### 2. Métriques de Base des Grilles (27 colonnes)
```
Dimensions:
- min/max/avg_input_height, min/max/avg_input_width
- min/max/avg_input_area
- min/max/avg_output_height, min/max/avg_output_width  
- min/max/avg_output_area

Changements:
- min/max/avg_height_change, min/max/avg_width_change
- min/max/avg_height_ratio, min/max/avg_width_ratio
- consistent_dimension_change

Couleurs:
- min/max/avg_input_colors, min/max/avg_output_colors
```

### 3. Métriques d'Utilisation des Couleurs (9 colonnes)
```
Usage:
- min/max/avg_color_usage_ratio (% cellules colorées vs fond)

Équilibre:
- min/max/avg_color_balance_score (distribution équilibrée)

Dominance:
- min/max/avg_dominant_color_ratio (couleur la plus fréquente)
```

### 4. Métriques ARCAnalyzer (15 colonnes)
```
Complexité:
- grid_complexity, color_complexity, object_complexity
- transformation_complexity, overall_complexity

Couleurs globales:
- total_input_colors, total_output_colors, color_change
```

### 5. Symétries (6 colonnes)
```
- horizontal_symmetries, vertical_symmetries
- diagonal_main_symmetries, diagonal_anti_symmetries
- total_symmetries, has_any_symmetry
```

### 6. Patterns Détectés (8 colonnes)
```
- repetition_detected, repetition_type, repetition_factor
- scaling_detected, scaling_consistent
- motif_detected, motif_type, motif_confidence
```

### 7. Transformations (4 colonnes)
```
- geometric_transforms, color_transforms
- structural_transforms, total_transforms
```

### 8. Score MOSAIC (2 colonnes)
```
- mosaic_score (0.0 à 1.0)
- is_mosaic_candidate (boolean)
```

## 📈 Statistiques Clés

### Distribution des Valeurs
```
Taille des grilles:
- Min: 5 cellules, Max: 900 cellules
- Moyenne: 167.8 cellules

Couleurs:
- Min: 0 couleurs, Max: 9 couleurs  
- Moyenne: 2.9 couleurs

Usage des couleurs:
- Min: 0%, Max: 100%
- Moyenne: 37.3%

Complexité globale:
- Min: 4.75, Max: 772.6
- Moyenne: 61.4
```

### Patterns Détectés
- **MOTIF** : 282 puzzles (70.3%)
- **SCALING** : 69 puzzles (17.2%)
- **REPETITION** : 7 puzzles (1.7%)

### Symétries
- **72.6%** des puzzles n'ont aucune symétrie
- **27.4%** ont au moins une symétrie

### Puzzles MOSAIC
- **27 puzzles** identifiés (6.7%)
- Score moyen : 0.364

## 🔧 Exemples d'Utilisation

### Python/Pandas
```python
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

# Charger les données
df = pd.read_csv('arc_complete_metrics.csv')

# Analyser les puzzles MOSAIC
mosaic_puzzles = df[df['is_mosaic_candidate'] == True]
print(f"Puzzles MOSAIC: {len(mosaic_puzzles)}")

# Corrélation taille vs couleurs
correlation = df['max_input_area'].corr(df['max_input_colors'])
print(f"Corrélation taille-couleurs: {correlation:.3f}")

# Distribution de la complexité
plt.hist(df['overall_complexity'], bins=50)
plt.title('Distribution de la Complexité')
plt.show()

# Clustering des puzzles
from sklearn.cluster import KMeans
features = ['max_input_area', 'max_input_colors', 'avg_color_usage_ratio', 'overall_complexity']
X = df[features].fillna(0)
kmeans = KMeans(n_clusters=5)
df['cluster'] = kmeans.fit_predict(X)
```

### R
```r
# Charger les données
df <- read.csv('arc_complete_metrics.csv')

# Analyse descriptive
summary(df)

# Corrélations
cor_matrix <- cor(df[sapply(df, is.numeric)], use="complete.obs")

# Visualisation
library(ggplot2)
ggplot(df, aes(x=max_input_area, y=max_input_colors)) +
  geom_point(aes(color=is_mosaic_candidate)) +
  labs(title="Taille vs Couleurs des Puzzles ARC")

# Analyse des composantes principales
pca <- prcomp(df[sapply(df, is.numeric)], scale=TRUE, na.action=na.omit)
summary(pca)
```

### Excel/Google Sheets
1. **Importer** le fichier CSV
2. **Filtrer** par `is_mosaic_candidate = TRUE`
3. **Créer des graphiques** : 
   - Histogramme de `overall_complexity`
   - Nuage de points `max_input_area` vs `max_input_colors`
4. **Tableaux croisés dynamiques** pour analyser les patterns

## 🎯 Analyses Recommandées

### 1. Identification de Nouveaux Types de Puzzles
```python
# Clustering pour identifier des groupes
features = ['max_input_area', 'max_input_colors', 'avg_color_usage_ratio', 
           'overall_complexity', 'total_symmetries']
```

### 2. Prédiction de Difficulté
```python
# Modèle de régression pour prédire la complexité
target = 'overall_complexity'
features = ['max_input_area', 'max_input_colors', 'total_transforms']
```

### 3. Classification des Patterns
```python
# Classification supervisée des types de patterns
# Utiliser motif_detected, scaling_detected, repetition_detected
```

### 4. Analyse des Outliers
```python
# Identifier les puzzles avec des métriques extrêmes
outliers = df[(df['overall_complexity'] > df['overall_complexity'].quantile(0.95)) |
              (df['max_input_area'] > df['max_input_area'].quantile(0.95))]
```

### 5. Corrélations Avancées
```python
# Matrice de corrélation complète
import seaborn as sns
numeric_cols = df.select_dtypes(include=[np.number]).columns
correlation_matrix = df[numeric_cols].corr()
sns.heatmap(correlation_matrix, annot=False, cmap='coolwarm')
```

## ⚠️ Notes Importantes

### Valeurs Manquantes
- `repetition_type` : 394 valeurs manquantes (normal, peu de répétitions)
- `repetition_factor` : 394 valeurs manquantes (normal)
- `motif_type` : 119 valeurs manquantes (30% des puzzles sans MOTIF)

### Validation des Données
✅ **Toutes les validations passées** :
- Aires cohérentes (min ≤ max)
- Couleurs dans les limites (0-9)
- Ratios d'usage valides (0-1)

## 🔗 Corrélations Fortes Identifiées

### Corrélations Parfaites (r = 1.000)
- `transformation_complexity ↔ total_transforms`
- Métriques de changement de hauteur avec `repetition_factor`

### Corrélations Très Fortes (r > 0.98)
- `min_color_usage_ratio ↔ avg_color_usage_ratio` (0.987)
- `max_color_usage_ratio ↔ avg_color_usage_ratio` (0.986)

## 📁 Fichiers Complémentaires

- `arc_metrics_summary.txt` : Résumé statistique détaillé
- `analyze_csv_metrics.py` : Script d'analyse du CSV
- `extract_all_metrics_csv.py` : Script de génération du CSV

## 🎯 Objectifs d'Analyse

Ce fichier CSV vous permet de :

1. **Catégoriser** les puzzles selon leurs caractéristiques
2. **Identifier** de nouveaux types de puzzles (comme MOSAIC)
3. **Prédire** la difficulté ou le type de stratégie nécessaire
4. **Optimiser** votre IA en adaptant les approches par catégorie
5. **Découvrir** des patterns cachés dans les données ARC

---

**Le fichier CSV est votre base de données complète pour l'analyse data-driven des puzzles ARC. Utilisez-le pour découvrir de nouveaux insights et améliorer votre système de résolution automatique.**