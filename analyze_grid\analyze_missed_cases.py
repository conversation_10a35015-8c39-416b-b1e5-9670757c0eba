#!/usr/bin/env python3
"""
Analyse des cas MOTIF non détectés pour identifier les patterns manqués
"""

import json
import os
import numpy as np
from arc_analyzer import ARCAnalyzer  # Correction du nom du module

def analyze_missed_case(puzzle_id: str):
    """Analyse détaillée d'un cas non détecté"""
    print(f"\n🔍 ANALYSE DE {puzzle_id}")
    print("-" * 30)
    
    # Charger puzzle et solution
    json_file = f"../arcdata/training/{puzzle_id}.json"
    agi_file = f"../arcdata/training/{puzzle_id}_TEST0_VALID.agi"
    
    if not os.path.exists(json_file):
        print("❌ Fichier JSON manquant")
        return
    
    with open(json_file, 'r') as f:
        puzzle = json.load(f)
    
    # Lire solution AGI
    agi_commands = []
    if os.path.exists(agi_file):
        content = ""  # Initialize default value
        if os.path.exists(agi_file):
            with open(agi_file, 'r', encoding='utf-8') as f:
                content = f.read()
            print(f"Solution AGI:")
            for line in content.split('\n'):
                if line.strip():
                    print(f"  {line}")
                if 'MOTIF' in line:
                    agi_commands.append(line.strip())
    
    # Analyser dimensions
    print(f"\nDimensions:")
    for i, example in enumerate(puzzle['train']):
        input_shape = (len(example['input']), len(example['input'][0]))
        output_shape = (len(example['output']), len(example['output'][0]))
        ratio = (output_shape[0] / input_shape[0], output_shape[1] / input_shape[1])
        print(f"  Ex{i}: {input_shape} → {output_shape} (ratio {ratio[0]:.1f}x{ratio[1]:.1f})")
    
    # Test détection actuelle
    analyzer = ARCAnalyzer()
    analysis = analyzer.analyze_puzzle(puzzle)
    motif_result = analysis['patterns']['motif']
    
    print(f"\nDétection actuelle:")
    print(f"  Détecté: {motif_result['detected']}")
    print(f"  Type: {motif_result['type']}")
    print(f"  Confiance: {motif_result['confidence']}")
    
    # Analyser pourquoi non détecté
    print(f"\nAnalyse des causes:")
    
    # Test tiling
    tiling_result = analyzer.detect_tiling_motif(puzzle)
    print(f"  Tiling: détecté={tiling_result['detected']}, conf={tiling_result['confidence']}")
    
    # Test détaché
    detached_result = analyzer.detect_detached_motif(puzzle)
    print(f"  Détaché: détecté={detached_result['detected']}, conf={detached_result['confidence']}")
    
    # Analyser les couleurs
    example = puzzle['train'][0]
    input_grid = np.array(example['input'])
    output_grid = np.array(example['output'])
    
    from collections import Counter
    input_colors = Counter(input_grid.flatten())
    output_colors = Counter(output_grid.flatten())
    
    print(f"  Couleurs input: {dict(input_colors)}")
    print(f"  Couleurs output: {dict(output_colors)}")
    
    # Identifier le pattern manqué
    print(f"\nPattern identifié:")
    if 'COPY [' in str(agi_commands):
        print("  → MOTIF par coordonnées directes (pas de COLOR)")
    elif 'COLOR 0' in str(agi_commands):
        print("  → MOTIF par sélection fond (COLOR 0)")
    content = ""  # Initialisation avant utilisation
    if os.path.exists(agi_file):
        with open(agi_file, 'r', encoding='utf-8') as f:
            content = f.read()
    if content and 'RESIZE' in content:  # Ensure content is defined before checking
        print("  → MOTIF avec redimensionnement")
    else:
        print("  → Pattern non identifié")

def main():
    """Analyse des cas manqués principaux"""
    print("🔍 ANALYSE DES CAS MOTIF NON DÉTECTÉS")
    print("=" * 50)
    
    # Cas identifiés comme non détectés
    missed_cases = [
        "017c7c7b",  # MOTIF {COPY [3,0 5,2]; PASTE [6,0]}
        "0520fde7",  # MOTIF {COPY (COLOR 0 [0,4 2,6]); PASTE [0,0]}
        "0dfd9992",  # Multiples MOTIF
        "1b2d62fb",  # MOTIF {COPY (COLOR 9 [0,4 4,6]); PASTE [0,0]}
        "228f6490",  # MOTIF avec COLOR multiple
        "264363fd"   # Pattern complexe
    ]
    
    for puzzle_id in missed_cases:
        analyze_missed_case(puzzle_id)
    
    print(f"\n📋 PATTERNS MANQUÉS IDENTIFIÉS:")
    print("1. MOTIF par coordonnées directes (sans COLOR)")
    print("2. MOTIF avec redimensionnement (RESIZE)")
    print("3. MOTIF multiples dans même puzzle")
    print("4. Seuils trop stricts pour détection fond")
    print("5. Ratios non-entiers (1.5x1.0, etc.)")

if __name__ == "__main__":
    main()