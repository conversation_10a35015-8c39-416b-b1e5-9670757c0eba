#!/usr/bin/env python3
"""
Détecteur unifié de puzzles MOSAIC basé sur l'analyse visuelle
Combine les meilleures approches des deux programmes précédents
"""

import numpy as np
from collections import Counter, defaultdict
from typing import Dict, List, Tuple, Optional
import json

class UnifiedMosaicDetector:
    """
    Détecteur unifié qui combine détection de patterns et résolution
    """
    
    def __init__(self, empty_value=0, min_block_size=2):
        self.empty_value = empty_value
        self.min_block_size = min_block_size
        
        # Transformations géométriques possibles
        self.transformations = {
            'identity': lambda x: x,
            'flip_horizontal': lambda x: np.fliplr(x),
            'flip_vertical': lambda x: np.flipud(x),
            'rotate_90': lambda x: np.rot90(x, 1),
            'rotate_180': lambda x: np.rot90(x, 2),
            'rotate_270': lambda x: np.rot90(x, 3),
        }
    
    def detect_and_solve(self, grid) -> Dict:
        """
        Détection et résolution complète d'une mosaïque
        
        Returns:
            dict: Résultats complets avec détection, analyse et solution
        """
        grid = np.array(grid)
        h, w = grid.shape
        
        results = {
            'is_mosaic': False,
            'confidence': 0.0,
            'detection_evidence': [],
            'source_blocks': [],
            'target_regions': [],
            'transformations': [],
            'solved_grid': None,
            'agi_commands': [],
            'analysis_summary': {}
        }
        
        # Phase 1: Détection des caractéristiques MOSAIC
        detection_results = self._detect_mosaic_characteristics(grid)
        results.update(detection_results)
        
        # Phase 2: Si c'est une mosaïque potentielle, analyser les transformations
        if results['confidence'] > 0.3:  # Seuil bas pour l'analyse
            transformation_results = self._analyze_transformations(grid)
            results.update(transformation_results)
            
            # Phase 3: Générer la solution et les commandes AGI
            if results['transformations']:
                solution_results = self._generate_solution(grid, results['transformations'])
                results.update(solution_results)
        
        # Phase 4: Score final et classification
        final_confidence = self._calculate_final_confidence(results)
        results['confidence'] = final_confidence
        results['is_mosaic'] = final_confidence > 0.6
        
        return results
    
    def _detect_mosaic_characteristics(self, grid) -> Dict:
        """Phase 1: Détection des caractéristiques visuelles d'une mosaïque"""
        h, w = grid.shape
        evidence = []
        confidence = 0.0
        
        # 1. Analyser la distribution des valeurs
        unique_vals, counts = np.unique(grid, return_counts=True)
        value_dist = dict(zip(unique_vals, counts))
        
        # Ratio de zones vides
        empty_ratio = value_dist.get(self.empty_value, 0) / (h * w)
        if 0.1 < empty_ratio < 0.7:  # Entre 10% et 70% de vide
            evidence.append(f"Ratio de vide approprié: {empty_ratio:.1%}")
            confidence += 0.2
        
        # 2. Détecter les blocs rectangulaires cohérents
        source_blocks = self._find_coherent_blocks(grid)
        if len(source_blocks) >= 2:
            evidence.append(f"Trouvé {len(source_blocks)} blocs de motifs cohérents")
            confidence += min(len(source_blocks) * 0.1, 0.3)
        
        # 3. Détecter les régions vides rectangulaires
        target_regions = self._find_empty_rectangles(grid)
        if len(target_regions) >= 1:
            evidence.append(f"Trouvé {len(target_regions)} régions vides rectangulaires")
            confidence += min(len(target_regions) * 0.15, 0.3)
        
        # 4. Analyser la structure géométrique globale
        geometric_score = self._analyze_geometric_structure(grid, source_blocks, target_regions)
        if geometric_score > 0.3:
            evidence.append(f"Structure géométrique cohérente (score: {geometric_score:.2f})")
            confidence += geometric_score * 0.2
        
        return {
            'detection_evidence': evidence,
            'confidence': confidence,
            'source_blocks': source_blocks,
            'target_regions': target_regions
        }
    
    def _find_coherent_blocks(self, grid) -> List[Dict]:
        """Trouve les blocs rectangulaires cohérents (non-vides)"""
        h, w = grid.shape
        blocks = []
        visited = np.zeros_like(grid, dtype=bool)
        
        for i in range(h):
            for j in range(w):
                if not visited[i, j] and grid[i, j] != self.empty_value:
                    block = self._extract_coherent_rectangle(grid, i, j, visited)
                    if block and self._is_significant_block(block):
                        blocks.append(block)
        
        return blocks
    
    def _extract_coherent_rectangle(self, grid, start_i, start_j, visited) -> Optional[Dict]:
        """Extrait le plus grand rectangle cohérent possible"""
        h, w = grid.shape
        
        # Stratégie adaptative : essayer différentes tailles
        best_block = None
        max_coherence = 0
        
        # Limiter la recherche pour éviter la complexité excessive
        max_search_h = min(h - start_i, 10)
        max_search_w = min(w - start_j, 10)
        
        for end_i in range(start_i + self.min_block_size, start_i + max_search_h + 1):
            for end_j in range(start_j + self.min_block_size, start_j + max_search_w + 1):
                if self._can_form_block(grid, start_i, start_j, end_i, end_j, visited):
                    region = grid[start_i:end_i, start_j:end_j]
                    coherence = self._calculate_block_coherence(region)
                    
                    if coherence > max_coherence:
                        max_coherence = coherence
                        best_block = {
                            'coords': (start_i, start_j, end_i, end_j),
                            'pattern': region.copy(),
                            'size': (end_i - start_i, end_j - start_j),
                            'area': (end_i - start_i) * (end_j - start_j),
                            'coherence': coherence
                        }
        
        if best_block:
            # Marquer comme visité
            r1, c1, r2, c2 = best_block['coords']
            visited[r1:r2, c1:c2] = True
        
        return best_block
    
    def _can_form_block(self, grid, r1, c1, r2, c2, visited) -> bool:
        """Vérifie si une région peut former un bloc cohérent"""
        # Pas déjà visitée
        if np.any(visited[r1:r2, c1:c2]):
            return False
        
        # Pas trop de valeurs vides
        region = grid[r1:r2, c1:c2]
        empty_ratio = np.sum(region == self.empty_value) / region.size
        return empty_ratio < 0.2  # Moins de 20% de vide
    
    def _calculate_block_coherence(self, region) -> float:
        """Calcule la cohérence interne d'un bloc"""
        if region.size == 0:
            return 0.0
        
        # Diversité des couleurs (pas trop uniforme, pas trop chaotique)
        unique_vals = np.unique(region[region != self.empty_value])
        if len(unique_vals) == 0:
            return 0.0
        
        diversity_score = min(len(unique_vals) / 5.0, 1.0)  # Optimal autour de 5 couleurs
        
        # Compacité (forme rectangulaire)
        h, w = region.shape
        compactness_score = min(h, w) / max(h, w)  # Ratio aspect
        
        # Score de structure (patterns répétitifs ou organisés)
        structure_score = self._detect_internal_structure(region)
        
        return (diversity_score + compactness_score + structure_score) / 3.0
    
    def _detect_internal_structure(self, region) -> float:
        """Détecte la structure interne d'un bloc"""
        h, w = region.shape
        
        # Vérifier les patterns répétitifs simples
        structure_score = 0.0
        
        # Patterns horizontaux
        if h >= 2:
            horizontal_similarity = 0
            for i in range(h - 1):
                if np.array_equal(region[i], region[i + 1]):
                    horizontal_similarity += 1
            if horizontal_similarity > 0:
                structure_score += 0.3
        
        # Patterns verticaux
        if w >= 2:
            vertical_similarity = 0
            for j in range(w - 1):
                if np.array_equal(region[:, j], region[:, j + 1]):
                    vertical_similarity += 1
            if vertical_similarity > 0:
                structure_score += 0.3
        
        # Bordures cohérentes
        border_coherence = self._check_border_coherence(region)
        structure_score += border_coherence * 0.4
        
        return min(structure_score, 1.0)
    
    def _check_border_coherence(self, region) -> float:
        """Vérifie la cohérence des bordures"""
        h, w = region.shape
        if h < 2 or w < 2:
            return 0.0
        
        coherence = 0.0
        checks = 0
        
        # Bordure haute vs basse
        if np.array_equal(region[0, :], region[-1, :]):
            coherence += 1
        checks += 1
        
        # Bordure gauche vs droite
        if np.array_equal(region[:, 0], region[:, -1]):
            coherence += 1
        checks += 1
        
        return coherence / checks
    
    def _is_significant_block(self, block) -> bool:
        """Détermine si un bloc est significatif"""
        if not block:
            return False
        
        area = block['area']
        coherence = block['coherence']
        
        # Critères de significativité
        min_area = self.min_block_size * self.min_block_size
        min_coherence = 0.3
        
        return area >= min_area and coherence >= min_coherence
    
    def _find_empty_rectangles(self, grid) -> List[Dict]:
        """Trouve les rectangles vides significatifs"""
        h, w = grid.shape
        regions = []
        visited = np.zeros_like(grid, dtype=bool)
        
        for i in range(h):
            for j in range(w):
                if not visited[i, j] and grid[i, j] == self.empty_value:
                    region = self._extract_empty_rectangle(grid, i, j, visited)
                    if region and self._is_significant_empty_region(region):
                        regions.append(region)
        
        return regions
    
    def _extract_empty_rectangle(self, grid, start_i, start_j, visited) -> Optional[Dict]:
        """Extrait le plus grand rectangle vide possible"""
        h, w = grid.shape
        
        # Expansion maximale
        max_j = start_j
        while max_j < w and grid[start_i, max_j] == self.empty_value and not visited[start_i, max_j]:
            max_j += 1
        
        max_i = start_i
        valid = True
        while max_i < h and valid:
            for j in range(start_j, max_j):
                if grid[max_i, j] != self.empty_value or visited[max_i, j]:
                    valid = False
                    break
            if valid:
                max_i += 1
        
        if max_i > start_i + self.min_block_size and max_j > start_j + self.min_block_size:
            # Marquer comme visité
            visited[start_i:max_i, start_j:max_j] = True
            
            return {
                'coords': (start_i, start_j, max_i, max_j),
                'size': (max_i - start_i, max_j - start_j),
                'area': (max_i - start_i) * (max_j - start_j)
            }
        
        return None
    
    def _is_significant_empty_region(self, region) -> bool:
        """Détermine si une région vide est significative"""
        min_area = self.min_block_size * self.min_block_size
        return region['area'] >= min_area
    
    def _analyze_geometric_structure(self, grid, source_blocks, target_regions) -> float:
        """Analyse la structure géométrique globale"""
        if not source_blocks or not target_regions:
            return 0.0
        
        h, w = grid.shape
        center_r, center_c = h // 2, w // 2
        
        score = 0.0
        
        # 1. Symétries potentielles
        symmetry_score = self._detect_symmetries(source_blocks, target_regions, center_r, center_c)
        score += symmetry_score * 0.4
        
        # 2. Distribution spatiale
        distribution_score = self._analyze_spatial_distribution(source_blocks, target_regions, h, w)
        score += distribution_score * 0.3
        
        # 3. Correspondances de taille
        size_match_score = self._analyze_size_correspondences(source_blocks, target_regions)
        score += size_match_score * 0.3
        
        return score
    
    def _detect_symmetries(self, source_blocks, target_regions, center_r, center_c) -> float:
        """Détecte les symétries géométriques"""
        symmetry_score = 0.0
        total_checks = 0
        
        for source in source_blocks:
            sr1, sc1, sr2, sc2 = source['coords']
            source_center_r = (sr1 + sr2) // 2
            source_center_c = (sc1 + sc2) // 2
            
            for target in target_regions:
                tr1, tc1, tr2, tc2 = target['coords']
                target_center_r = (tr1 + tr2) // 2
                target_center_c = (tc1 + tc2) // 2
                
                # Symétrie horizontale
                if abs((source_center_c + target_center_c) / 2 - center_c) < 2:
                    symmetry_score += 1
                
                # Symétrie verticale
                if abs((source_center_r + target_center_r) / 2 - center_r) < 2:
                    symmetry_score += 1
                
                total_checks += 2
        
        return symmetry_score / max(total_checks, 1)
    
    def _analyze_spatial_distribution(self, source_blocks, target_regions, h, w) -> float:
        """Analyse la distribution spatiale des blocs"""
        # Vérifier si les blocs sont bien répartis dans l'espace
        all_blocks = source_blocks + target_regions
        if len(all_blocks) < 2:
            return 0.0
        
        # Calculer la dispersion
        centers = []
        for block in all_blocks:
            r1, c1, r2, c2 = block['coords']
            center_r = (r1 + r2) // 2
            center_c = (c1 + c2) // 2
            centers.append((center_r, center_c))
        
        # Score basé sur la couverture de l'espace
        min_r = min(c[0] for c in centers)
        max_r = max(c[0] for c in centers)
        min_c = min(c[1] for c in centers)
        max_c = max(c[1] for c in centers)
        
        coverage_r = (max_r - min_r) / h
        coverage_c = (max_c - min_c) / w
        
        return (coverage_r + coverage_c) / 2
    
    def _analyze_size_correspondences(self, source_blocks, target_regions) -> float:
        """Analyse les correspondances de taille entre sources et cibles"""
        if not source_blocks or not target_regions:
            return 0.0
        
        matches = 0
        total_comparisons = 0
        
        for source in source_blocks:
            source_size = source['size']
            for target in target_regions:
                target_size = target['size']
                
                # Correspondance exacte
                if source_size == target_size:
                    matches += 2
                # Correspondance avec transformation (rotation)
                elif (source_size[0], source_size[1]) == (target_size[1], target_size[0]):
                    matches += 1
                
                total_comparisons += 1
        
        return matches / max(total_comparisons, 1)
    
    def _analyze_transformations(self, grid) -> Dict:
        """Phase 2: Analyse des transformations possibles"""
        source_blocks = self.source_blocks if hasattr(self, 'source_blocks') else []
        target_regions = self.target_regions if hasattr(self, 'target_regions') else []
        
        transformations = []
        
        for target in target_regions:
            best_transformation = self._find_best_transformation(grid, source_blocks, target)
            if best_transformation:
                transformations.append(best_transformation)
        
        return {'transformations': transformations}
    
    def _find_best_transformation(self, grid, source_blocks, target) -> Optional[Dict]:
        """Trouve la meilleure transformation pour remplir une région cible"""
        target_size = target['size']
        best_match = None
        best_score = 0
        
        for source in source_blocks:
            for trans_name, trans_func in self.transformations.items():
                transformed_pattern = trans_func(source['pattern'])
                
                if transformed_pattern.shape == target_size:
                    score = self._score_transformation(grid, source, target, trans_name, transformed_pattern)
                    
                    if score > best_score:
                        best_score = score
                        best_match = {
                            'source': source,
                            'target': target,
                            'transformation': trans_name,
                            'transformed_pattern': transformed_pattern,
                            'score': score
                        }
        
        return best_match if best_score > 0.4 else None
    
    def _score_transformation(self, grid, source, target, trans_name, transformed_pattern) -> float:
        """Score une transformation potentielle"""
        score = 0.0
        
        # Score de base pour correspondance de taille
        score += 0.3
        
        # Score géométrique (logique de la transformation)
        geometric_score = self._score_geometric_logic(grid, source, target, trans_name)
        score += geometric_score * 0.4
        
        # Score de cohérence avec l'environnement
        coherence_score = self._score_pattern_coherence(grid, target, transformed_pattern)
        score += coherence_score * 0.3
        
        return score
    
    def _score_geometric_logic(self, grid, source, target, trans_name) -> float:
        """Score la logique géométrique d'une transformation"""
        h, w = grid.shape
        center_r, center_c = h // 2, w // 2
        
        sr1, sc1, sr2, sc2 = source['coords']
        tr1, tc1, tr2, tc2 = target['coords']
        
        source_center_r = (sr1 + sr2) // 2
        source_center_c = (sc1 + sc2) // 2
        target_center_r = (tr1 + tr2) // 2
        target_center_c = (tc1 + tc2) // 2
        
        score = 0.0
        
        if trans_name == 'flip_horizontal':
            # Logique de symétrie horizontale
            if abs((source_center_c + target_center_c) / 2 - center_c) < 3:
                score += 0.8
        elif trans_name == 'flip_vertical':
            # Logique de symétrie verticale
            if abs((source_center_r + target_center_r) / 2 - center_r) < 3:
                score += 0.8
        elif 'rotate' in trans_name:
            # Logique de rotation (distance au centre similaire)
            source_dist = np.sqrt((source_center_r - center_r)**2 + (source_center_c - center_c)**2)
            target_dist = np.sqrt((target_center_r - center_r)**2 + (target_center_c - center_c)**2)
            if abs(source_dist - target_dist) < 3:
                score += 0.6
        elif trans_name == 'identity':
            # Copie simple - score modéré
            score += 0.4
        
        return score
    
    def _score_pattern_coherence(self, grid, target, pattern) -> float:
        """Score la cohérence d'un pattern avec son environnement"""
        # Implémentation simplifiée - vérifier les bordures adjacentes
        r1, c1, r2, c2 = target['coords']
        h, w = grid.shape
        
        coherence_checks = 0
        coherence_matches = 0
        
        # Vérifier bordure gauche
        if c1 > 0:
            for i in range(r2 - r1):
                if r1 + i < h:
                    grid_val = grid[r1 + i, c1 - 1]
                    pattern_val = pattern[i, 0]
                    if grid_val != self.empty_value:
                        coherence_checks += 1
                        if abs(grid_val - pattern_val) <= 1:  # Tolérance
                            coherence_matches += 1
        
        # Vérifier bordure droite
        if c2 < w:
            for i in range(r2 - r1):
                if r1 + i < h:
                    grid_val = grid[r1 + i, c2]
                    pattern_val = pattern[i, -1]
                    if grid_val != self.empty_value:
                        coherence_checks += 1
                        if abs(grid_val - pattern_val) <= 1:
                            coherence_matches += 1
        
        return coherence_matches / max(coherence_checks, 1)
    
    def _generate_solution(self, grid, transformations) -> Dict:
        """Phase 3: Génère la solution complète"""
        solved_grid = np.array(grid, copy=True)
        agi_commands = []
        
        for transform in transformations:
            # Appliquer la transformation
            target_coords = transform['target']['coords']
            pattern = transform['transformed_pattern']
            
            r1, c1, r2, c2 = target_coords
            solved_grid[r1:r2, c1:c2] = pattern
            
            # Générer la commande AGI
            agi_command = self._generate_agi_command(transform)
            agi_commands.append(agi_command)
        
        return {
            'solved_grid': solved_grid,
            'agi_commands': agi_commands
        }
    
    def _generate_agi_command(self, transform) -> str:
        """Génère une commande AGI pour une transformation"""
        source = transform['source']
        target = transform['target']
        trans_name = transform['transformation']
        
        sr1, sc1, sr2, sc2 = source['coords']
        tr1, tc1, tr2, tc2 = target['coords']
        
        # Mapping des transformations
        trans_map = {
            'identity': 'COPY',
            'flip_horizontal': 'FLIP HORIZONTAL',
            'flip_vertical': 'FLIP VERTICAL',
            'rotate_90': 'ROTATE LEFT',
            'rotate_180': 'ROTATE 180',
            'rotate_270': 'ROTATE RIGHT'
        }
        
        copy_cmd = f"COPY [{sr1},{sc1} {sr2-1},{sc2-1}]"
        transform_cmd = trans_map.get(trans_name, trans_name.upper())
        paste_cmd = f"PASTE [{tr1},{tc1}]"
        
        if trans_name == 'identity':
            return f"{copy_cmd}; {paste_cmd}"
        else:
            return f"{copy_cmd}; {transform_cmd}; {paste_cmd}"
    
    def _calculate_final_confidence(self, results) -> float:
        """Calcule le score de confiance final"""
        base_confidence = results.get('confidence', 0.0)
        
        # Bonus pour les transformations trouvées
        num_transforms = len(results.get('transformations', []))
        if num_transforms > 0:
            transform_bonus = min(num_transforms * 0.15, 0.4)
            base_confidence += transform_bonus
        
        # Bonus pour la qualité des transformations
        if results.get('transformations'):
            avg_quality = sum(t['score'] for t in results['transformations']) / len(results['transformations'])
            base_confidence += avg_quality * 0.2
        
        return min(base_confidence, 1.0)
    
    def print_analysis(self, results):
        """Affiche l'analyse complète"""
        print("🎨 ANALYSE MOSAÏQUE UNIFIÉE")
        print("=" * 50)
        
        print(f"✅ Mosaïque détectée: {results['is_mosaic']}")
        print(f"🎯 Confiance: {results['confidence']:.1%}")
        
        print(f"\n📋 Preuves de détection:")
        for evidence in results['detection_evidence']:
            print(f"   • {evidence}")
        
        print(f"\n📊 Analyse structurelle:")
        print(f"   • Blocs sources: {len(results['source_blocks'])}")
        print(f"   • Régions cibles: {len(results['target_regions'])}")
        print(f"   • Transformations: {len(results['transformations'])}")
        
        if results['transformations']:
            print(f"\n🔧 Transformations proposées:")
            for i, transform in enumerate(results['transformations'], 1):
                score = transform['score']
                trans_name = transform['transformation']
                print(f"   {i}. {trans_name} (score: {score:.2f})")
        
        if results['agi_commands']:
            print(f"\n💻 Commandes AGI générées:")
            for i, cmd in enumerate(results['agi_commands'], 1):
                print(f"   {i}. {cmd}")

# Fonction de test
def test_unified_detector():
    """Test du détecteur unifié"""
    detector = UnifiedMosaicDetector()
    
    # Test avec une mosaïque simple
    test_grid = np.array([
        [1, 2, 0, 0, 0, 0],
        [3, 4, 0, 0, 0, 0],
        [0, 0, 0, 0, 2, 1],
        [0, 0, 0, 0, 4, 3],
        [5, 6, 0, 0, 0, 0],
        [7, 8, 0, 0, 0, 0]
    ])
    
    print("Test avec grille 6x6:")
    results = detector.detect_and_solve(test_grid)
    detector.print_analysis(results)
    
    return detector, results

if __name__ == "__main__":
    detector, results = test_unified_detector()