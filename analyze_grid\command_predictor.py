#!/usr/bin/env python3
"""
Système de prédiction de commandes AGI basé sur les corrélations découvertes
"""

import json
import numpy as np
from typing import Dict, List, Tuple, Any
from analyze_grid.arc_analyzer import ARCAnalyzer

class AGICommandPredictor:
    """Prédicteur de commandes AGI basé sur l'analyse de corrélations"""
    
    def __init__(self):
        self.load_correlation_rules()
    
    def load_correlation_rules(self):
        """Charge les règles de corrélation découvertes"""
        self.rules = {
            'RESIZE_EXTRACT': {
                'strong_indicators': [
                    ('max_dimension_change', '>', 3.0, 0.425),  # Forte corrélation
                    ('avg_height_change', '<', -1.0, -0.213),   # Contraction hauteur
                    ('avg_width_change', '<', -1.0, -0.235),    # Contraction largeur
                ],
                'moderate_indicators': [
                    ('geometric_transforms', '>', 0, 0.518),
                    ('structural_transforms', '==', 0, -0.375),
                ]
            },
            'MOTIF': {
                'strong_indicators': [
                    ('motif_detected', '==', True, 1.0),  # Évident mais important
                    ('motif_confidence', '>', 0.7, 0.8),
                ],
                'subtypes': {
                    'transformations': [
                        ('geometric_transforms', '>', 0, 0.235),
                        ('color_transforms', '<', 1, -0.214),
                    ],
                    'coordinate_selection': [
                        ('object_complexity', '>', 5.0, 0.200),
                        ('max_size_ratio', '>', 1.5, 0.193),
                        ('avg_size_ratio_h', '>', 1.2, 0.210),
                    ],
                    'color_selection': [
                        ('geometric_transforms', '>', 0, 0.196),
                        ('input_colors_count', '>', 3, 0.15),
                    ]
                }
            },
            'FILL_GROUP': {
                'strong_indicators': [
                    ('avg_height_change', '>', 1.0, 0.168),    # Expansion
                    ('avg_width_change', '>', 1.0, 0.169),     # Expansion
                    ('min_size_ratio', '>', 1.0, 0.192),
                ],
                'moderate_indicators': [
                    ('grid_complexity', '>', 50.0, 0.156),
                ]
            },
            'FLOODFILL': {
                'strong_indicators': [
                    ('input_colors_count', '<', 4, -0.177),     # Peu de couleurs
                    ('color_transforms', '>', 0, 0.161),
                ],
            },
            'EDIT_GROUP': {
                'strong_indicators': [
                    ('avg_size_ratio_w', '==', 1.0, -0.152),   # Pas de changement largeur
                    ('has_dimension_change', '==', False, 0.3), # Estimation
                ],
            }
        }
    
    def extract_metrics(self, puzzle: Dict) -> Dict[str, float]:
        """Extrait les métriques d'un puzzle avec ARCAnalyzer"""
        analyzer = ARCAnalyzer()
        analysis = analyzer.analyze_puzzle(puzzle)
        
        metrics = {}
        
        # Métriques de base
        metrics['grid_complexity'] = analysis['complexity']['grid_size_complexity']
        metrics['color_complexity'] = analysis['complexity']['color_complexity']
        metrics['object_complexity'] = analysis['complexity']['object_complexity']
        metrics['overall_complexity'] = analysis['complexity']['overall_complexity']
        metrics['input_colors_count'] = len(analysis['colors']['input_colors'])
        metrics['output_colors_count'] = len(analysis['colors']['output_colors'])
        metrics['geometric_transforms'] = len(analysis['transformations']['geometric'])
        metrics['color_transforms'] = len(analysis['transformations']['color'])
        metrics['structural_transforms'] = len(analysis['transformations']['structural'])
        
        # Métriques MOTIF
        metrics['motif_detected'] = analysis['patterns']['motif']['detected']
        metrics['motif_confidence'] = analysis['patterns']['motif']['confidence']
        
        # Métriques dimensionnelles (cruciales pour RESIZE_EXTRACT)
        if analysis['grid_info']['size_ratios']:
            ratios = analysis['grid_info']['size_ratios']
            metrics['avg_size_ratio_h'] = np.mean([r[0] for r in ratios])
            metrics['avg_size_ratio_w'] = np.mean([r[1] for r in ratios])
            metrics['has_size_change'] = not all(r[0] == 1.0 and r[1] == 1.0 for r in ratios)
            metrics['max_size_ratio'] = max(max(r[0], r[1]) for r in ratios)
            metrics['min_size_ratio'] = min(min(r[0], r[1]) for r in ratios)
        else:
            metrics.update({
                'avg_size_ratio_h': 1.0, 'avg_size_ratio_w': 1.0,
                'has_size_change': False, 'max_size_ratio': 1.0, 'min_size_ratio': 1.0
            })
        
        if analysis['grid_info']['dimension_changes']:
            dim_changes = analysis['grid_info']['dimension_changes']
            metrics['has_dimension_change'] = not all(c[0] == 0 and c[1] == 0 for c in dim_changes)
            metrics['avg_height_change'] = np.mean([c[0] for c in dim_changes])
            metrics['avg_width_change'] = np.mean([c[1] for c in dim_changes])
            metrics['max_dimension_change'] = max(max(abs(c[0]), abs(c[1])) for c in dim_changes)
        else:
            metrics.update({
                'has_dimension_change': False, 'avg_height_change': 0.0,
                'avg_width_change': 0.0, 'max_dimension_change': 0.0
            })
        
        return metrics
    
    def evaluate_rule(self, metric_value: Any, operator: str, threshold: Any) -> bool:
        """Évalue une règle de corrélation"""
        try:
            if operator == '>':
                return float(metric_value) > float(threshold)
            elif operator == '<':
                return float(metric_value) < float(threshold)
            elif operator == '==':
                if isinstance(threshold, bool):
                    return bool(metric_value) == threshold
                return float(metric_value) == float(threshold)
            elif operator == '>=':
                return float(metric_value) >= float(threshold)
            elif operator == '<=':
                return float(metric_value) <= float(threshold)
        except (ValueError, TypeError):
            return False
        return False
    
    def predict_command_probability(self, metrics: Dict[str, Any], command: str) -> float:
        """Calcule la probabilité d'une commande basée sur les métriques"""
        if command not in self.rules:
            return 0.0
        
        rule_set = self.rules[command]
        score = 0.0
        total_weight = 0.0
        
        # Évaluer les indicateurs forts
        for metric_name, operator, threshold, correlation in rule_set.get('strong_indicators', []):
            if metric_name in metrics:
                weight = abs(correlation) * 2.0  # Poids double pour indicateurs forts
                if self.evaluate_rule(metrics[metric_name], operator, threshold):
                    score += weight
                total_weight += weight
        
        # Évaluer les indicateurs modérés
        for metric_name, operator, threshold, correlation in rule_set.get('moderate_indicators', []):
            if metric_name in metrics:
                weight = abs(correlation)
                if self.evaluate_rule(metrics[metric_name], operator, threshold):
                    score += weight
                total_weight += weight
        
        return score / total_weight if total_weight > 0 else 0.0
    
    def predict_motif_subtype(self, metrics: Dict[str, Any]) -> Dict[str, float]:
        """Prédit le sous-type de MOTIF"""
        if not metrics.get('motif_detected', False):
            return {}
        
        subtypes = {}
        motif_rules = self.rules['MOTIF']['subtypes']
        
        for subtype, indicators in motif_rules.items():
            score = 0.0
            total_weight = 0.0
            
            for metric_name, operator, threshold, correlation in indicators:
                if metric_name in metrics:
                    weight = abs(correlation)
                    if self.evaluate_rule(metrics[metric_name], operator, threshold):
                        score += weight
                    total_weight += weight
            
            subtypes[subtype] = score / total_weight if total_weight > 0 else 0.0
        
        return subtypes
    
    def predict_commands(self, puzzle: Dict) -> Dict[str, Any]:
        """Prédit les commandes pour un puzzle"""
        metrics = self.extract_metrics(puzzle)
        
        # Prédire chaque type de commande
        predictions = {}
        commands = ['RESIZE_EXTRACT', 'MOTIF', 'FILL_GROUP', 'FLOODFILL', 'EDIT_GROUP']
        
        for command in commands:
            probability = self.predict_command_probability(metrics, command)
            predictions[command] = {
                'probability': probability,
                'confidence': 'high' if probability > 0.7 else 'medium' if probability > 0.4 else 'low'
            }
        
        # Analyse spéciale pour MOTIF
        if metrics.get('motif_detected', False):
            motif_subtypes = self.predict_motif_subtype(metrics)
            predictions['MOTIF']['subtypes'] = motif_subtypes
        
        # Trier par probabilité
        sorted_predictions = sorted(
            [(cmd, data) for cmd, data in predictions.items()],
            key=lambda x: x[1]['probability'],
            reverse=True
        )
        
        return {
            'metrics_used': metrics,
            'predictions': dict(sorted_predictions),
            'top_recommendation': sorted_predictions[0][0] if sorted_predictions else None,
            'confidence_summary': {
                'high_confidence': [cmd for cmd, data in predictions.items() if data['probability'] > 0.7],
                'medium_confidence': [cmd for cmd, data in predictions.items() if 0.4 < data['probability'] <= 0.7],
                'low_confidence': [cmd for cmd, data in predictions.items() if data['probability'] <= 0.4]
            }
        }

def test_predictor():
    """Test du prédicteur sur quelques puzzles"""
    print("🧪 TEST DU PRÉDICTEUR DE COMMANDES AGI")
    print("=" * 50)
    
    predictor = AGICommandPredictor()
    
    # Test sur 007bbfb7 (connu pour MOTIF + RESIZE)
    test_puzzles = ['007bbfb7', '36d67576', '017c7c7b']
    
    for puzzle_id in test_puzzles:
        json_file = f"../arcdata/training/{puzzle_id}.json"
        agi_file = f"../arcdata/training/{puzzle_id}_TEST0_VALID.agi"
        
        if not os.path.exists(json_file):
            continue
        
        print(f"\n🔍 Test sur {puzzle_id}:")
        
        # Charger puzzle
        with open(json_file, 'r') as f:
            puzzle = json.load(f)
        
        # Prédire
        result = predictor.predict_commands(puzzle)
        
        # Afficher prédictions
        print("  Prédictions:")
        for cmd, data in result['predictions'].items():
            prob = data['probability']
            conf = data['confidence']
            print(f"    {cmd}: {prob:.3f} ({conf})")
            
            if cmd == 'MOTIF' and 'subtypes' in data:
                for subtype, score in data['subtypes'].items():
                    print(f"      └─ {subtype}: {score:.3f}")
        
        print(f"  Recommandation: {result['top_recommendation']}")
        
        # Vérifier avec la vraie solution
        if os.path.exists(agi_file):
            with open(agi_file, 'r', encoding='utf-8') as f:
                agi_content = f.read()
            
            actual_commands = []
            if 'MOTIF' in agi_content:
                actual_commands.append('MOTIF')
            if 'RESIZE' in agi_content or 'EXTRACT' in agi_content:
                actual_commands.append('RESIZE_EXTRACT')
            if 'FILL' in agi_content:
                actual_commands.append('FILL_GROUP')
            if 'EDIT' in agi_content:
                actual_commands.append('EDIT_GROUP')
            if 'FLOODFILL' in agi_content:
                actual_commands.append('FLOODFILL')
            
            print(f"  Réalité: {actual_commands}")
            
            # Vérifier si la prédiction est correcte
            top_pred = result['top_recommendation']
            if top_pred in actual_commands:
                print("  ✅ Prédiction correcte!")
            else:
                print("  ❌ Prédiction incorrecte")

if __name__ == "__main__":
    import os
    test_predictor()