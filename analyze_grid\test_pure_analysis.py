"""
Test simple pour l'analyseur de grilles pur
"""

from grid_analysis_pure import PureGridAnalyzer

def test_simple_transformation():
    """Test avec une transformation simple"""
    
    print("Test d'une transformation simple")
    print("-" * 30)
    
    # Grille d'entrée 2x2
    input_grid = [
        [1, 0],
        [0, 1]
    ]
    
    # Grille de sortie 4x4 (multiplication par 2)
    output_grid = [
        [1, 0, 1, 0],
        [0, 1, 0, 1],
        [1, 0, 1, 0],
        [0, 1, 0, 1]
    ]
    
    analyzer = PureGridAnalyzer("../arcdata/training")
    analysis = analyzer.analyze_transformation(input_grid, output_grid)
    
    print(f"Forme input: {analysis['input_shape']}")
    print(f"Forme output: {analysis['output_shape']}")
    print(f"Ratio de taille: {analysis['size_ratio']}")
    print(f"Type de transformation: {analysis['transformation_type']['type']}")
    print(f"Changement de complexité: {analysis['complexity_change']:.2f}")
    print(f"Patterns détectés: {analysis['special_patterns']}")
    
    print("\nInsights calculés:")
    for insight, value in analysis['computed_insights'].items():
        status = "✓" if value else "✗"
        print(f"  {status} {insight}")
    
    return analysis

def test_same_size_transformation():
    """Test avec une transformation de même taille"""
    
    print("\nTest d'une transformation même taille")
    print("-" * 30)
    
    # Grille d'entrée avec des 0
    input_grid = [
        [1, 0, 1],
        [0, 0, 0],
        [1, 0, 1]
    ]
    
    # Grille de sortie avec remplissage
    output_grid = [
        [1, 2, 1],
        [2, 2, 2],
        [1, 2, 1]
    ]
    
    analyzer = PureGridAnalyzer("../arcdata/training")
    analysis = analyzer.analyze_transformation(input_grid, output_grid)
    
    print(f"Type de transformation: {analysis['transformation_type']['type']}")
    print(f"Couleurs ajoutées: {analysis['colors_added']}")
    print(f"Couleurs supprimées: {analysis['colors_removed']}")
    print(f"Changements de cellules: {analysis['cell_analysis']['changes']}")
    print(f"Ratio de changement: {analysis['cell_analysis']['change_ratio']:.2f}")
    
    return analysis

if __name__ == "__main__":
    print("TEST DE L'ANALYSEUR DE GRILLES PUR")
    print("=" * 40)
    
    # Test 1: Transformation avec multiplication
    test_simple_transformation()
    
    # Test 2: Transformation même taille
    test_same_size_transformation()
    
    print("\nTests terminés !")