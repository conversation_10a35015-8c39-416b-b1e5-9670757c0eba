================================================================================
RAPPORT DE SYNTHÈSE - ANALYSE DES TÂCHES ARC SPÉCIFIÉES
================================================================================

Tâches analysées avec succès: 8/8

STATISTIQUES GLOBALES:
- Total exemples d'entraînement: 27
- Total exemples de test: 8
- Total paires même taille: 21

ANALYSE DÉTAILLÉE PAR TÂCHE:
--------------------------------------------------

🔍 TÂCHE 0dfd9992:
   Exemples: 3 train, 1 test
   Paires même taille: 3
   Changement dimension cohérent: (0, 0)
   Patterns: Aucun pattern majeur
   Complexité globale: 404.70
   Couleurs utilisées: 8 input, 7 output
   Dimensions train inputs: [(21, 21)] (cohérent)
   Dimensions train outputs: [(21, 21)] (cohérent)

🔍 TÂCHE 29ec7d0e:
   Exemples: 4 train, 1 test
   Paires même taille: 4
   Changement dimension cohérent: (0, 0)
   Patterns: Aucun pattern majeur
   Complexité globale: 256.52
   Couleurs utilisées: 9 input, 8 output
   Dimensions train inputs: [(18, 18)] (cohérent)
   Dimensions train outputs: [(18, 18)] (cohérent)

🔍 TÂCHE 3631a71a:
   Exemples: 4 train, 1 test
   Paires même taille: 4
   Changement dimension cohérent: (0, 0)
   Patterns: Aucun pattern majeur
   Complexité globale: 488.72
   Couleurs utilisées: 10 input, 9 output
   Dimensions train inputs: [(30, 30)] (cohérent)
   Dimensions train outputs: [(30, 30)] (cohérent)

🔍 TÂCHE 484b58aa:
   Exemples: 3 train, 1 test
   Paires même taille: 3
   Changement dimension cohérent: (0, 0)
   Patterns: Aucun pattern majeur
   Complexité globale: 772.60
   Couleurs utilisées: 9 input, 8 output
   Dimensions train inputs: [(29, 29)] (cohérent)
   Dimensions train outputs: [(29, 29)] (cohérent)

🔍 TÂCHE 9ecd008a:
   Exemples: 3 train, 1 test
   Paires même taille: 0
   Changement dimension cohérent: (-13, -13)
   Patterns: Mise à l'échelle, Motif coordinate (conf: 0.60)
   Complexité globale: 92.70
   Couleurs utilisées: 10 input, 8 output
   Dimensions train inputs: [(16, 16)] (cohérent)
   Dimensions train outputs: [(3, 3)] (cohérent)

🔍 TÂCHE b8825c91:
   Exemples: 4 train, 1 test
   Paires même taille: 4
   Changement dimension cohérent: (0, 0)
   Patterns: Aucun pattern majeur
   Complexité globale: 172.93
   Couleurs utilisées: 9 input, 8 output
   Dimensions train inputs: [(16, 16)] (cohérent)
   Dimensions train outputs: [(16, 16)] (cohérent)

🔍 TÂCHE c3f564a4:
   Exemples: 3 train, 1 test
   Paires même taille: 3
   Changement dimension cohérent: (0, 0)
   Patterns: Aucun pattern majeur
   Complexité globale: 246.60
   Couleurs utilisées: 8 input, 7 output
   Dimensions train inputs: [(16, 16)] (cohérent)
   Dimensions train outputs: [(16, 16)] (cohérent)

🔍 TÂCHE dc0a314f:
   Exemples: 3 train, 1 test
   Paires même taille: 0
   Changement dimension cohérent: (-11, -11)
   Patterns: Mise à l'échelle, Motif coordinate (conf: 0.60)
   Complexité globale: 94.90
   Couleurs utilisées: 9 input, 7 output
   Dimensions train inputs: [(16, 16)] (cohérent)
   Dimensions train outputs: [(5, 5)] (cohérent)

==================================================
PATTERNS LES PLUS FRÉQUENTS:
- scaling: 2/8 tâches (25.0%)
- motif_coordinate: 2/8 tâches (25.0%)