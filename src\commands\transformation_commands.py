"""
Module contenant les commandes de transformation pour les grilles ARC
"""

import numpy as np
from typing import Dict, Any
from .unified_command import UnifiedCommand


class TransformationCommands:
    """Classe contenant les commandes de transformation pour les grilles ARC"""
    
    def __init__(self, executor):
        """
        Initialise les commandes de transformation
        
        Args:
            executor: Instance de CommandExecutor pour accéder à la grille et aux propriétés
        """
        self.executor = executor

    def cmd_flip(self, unified_cmd: UnifiedCommand) -> bool:
        """Effectue une opération FLIP sur la sélection (copiée au préalable) ou sur la grille entière."""
        if not unified_cmd.parameters:
            self.executor.error = "La commande FLIP nécessite une direction (HORIZONTAL ou VERTICAL)"
            return False

        direction = unified_cmd.parameters[0].upper()

        # Si aucune coordonnée, on flip la grille entière
        if not unified_cmd.coordinates:
            self.executor.clipboard = self.executor.grid.copy()
            self.executor.clipboard_mask = None
        # Sinon, on s'attend à ce que COPY ait déjà été appelé
        elif self.executor.clipboard is None:
            self.executor.error = "FLIP sur une sélection nécessite une commande COPY préalable."
            return False

        if direction == 'HORIZONTAL':
            self._flip_horizontal()
        elif direction == 'VERTICAL':
            self._flip_vertical()
        else:
            self.executor.error = f"Direction invalide pour FLIP: {direction}"
            return False

        # Si on a flippé la grille entière, on la met à jour
        if not unified_cmd.coordinates:
            self.executor.grid = self.executor.clipboard.copy()

        return True

    def cmd_rotate(self, unified_cmd: UnifiedCommand) -> bool:
        """Effectue une opération ROTATE sur la sélection (copiée au préalable) ou sur la grille entière."""
        if not unified_cmd.parameters:
            self.executor.error = "La commande ROTATE nécessite une direction (LEFT ou RIGHT)"
            return False

        direction = unified_cmd.parameters[0].upper()

        if not unified_cmd.coordinates:
            # Rotation de toute la grille → copier d'abord dans le presse-papier
            self.executor.clipboard = self.executor.grid.copy()
            self.executor.clipboard_mask = np.ones(self.executor.grid.shape, dtype=bool)
        elif self.executor.clipboard is None:
            self.executor.error = "ROTATE sur une sélection nécessite une commande COPY préalable."
            return False

        if direction == 'LEFT':
            self._rotate_left()
        elif direction == 'RIGHT':
            self._rotate_right()
        else:
            self.executor.error = f"Direction invalide pour ROTATE: {direction}"
            return False

        if not unified_cmd.coordinates:
            self.executor.grid = self.executor.clipboard.copy()
            self.executor.height, self.executor.width = self.executor.grid.shape

        return True

    def _flip_horizontal(self):
        """Flips the clipboard horizontally."""
        if self.executor.clipboard is not None:
            self.executor.clipboard = np.fliplr(self.executor.clipboard)
            if self.executor.clipboard_mask is not None:
                self.executor.clipboard_mask = np.fliplr(self.executor.clipboard_mask)

    def _flip_vertical(self):
        """Flips the clipboard vertically."""
        if self.executor.clipboard is not None:
            self.executor.clipboard = np.flipud(self.executor.clipboard)
            if self.executor.clipboard_mask is not None:
                self.executor.clipboard_mask = np.flipud(self.executor.clipboard_mask)

    def _rotate_left(self):
        """Rotates the clipboard 90 degrees to the left."""
        if self.executor.clipboard is not None:
            self.executor.clipboard = np.rot90(self.executor.clipboard)
            if self.executor.clipboard_mask is not None:
                self.executor.clipboard_mask = np.rot90(self.executor.clipboard_mask)

    def _rotate_right(self):
        """Rotates the clipboard 90 degrees to the right."""
        if self.executor.clipboard is not None:
            self.executor.clipboard = np.rot90(self.executor.clipboard, k=-1)
            if self.executor.clipboard_mask is not None:
                self.executor.clipboard_mask = np.rot90(self.executor.clipboard_mask, k=-1)
