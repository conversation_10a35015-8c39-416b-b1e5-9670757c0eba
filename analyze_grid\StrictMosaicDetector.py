#!/usr/bin/env python3
"""
Détecteur de mosaïque strict - évite les faux positifs
Se base uniquement sur des preuves solides de répétition de blocs
"""

import numpy as np
from collections import Counter
from typing import Dict, List, Tuple, Optional
import json

class StrictMosaicDetector:
    """
    Détecteur de mosaïque strict qui évite les faux positifs
    """
    
    def __init__(self, empty_value=0):
        self.empty_value = empty_value
        self.min_block_size = 2
        self.max_block_size = 6
    
    def analyze_mosaic_task(self, task_data: dict) -> dict:
        """
        Analyse une tâche ARC avec des critères stricts
        """
        results = {
            'is_mosaic_task': False,
            'confidence': 0.0,
            'evidence': [],
            'examples_analysis': [],
            'summary': {}
        }
        
        # Analyser chaque exemple d'entraînement
        valid_examples = []
        
        for i, example in enumerate(task_data['train']):
            input_grid = np.array(example['input'])
            output_grid = np.array(example['output'])
            
            # Critère strict : dimensions identiques obligatoires
            if input_grid.shape != output_grid.shape:
                continue
            
            example_analysis = self.analyze_example_strict(input_grid, output_grid, i)
            results['examples_analysis'].append(example_analysis)
            
            if example_analysis['has_solid_evidence']:
                valid_examples.append(example_analysis)
        
        # Calculer la confiance seulement si on a des preuves solides
        if valid_examples:
            # Critère strict : au moins 2 exemples avec preuves solides
            if len(valid_examples) >= 2:
                avg_score = sum(ex['mosaic_score'] for ex in valid_examples) / len(valid_examples)
                consistency = self._calculate_strict_consistency(valid_examples)
                
                results['confidence'] = (avg_score * 0.8) + (consistency * 0.2)
                results['is_mosaic_task'] = results['confidence'] > 0.7  # Seuil plus élevé
                results['evidence'] = self._consolidate_evidence([ex['evidence'] for ex in valid_examples])
                results['summary'] = self._generate_summary(results, valid_examples)
        
        return results
    
    def analyze_example_strict(self, input_grid: np.ndarray, output_grid: np.ndarray, example_idx: int) -> dict:
        """
        Analyse stricte d'un exemple - cherche des preuves solides uniquement
        """
        analysis = {
            'example_idx': example_idx,
            'mosaic_score': 0.0,
            'evidence': [],
            'has_solid_evidence': False,
            'block_matches': [],
            'transformation_type': None
        }
        
        # 1. DÉTECTION DE BLOCS RÉPÉTÉS STRICTS
        block_matches = self._find_strict_block_matches(input_grid, output_grid)
        analysis['block_matches'] = block_matches
        
        if block_matches:
            analysis['evidence'].append(f"Blocs répétés détectés: {len(block_matches)}")
            analysis['mosaic_score'] += 0.6
            analysis['has_solid_evidence'] = True
        
        # 2. ANALYSE DES TRANSFORMATIONS GÉOMÉTRIQUES
        transformation_type = self._detect_geometric_transformation(block_matches)
        if transformation_type:
            analysis['transformation_type'] = transformation_type
            analysis['evidence'].append(f"Transformation géométrique: {transformation_type}")
            analysis['mosaic_score'] += 0.3
        
        # 3. VÉRIFICATION DE LA COHÉRENCE SPATIALE
        if block_matches:
            spatial_coherence = self._verify_spatial_coherence(input_grid, output_grid, block_matches)
            if spatial_coherence > 0.8:
                analysis['evidence'].append("Cohérence spatiale élevée")
                analysis['mosaic_score'] += 0.2
        
        # 4. CRITÈRE STRICT : au moins 2 preuves solides
        if len(analysis['evidence']) >= 2 and analysis['mosaic_score'] > 0.8:
            analysis['has_solid_evidence'] = True
        else:
            analysis['has_solid_evidence'] = False
            analysis['mosaic_score'] = 0.0  # Reset si pas assez de preuves
        
        return analysis
    
    def _find_strict_block_matches(self, input_grid: np.ndarray, output_grid: np.ndarray) -> list:
        """
        Trouve les correspondances de blocs avec des critères très stricts
        """
        h, w = input_grid.shape
        matches = []
        
        # Tester différentes tailles de blocs
        for block_h in range(self.min_block_size, min(self.max_block_size + 1, h//3)):
            for block_w in range(self.min_block_size, min(self.max_block_size + 1, w//3)):
                block_matches = self._find_blocks_of_size(input_grid, output_grid, block_h, block_w)
                matches.extend(block_matches)
        
        # Filtrer et garder seulement les meilleurs matches
        return self._filter_best_matches(matches)
    
    def _find_blocks_of_size(self, input_grid: np.ndarray, output_grid: np.ndarray, 
                            block_h: int, block_w: int) -> list:
        """
        Trouve les blocs d'une taille spécifique qui correspondent
        """
        h, w = input_grid.shape
        matches = []
        
        # Extraire tous les blocs uniques de l'input
        input_blocks = {}
        for i in range(h - block_h + 1):
            for j in range(w - block_w + 1):
                block = input_grid[i:i+block_h, j:j+block_w]
                
                # Ignorer les blocs trop vides ou trop uniformes
                unique_values = len(np.unique(block))
                non_empty_ratio = np.sum(block != self.empty_value) / block.size
                
                if unique_values >= 2 and non_empty_ratio >= 0.4:
                    block_key = tuple(block.flatten())
                    if block_key not in input_blocks:
                        input_blocks[block_key] = []
                    input_blocks[block_key].append((i, j, block))
        
        # Chercher ces blocs dans l'output (avec transformations possibles)
        for i in range(h - block_h + 1):
            for j in range(w - block_w + 1):
                output_block = output_grid[i:i+block_h, j:j+block_w]
                
                # Tester les transformations possibles
                transformations = {
                    'identity': output_block,
                    'flip_h': np.fliplr(output_block),
                    'flip_v': np.flipud(output_block),
                    'rot_90': np.rot90(output_block, 1),
                    'rot_180': np.rot90(output_block, 2),
                    'rot_270': np.rot90(output_block, 3)
                }
                
                for trans_name, transformed_block in transformations.items():
                    if transformed_block.shape == (block_h, block_w):
                        trans_key = tuple(transformed_block.flatten())
                        
                        if trans_key in input_blocks:
                            # Match trouvé !
                            for input_pos in input_blocks[trans_key]:
                                matches.append({
                                    'input_pos': input_pos[:2],
                                    'output_pos': (i, j),
                                    'block_size': (block_h, block_w),
                                    'transformation': trans_name,
                                    'input_block': input_pos[2],
                                    'output_block': output_block,
                                    'match_quality': 1.0  # Match exact
                                })
        
        return matches
    
    def _filter_best_matches(self, matches: list) -> list:
        """
        Filtre et garde seulement les meilleurs matches
        """
        if not matches:
            return []
        
        # Éliminer les chevauchements
        unique_matches = []
        used_positions = set()
        
        # Trier par qualité et taille de bloc
        matches.sort(key=lambda x: (x['match_quality'], x['block_size'][0] * x['block_size'][1]), reverse=True)
        
        for match in matches:
            input_pos = match['input_pos']
            output_pos = match['output_pos']
            
            # Vérifier les chevauchements
            overlap = False
            for used_input, used_output in used_positions:
                if (abs(input_pos[0] - used_input[0]) < match['block_size'][0] and
                    abs(input_pos[1] - used_input[1]) < match['block_size'][1]) or \
                   (abs(output_pos[0] - used_output[0]) < match['block_size'][0] and
                    abs(output_pos[1] - used_output[1]) < match['block_size'][1]):
                    overlap = True
                    break
            
            if not overlap:
                unique_matches.append(match)
                used_positions.add((input_pos, output_pos))
        
        return unique_matches[:5]  # Max 5 matches par exemple
    
    def _detect_geometric_transformation(self, block_matches: list) -> Optional[str]:
        """
        Détecte le type de transformation géométrique dominant
        """
        if not block_matches:
            return None
        
        transformations = [match['transformation'] for match in block_matches]
        transformation_counts = Counter(transformations)
        
        most_common = transformation_counts.most_common(1)[0]
        if most_common[1] >= len(block_matches) * 0.6:  # Au moins 60% cohérent
            return most_common[0]
        
        return None
    
    def _verify_spatial_coherence(self, input_grid: np.ndarray, output_grid: np.ndarray, 
                                 block_matches: list) -> float:
        """
        Vérifie la cohérence spatiale des transformations
        """
        if len(block_matches) < 2:
            return 0.0
        
        # Calculer les vecteurs de déplacement
        displacement_vectors = []
        for match in block_matches:
            input_pos = match['input_pos']
            output_pos = match['output_pos']
            vector = (output_pos[0] - input_pos[0], output_pos[1] - input_pos[1])
            displacement_vectors.append(vector)
        
        # Mesurer la cohérence des déplacements
        unique_vectors = set(displacement_vectors)
        if len(unique_vectors) == 1:
            return 1.0  # Tous les déplacements identiques
        elif len(unique_vectors) <= len(displacement_vectors) // 2:
            return 0.8  # Assez cohérent
        else:
            return 0.3  # Peu cohérent
    
    def _calculate_strict_consistency(self, valid_examples: list) -> float:
        """
        Calcule la cohérence entre exemples avec critères stricts
        """
        if len(valid_examples) < 2:
            return 1.0
        
        # Vérifier la cohérence des types de transformation
        transformation_types = []
        for example in valid_examples:
            if example['transformation_type']:
                transformation_types.append(example['transformation_type'])
        
        if transformation_types:
            type_consistency = len(set(transformation_types)) == 1
            return 1.0 if type_consistency else 0.5
        
        return 0.5
    
    def _consolidate_evidence(self, evidence_lists: list) -> list:
        """
        Consolide les preuves de tous les exemples
        """
        all_evidence = []
        for evidence_list in evidence_lists:
            all_evidence.extend(evidence_list)
        
        evidence_counter = Counter(all_evidence)
        consolidated = []
        
        for evidence, count in evidence_counter.most_common():
            if count > 1:
                consolidated.append(f"{evidence} (dans {count} exemples)")
            else:
                consolidated.append(evidence)
        
        return consolidated
    
    def _generate_summary(self, results: dict, valid_examples: list) -> dict:
        """
        Génère un résumé de l'analyse stricte
        """
        return {
            'total_examples': len(results['examples_analysis']),
            'valid_examples': len(valid_examples),
            'avg_mosaic_score': sum(ex['mosaic_score'] for ex in valid_examples) / len(valid_examples),
            'dominant_transformation': self._find_dominant_transformation(valid_examples),
            'total_block_matches': sum(len(ex['block_matches']) for ex in valid_examples)
        }
    
    def _find_dominant_transformation(self, valid_examples: list) -> Optional[str]:
        """
        Trouve la transformation dominante
        """
        transformations = []
        for example in valid_examples:
            if example['transformation_type']:
                transformations.append(example['transformation_type'])
        
        if transformations:
            return Counter(transformations).most_common(1)[0][0]
        return None
    
    def print_analysis(self, results: dict, task_id: str = ""):
        """
        Affiche l'analyse stricte
        """
        print(f"{'='*60}")
        print(f"ANALYSE MOSAÏQUE STRICTE - {task_id}")
        print(f"{'='*60}")
        
        print(f"\n🎯 RÉSULTAT GLOBAL:")
        print(f"   Mosaïque détectée: {'✅ OUI' if results['is_mosaic_task'] else '❌ NON'}")
        print(f"   Confiance: {results['confidence']:.2f}")
        
        if results['evidence']:
            print(f"\n🔍 PREUVES SOLIDES:")
            for evidence in results['evidence']:
                print(f"   • {evidence}")
        else:
            print(f"\n❌ AUCUNE PREUVE SOLIDE DÉTECTÉE")
        
        if 'summary' in results and results['summary']:
            summary = results['summary']
            print(f"\n📊 RÉSUMÉ:")
            print(f"   • Exemples analysés: {summary['total_examples']}")
            print(f"   • Exemples avec preuves solides: {summary['valid_examples']}")
            if summary['valid_examples'] > 0:
                print(f"   • Score moyen: {summary['avg_mosaic_score']:.2f}")
                print(f"   • Transformation dominante: {summary['dominant_transformation']}")
                print(f"   • Total correspondances de blocs: {summary['total_block_matches']}")

# Test du détecteur strict
def test_strict_detector():
    """
    Test le détecteur strict sur les tâches spécifiées
    """
    import json
    from pathlib import Path
    
    task_ids = [
        "0dfd9992", "29ec7d0e", "3631a71a", "484b58aa", 
        "9ecd008a", "b8825c91", "c3f564a4", "dc0a314f"
    ]
    
    detector = StrictMosaicDetector()
    
    print("=" * 80)
    print("TEST DU DÉTECTEUR DE MOSAÏQUE STRICT")
    print("=" * 80)
    
    for task_id in task_ids:
        try:
            task_path = Path(f"../arcdata/training/{task_id}.json")
            with open(task_path, 'r') as f:
                task_data = json.load(f)
            
            results = detector.analyze_mosaic_task(task_data)
            detector.print_analysis(results, task_id)
            print()
            
        except Exception as e:
            print(f"❌ Erreur pour {task_id}: {e}")

if __name__ == "__main__":
    test_strict_detector()