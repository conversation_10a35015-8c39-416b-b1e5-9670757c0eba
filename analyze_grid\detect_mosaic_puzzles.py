#!/usr/bin/env python3
"""
Détecteur de puzzles de type MOSAIC
Identifie les puzzles avec de grandes dimensions et beaucoup de couleurs
"""

import json
import os
import numpy as np
from typing import Dict, List, Tuple
from analyze_grid.arc_analyzer import ARCAnalyzer

class MosaicPuzzleDetector:
    """Détecteur spécialisé pour les puzzles de type Mosaic"""
    
    def __init__(self):
        self.analyzer = ARCAnalyzer()
        self.mosaic_candidates = []
        
    def analyze_puzzle_for_mosaic(self, puzzle_data: Dict, puzzle_id: str) -> Dict:
        """Analyse un puzzle pour détecter s'il est de type Mosaic"""
        
        # Analyse complète avec ARCAnalyzer
        analysis = self.analyzer.analyze_puzzle(puzzle_data)
        
        # Extraire les métriques clés pour Mosaic
        mosaic_metrics = self.extract_mosaic_metrics(analysis, puzzle_data)
        mosaic_metrics['puzzle_id'] = puzzle_id
        
        # Calculer le score Mosaic
        mosaic_score = self.calculate_mosaic_score(mosaic_metrics)
        mosaic_metrics['mosaic_score'] = mosaic_score
        mosaic_metrics['is_mosaic_candidate'] = mosaic_score > 0.7  # Seuil à ajuster
        
        return mosaic_metrics
    
    def extract_mosaic_metrics(self, analysis: Dict, puzzle_data: Dict) -> Dict:
        """Extrait les métriques spécifiques aux puzzles Mosaic"""
        
        metrics = {}
        
        # Analyser les dimensions d'input
        input_dimensions = []
        input_areas = []
        input_color_counts = []
        
        for example in puzzle_data['train']:
            input_grid = np.array(example['input'])
            h, w = input_grid.shape
            
            input_dimensions.append((h, w))
            input_areas.append(h * w)
            
            # Compter les couleurs uniques (excluant le fond noir)
            unique_colors = set(input_grid.flatten())
            if 0 in unique_colors:
                unique_colors.remove(0)  # Retirer le fond noir
            input_color_counts.append(len(unique_colors))
        
        # Métriques de taille
        metrics['max_input_area'] = max(input_areas)
        metrics['avg_input_area'] = np.mean(input_areas)
        metrics['max_input_height'] = max(dim[0] for dim in input_dimensions)
        metrics['max_input_width'] = max(dim[1] for dim in input_dimensions)
        metrics['input_dimensions'] = input_dimensions
        
        # Métriques de couleurs
        metrics['max_input_colors'] = max(input_color_counts)
        metrics['avg_input_colors'] = np.mean(input_color_counts)
        metrics['input_color_counts'] = input_color_counts
        
        # Métriques de complexité (depuis ARCAnalyzer)
        if 'complexity' in analysis:
            metrics['grid_complexity'] = analysis['complexity'].get('grid_complexity', 0)
            metrics['color_complexity'] = analysis['complexity'].get('color_complexity', 0)
            metrics['overall_complexity'] = analysis['complexity'].get('overall_complexity', 0)
        
        # Métriques de couleurs globales (depuis ARCAnalyzer)
        if 'colors' in analysis:
            metrics['total_input_colors'] = len(analysis['colors'].get('input_colors', []))
            metrics['color_frequency'] = analysis['colors'].get('color_frequency', {})
        
        # Densité de couleurs (couleurs par cellule)
        if metrics['avg_input_area'] > 0:
            metrics['color_density'] = metrics['avg_input_colors'] / metrics['avg_input_area']
        else:
            metrics['color_density'] = 0
            
        return metrics
    
    def calculate_mosaic_score(self, metrics: Dict) -> float:
        """Calcule un score de probabilité qu'un puzzle soit de type Mosaic"""
        
        score = 0.0
        
        # Critère 1: Grande taille d'input (poids: 0.4)
        area_score = 0.0
        if metrics['max_input_area'] >= 400:  # 20x20 ou plus
            area_score = 1.0
        elif metrics['max_input_area'] >= 225:  # 15x15 ou plus
            area_score = 0.8
        elif metrics['max_input_area'] >= 100:  # 10x10 ou plus
            area_score = 0.5
        elif metrics['max_input_area'] >= 64:   # 8x8 ou plus
            area_score = 0.3
        
        score += area_score * 0.4
        
        # Critère 2: Grand nombre de couleurs (poids: 0.4)
        color_score = 0.0
        if metrics['max_input_colors'] >= 8:
            color_score = 1.0
        elif metrics['max_input_colors'] >= 6:
            color_score = 0.8
        elif metrics['max_input_colors'] >= 5:
            color_score = 0.6
        elif metrics['max_input_colors'] >= 4:
            color_score = 0.4
        
        score += color_score * 0.4
        
        # Critère 3: Densité de couleurs élevée (poids: 0.2)
        density_score = 0.0
        if metrics['color_density'] >= 0.05:  # 5% des cellules ont des couleurs différentes
            density_score = 1.0
        elif metrics['color_density'] >= 0.03:
            density_score = 0.7
        elif metrics['color_density'] >= 0.02:
            density_score = 0.5
        elif metrics['color_density'] >= 0.01:
            density_score = 0.3
        
        score += density_score * 0.2
        
        return min(score, 1.0)  # Limiter à 1.0
    
    def scan_arc_puzzles(self, data_dir: str = "../arcdata/training") -> List[Dict]:
        """Scanne tous les puzzles ARC pour identifier les candidats Mosaic"""
        
        print("🔍 Recherche des puzzles de type MOSAIC...")
        print("=" * 50)
        
        mosaic_candidates = []
        total_puzzles = 0
        
        # Parcourir tous les fichiers JSON
        for filename in os.listdir(data_dir):
            if not filename.endswith('.json'):
                continue
                
            puzzle_id = filename.replace('.json', '')
            filepath = os.path.join(data_dir, filename)
            
            try:
                with open(filepath, 'r') as f:
                    puzzle_data = json.load(f)
                
                # Analyser pour Mosaic
                mosaic_metrics = self.analyze_puzzle_for_mosaic(puzzle_data, puzzle_id)
                
                total_puzzles += 1
                
                # Si c'est un candidat Mosaic, l'ajouter
                if mosaic_metrics['is_mosaic_candidate']:
                    mosaic_candidates.append(mosaic_metrics)
                    print(f"✅ MOSAIC détecté: {puzzle_id} (score: {mosaic_metrics['mosaic_score']:.3f})")
                    print(f"   Dimensions max: {mosaic_metrics['max_input_height']}x{mosaic_metrics['max_input_width']}")
                    print(f"   Couleurs max: {mosaic_metrics['max_input_colors']}")
                    print(f"   Aire max: {mosaic_metrics['max_input_area']}")
                    print()
                
            except Exception as e:
                print(f"❌ Erreur avec {puzzle_id}: {e}")
                continue
        
        # Trier par score décroissant
        mosaic_candidates.sort(key=lambda x: x['mosaic_score'], reverse=True)
        
        print(f"📊 RÉSULTATS FINAUX")
        print(f"Total puzzles analysés: {total_puzzles}")
        print(f"Candidats MOSAIC trouvés: {len(mosaic_candidates)}")
        print(f"Pourcentage: {len(mosaic_candidates)/total_puzzles*100:.1f}%")
        
        return mosaic_candidates
    
    def generate_mosaic_report(self, mosaic_candidates: List[Dict]) -> str:
        """Génère un rapport détaillé des puzzles Mosaic détectés"""
        
        if not mosaic_candidates:
            return "Aucun puzzle de type MOSAIC détecté."
        
        report = []
        report.append("🎨 RAPPORT DÉTAILLÉ - PUZZLES DE TYPE MOSAIC")
        report.append("=" * 60)
        report.append("")
        
        # Top 10 des candidats
        report.append("🏆 TOP 10 DES CANDIDATS MOSAIC")
        report.append("-" * 40)
        
        for i, candidate in enumerate(mosaic_candidates[:10], 1):
            report.append(f"{i:2d}. {candidate['puzzle_id']} (score: {candidate['mosaic_score']:.3f})")
            report.append(f"    Dimensions: {candidate['max_input_height']}x{candidate['max_input_width']} (aire: {candidate['max_input_area']})")
            report.append(f"    Couleurs: {candidate['max_input_colors']} max, {candidate['avg_input_colors']:.1f} moy")
            report.append(f"    Densité: {candidate['color_density']:.4f}")
            report.append("")
        
        # Statistiques globales
        report.append("📈 STATISTIQUES GLOBALES")
        report.append("-" * 30)
        
        scores = [c['mosaic_score'] for c in mosaic_candidates]
        areas = [c['max_input_area'] for c in mosaic_candidates]
        colors = [c['max_input_colors'] for c in mosaic_candidates]
        
        report.append(f"Score moyen: {np.mean(scores):.3f}")
        report.append(f"Aire moyenne: {np.mean(areas):.1f}")
        report.append(f"Couleurs moyennes: {np.mean(colors):.1f}")
        report.append("")
        
        # Liste complète des IDs
        report.append("📋 LISTE COMPLÈTE DES IDs MOSAIC")
        report.append("-" * 35)
        
        ids_by_score = [f"{c['puzzle_id']} ({c['mosaic_score']:.3f})" for c in mosaic_candidates]
        for i in range(0, len(ids_by_score), 3):
            line = " | ".join(ids_by_score[i:i+3])
            report.append(line)
        
        return "\n".join(report)
    
    def save_results(self, mosaic_candidates: List[Dict], output_file: str = "mosaic_puzzles_detected.json"):
        """Sauvegarde les résultats de détection"""
        
        results = {
            'detection_timestamp': '2024-12-19',
            'total_candidates': len(mosaic_candidates),
            'detection_criteria': {
                'min_area_threshold': 64,
                'min_colors_threshold': 4,
                'mosaic_score_threshold': 0.7
            },
            'candidates': mosaic_candidates
        }
        
        with open(output_file, 'w') as f:
            json.dump(results, f, indent=2)
        
        print(f"💾 Résultats sauvegardés dans: {output_file}")

def main():
    """Point d'entrée principal"""
    
    detector = MosaicPuzzleDetector()
    
    # Scanner les puzzles
    mosaic_candidates = detector.scan_arc_puzzles()
    
    # Générer le rapport
    report = detector.generate_mosaic_report(mosaic_candidates)
    print("\n" + report)
    
    # Sauvegarder les résultats
    detector.save_results(mosaic_candidates)
    
    # Sauvegarder aussi le rapport
    with open("mosaic_detection_report.txt", "w") as f:
        f.write(report)
    
    print(f"\n📄 Rapport sauvegardé dans: mosaic_detection_report.txt")

if __name__ == "__main__":
    main()