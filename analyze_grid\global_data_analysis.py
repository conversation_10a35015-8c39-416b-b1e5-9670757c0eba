#!/usr/bin/env python3
"""
Analyse globale de données : corrélation entre métriques ARCAnalyzer et commandes AGI
Approche data-driven pour découvrir les patterns réels
"""

import json
import os
import glob
import re
from typing import Dict, List, Any
from analyze_grid.arc_analyzer import ARCAnalyzer
import pandas as pd
import numpy as np
from collections import Counter, defaultdict

def get_all_puzzles() -> List[str]:
    """Récupère tous les puzzles ARC"""
    json_files = glob.glob("../arcdata/training/*.json")
    return [os.path.basename(f).replace('.json', '') for f in json_files]

def extract_agi_commands(agi_content: str) -> List[str]:
    """Extrait les commandes intelligentes du scénario AGI"""
    lines = agi_content.strip().split('\n')
    commands = []
    
    in_intelligent_section = False
    
    for line in lines:
        line = line.strip()
        
        # Ignorer les lignes vides
        if not line:
            continue
            
        # Détecter le début de la section intelligente
        if line.startswith('TRANSFERT') or line.startswith('INIT'):
            in_intelligent_section = True
            continue
            
        # Détecter la fin
        if line == 'END':
            break
            
        # Collecter les commandes dans la section intelligente
        if in_intelligent_section:
            # Nettoyer et normaliser la commande
            if line.startswith(('RESIZE', 'MOTIF', 'EXTRACT', 'REPLACE', 'REPLACES', 
                              'FLOODFILL', 'FILL', 'ROTATE', 'FLIP', 'EDITS', 'EDIT')):
                # Extraire le type de commande principal
                cmd_type = line.split()[0].split('{')[0]
                commands.append(cmd_type)
    
    return commands

def flatten_analysis(analysis: Dict, prefix: str = '') -> Dict[str, Any]:
    """Aplatit récursivement l'analyse ARCAnalyzer en métriques plates"""
    flat = {}
    
    for key, value in analysis.items():
        new_key = f"{prefix}_{key}" if prefix else key
        
        if isinstance(value, dict):
            flat.update(flatten_analysis(value, new_key))
        elif isinstance(value, list):
            if value and isinstance(value[0], (int, float)):
                # Listes numériques : calculer statistiques
                flat[f"{new_key}_count"] = len(value)
                flat[f"{new_key}_mean"] = np.mean(value) if value else 0
                flat[f"{new_key}_std"] = np.std(value) if len(value) > 1 else 0
                flat[f"{new_key}_min"] = min(value) if value else 0
                flat[f"{new_key}_max"] = max(value) if value else 0
            elif value and isinstance(value[0], dict):
                # Listes de dictionnaires : compter
                flat[f"{new_key}_count"] = len(value)
            else:
                # Autres listes : longueur
                flat[f"{new_key}_count"] = len(value)
        elif isinstance(value, (int, float, bool)):
            flat[new_key] = value
        elif isinstance(value, str):
            # Encoder les strings comme booléens ou compter
            if value in ['True', 'False']:
                flat[new_key] = value == 'True'
            else:
                flat[f"{new_key}_present"] = 1
        else:
            # Autres types : marquer comme présent
            flat[f"{new_key}_present"] = 1 if value else 0
    
    return flat

def analyze_puzzle_complete(puzzle_id: str) -> Dict[str, Any]:
    """Analyse complète d'un puzzle : métriques + commandes"""
    json_file = f"../arcdata/training/{puzzle_id}.json"
    agi_file = f"../arcdata/training/{puzzle_id}_TEST0_VALID.agi"
    
    result: Dict[str, Any] = {'puzzle_id': puzzle_id}
    
    try:
        # Analyser avec ARCAnalyzer
        if os.path.exists(json_file):
            with open(json_file, 'r') as f:
                puzzle = json.load(f)
            
            analyzer = ARCAnalyzer()
            analysis = analyzer.analyze_puzzle(puzzle)
            
            # Aplatir toutes les métriques
            flat_metrics = flatten_analysis(analysis)
            result.update(flat_metrics)
        
        # Extraire commandes AGI
        if os.path.exists(agi_file):
            with open(agi_file, 'r', encoding='utf-8') as f:
                agi_content = f.read()
            
            commands = extract_agi_commands(agi_content)
            
            # Encoder les commandes comme features binaires
            all_command_types = ['RESIZE', 'MOTIF', 'EXTRACT', 'REPLACE', 'REPLACES', 
                               'FLOODFILL', 'FILL', 'ROTATE', 'FLIP', 'EDITS', 'EDIT']
            
            for cmd_type in all_command_types:
                result[f"uses_{cmd_type}"] = str(cmd_type in commands)
                result[f"count_{cmd_type}"] = str(commands.count(cmd_type))
            
            result['total_commands'] = str(len(commands))
            result['unique_commands'] = str(len(set(commands)))
            result['command_sequence'] = '_'.join(commands[:5])  # 5 premières commandes
        
        result['success'] = "True"
        
    except Exception as e:
        result['error'] = str(e)
        result['success'] = "False"
    
    return result

def compute_correlations(df: pd.DataFrame) -> Dict[str, Any]:
    """Calcule les corrélations entre métriques et commandes"""
    
    # Séparer les colonnes métriques et commandes
    metric_cols = [col for col in df.columns if not col.startswith(('uses_', 'count_', 'puzzle_id', 'success', 'error'))]
    command_cols = [col for col in df.columns if col.startswith(('uses_', 'count_'))]
    
    correlations = {}
    
    # Pour chaque commande, trouver les métriques les plus corrélées
    for cmd_col in command_cols:
        if df[cmd_col].sum() > 5:  # Au moins 5 occurrences
            cmd_correlations = []
            
            for metric_col in metric_cols:
                if df[metric_col].dtype in ['int64', 'float64', 'bool']:
                    try:
                        corr = df[cmd_col].corr(df[metric_col])
                        if not pd.isna(corr) and abs(corr) > 0.1:  # Corrélation significative
                            cmd_correlations.append((metric_col, corr))
                    except:
                        continue
            
            # Trier par corrélation absolue
            cmd_correlations.sort(key=lambda x: abs(x[1]), reverse=True)
            correlations[cmd_col] = cmd_correlations[:10]  # Top 10
    
    return correlations

def main():
    """Analyse globale de données"""
    print("🔍 ANALYSE GLOBALE DE DONNÉES ARC")
    print("Corrélation métriques ARCAnalyzer ↔ commandes AGI")
    print("=" * 60)
    
    # Collecter tous les puzzles
    all_puzzles = get_all_puzzles()
    print(f"📋 {len(all_puzzles)} puzzles à analyser")
    
    # Analyser tous les puzzles
    print("\n🧪 Collecte des données...")
    all_data = []
    
    for i, puzzle_id in enumerate(all_puzzles):
        if (i + 1) % 50 == 0:
            print(f"  Progress: {i+1}/{len(all_puzzles)} ({(i+1)/len(all_puzzles)*100:.1f}%)")
        
        data = analyze_puzzle_complete(puzzle_id)
        all_data.append(data)
    
    # Créer DataFrame
    print("\n📊 Création de la matrice de données...")
    df = pd.DataFrame(all_data)
    
    # Nettoyer les données
    successful_data = df[df['success'] == True].copy()
    print(f"Données valides: {len(successful_data)}/{len(df)}")
    
    # Remplir les NaN avec 0 pour les métriques numériques
    numeric_cols = successful_data.select_dtypes(include=[np.number]).columns
    successful_data[numeric_cols] = successful_data[numeric_cols].fillna(0)
    
    # Statistiques descriptives
    print(f"\n📈 Statistiques des commandes:")
    command_stats = {}
    for col in successful_data.columns:
        if col.startswith('uses_'):
            cmd = col.replace('uses_', '')
            count = successful_data[col].sum()
            percentage = count / len(successful_data) * 100
            command_stats[cmd] = {'count': count, 'percentage': percentage}
            print(f"  {cmd}: {count} puzzles ({percentage:.1f}%)")
    
    # Calculer corrélations
    print(f"\n🔗 Calcul des corrélations...")
    correlations = compute_correlations(successful_data)
    
    # Afficher les corrélations les plus intéressantes
    print(f"\n🎯 CORRÉLATIONS DÉCOUVERTES:")
    print("=" * 40)
    
    for cmd, corr_list in correlations.items():
        if corr_list:  # Si il y a des corrélations
            cmd_name = cmd.replace('uses_', '').replace('count_', '')
            usage = command_stats.get(cmd_name, {}).get('count', 0)
            print(f"\n🔧 {cmd_name} ({usage} puzzles):")
            
            for metric, corr_value in corr_list[:5]:  # Top 5
                direction = "↗️" if corr_value > 0 else "↘️"
                print(f"  {direction} {metric}: {corr_value:.3f}")
    
    # Sauvegarder les résultats
    results = {
        'analysis_date': pd.Timestamp.now().isoformat(),
        'total_puzzles': len(all_puzzles),
        'successful_analyses': len(successful_data),
        'command_statistics': command_stats,
        'correlations': {k: [(m, float(c)) for m, c in v] for k, v in correlations.items()},
        'data_summary': {
            'total_metrics': len([col for col in successful_data.columns if not col.startswith(('uses_', 'count_', 'puzzle_'))]),
            'total_commands': len([col for col in successful_data.columns if col.startswith('uses_')]),
        }
    }
    
    with open('global_arc_analysis.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    # Sauvegarder la matrice de données
    successful_data.to_csv('arc_data_matrix.csv', index=False)
    
    print(f"\n💾 Résultats sauvegardés:")
    print(f"  - global_arc_analysis.json: Corrélations et statistiques")
    print(f"  - arc_data_matrix.csv: Matrice complète des données")
    
    print(f"\n🎯 INSIGHTS PRINCIPAUX:")
    print(f"  - {len(successful_data)} puzzles analysés avec succès")
    print(f"  - {len([col for col in successful_data.columns if not col.startswith(('uses_', 'count_', 'puzzle_'))])} métriques extraites")
    print(f"  - {len(correlations)} commandes avec corrélations significatives")
    print(f"  - Prêt pour analyse approfondie et machine learning")

if __name__ == "__main__":
    main()