#!/usr/bin/env python3
"""
Analyse détaillée d'un puzzle spécifique pour comprendre ses caractéristiques
"""

import json
import os
import numpy as np
from collections import Counter
from typing import Dict
from analyze_grid.arc_analyzer import ARCAnalyzer

def analyze_color_usage(grid: np.ndarray) -> Dict:
    """Analyse l'utilisation des couleurs dans une grille"""
    
    total_cells = grid.size
    color_counts = Counter(grid.flatten())
    
    # Retirer le fond noir pour l'analyse
    if 0 in color_counts:
        background_cells = color_counts[0]
        del color_counts[0]
    else:
        background_cells = 0
    
    non_background_cells = total_cells - background_cells
    
    analysis = {
        'total_cells': total_cells,
        'background_cells': background_cells,
        'non_background_cells': non_background_cells,
        'unique_colors': len(color_counts),
        'color_counts': dict(color_counts),
        'color_distribution': {},
        'color_usage_ratio': non_background_cells / total_cells if total_cells > 0 else 0,
        'color_balance_score': 0.0,
        'dominant_color_ratio': 0.0
    }
    
    # Calculer la distribution des couleurs
    if non_background_cells > 0:
        for color, count in color_counts.items():
            analysis['color_distribution'][color] = count / non_background_cells
    
    # Calculer le score d'équilibre des couleurs
    if len(color_counts) > 0:
        color_ratios = list(analysis['color_distribution'].values())
        
        # Score d'équilibre : plus les couleurs sont équilibrées, plus le score est élevé
        if len(color_ratios) > 1:
            # Variance des ratios (plus faible = plus équilibré)
            variance = np.var(color_ratios)
            # Score inversé : variance faible = score élevé
            analysis['color_balance_score'] = 1.0 / (1.0 + variance * 10)
        else:
            analysis['color_balance_score'] = 0.0  # Une seule couleur = pas équilibré
        
        # Ratio de la couleur dominante
        analysis['dominant_color_ratio'] = max(color_ratios)
    
    return analysis

def analyze_puzzle_detailed(puzzle_id: str, data_dir: str = "../arcdata/training"):
    """Analyse détaillée d'un puzzle spécifique"""
    
    filepath = os.path.join(data_dir, f"{puzzle_id}.json")
    
    try:
        with open(filepath, 'r') as f:
            puzzle_data = json.load(f)
        
        print(f"🔍 ANALYSE DÉTAILLÉE DU PUZZLE: {puzzle_id}")
        print("=" * 60)
        
        # Analyse avec ARCAnalyzer
        analyzer = ARCAnalyzer()
        analysis = analyzer.analyze_puzzle(puzzle_data)
        
        print(f"\n📊 MÉTRIQUES GÉNÉRALES")
        print("-" * 30)
        
        # Informations de base
        grid_info = analysis.get('grid_info', {})
        print(f"Exemples d'entraînement: {grid_info.get('train_examples', 0)}")
        
        if 'input_dimensions' in grid_info:
            for i, dim in enumerate(grid_info['input_dimensions']):
                print(f"Exemple {i+1} - Input: {dim[0]}x{dim[1]} ({dim[0]*dim[1]} cellules)")
        
        # Analyse des couleurs
        colors_info = analysis.get('colors', {})
        print(f"\nCouleurs d'entrée: {colors_info.get('input_colors', [])}")
        print(f"Nombre de couleurs: {len(colors_info.get('input_colors', []))}")
        
        # Analyse de la complexité
        complexity = analysis.get('complexity', {})
        print(f"\n📈 COMPLEXITÉ")
        print("-" * 20)
        print(f"Complexité grille: {complexity.get('grid_complexity', 0):.2f}")
        print(f"Complexité couleurs: {complexity.get('color_complexity', 0):.2f}")
        print(f"Complexité globale: {complexity.get('overall_complexity', 0):.2f}")
        
        # Analyse des symétries
        symmetries = analysis.get('symmetries', {})
        print(f"\n🔄 SYMÉTRIES")
        print("-" * 15)
        
        if 'input_symmetries' in symmetries:
            for i, sym in enumerate(symmetries['input_symmetries']):
                print(f"Exemple {i+1}:")
                print(f"  Horizontale: {sym.get('horizontal', False)}")
                print(f"  Verticale: {sym.get('vertical', False)}")
                print(f"  Diagonale principale: {sym.get('diagonal_main', False)}")
                print(f"  Diagonale anti: {sym.get('diagonal_anti', False)}")
        
        # Analyse détaillée des couleurs pour chaque exemple
        print(f"\n🎨 ANALYSE DÉTAILLÉE DES COULEURS")
        print("-" * 40)
        
        for i, example in enumerate(puzzle_data['train']):
            input_grid = np.array(example['input'])
            
            print(f"\nExemple {i+1}:")
            color_analysis = analyze_color_usage(input_grid)
            
            print(f"  Cellules totales: {color_analysis['total_cells']}")
            print(f"  Cellules de fond: {color_analysis['background_cells']}")
            print(f"  Cellules colorées: {color_analysis['non_background_cells']}")
            print(f"  Ratio d'utilisation: {color_analysis['color_usage_ratio']:.3f}")
            print(f"  Couleurs uniques: {color_analysis['unique_colors']}")
            print(f"  Score d'équilibre: {color_analysis['color_balance_score']:.3f}")
            print(f"  Ratio couleur dominante: {color_analysis['dominant_color_ratio']:.3f}")
            
            print(f"  Distribution des couleurs:")
            for color, ratio in color_analysis['color_distribution'].items():
                count = color_analysis['color_counts'][color]
                print(f"    Couleur {color}: {count} cellules ({ratio:.3f})")
        
        # Patterns détectés
        patterns = analysis.get('patterns', {})
        print(f"\n🔍 PATTERNS DÉTECTÉS")
        print("-" * 25)
        
        for pattern_type, pattern_info in patterns.items():
            if isinstance(pattern_info, dict) and pattern_info.get('detected', False):
                print(f"✅ {pattern_type.upper()}: {pattern_info}")
            elif pattern_type == 'motif':
                motif_info = pattern_info
                print(f"MOTIF détecté: {motif_info.get('detected', False)}")
                if motif_info.get('detected'):
                    print(f"  Type: {motif_info.get('type', 'N/A')}")
                    print(f"  Confiance: {motif_info.get('confidence', 0):.3f}")
        
        return analysis
        
    except Exception as e:
        print(f"❌ Erreur avec {puzzle_id}: {e}")
        return None

def main():
    """Analyse de puzzles spécifiques"""
    
    # Analyser le puzzle problématique
    puzzle_id = "6ecd11f4"
    print(f"Analyse du puzzle potentiellement problématique: {puzzle_id}")
    
    analysis = analyze_puzzle_detailed(puzzle_id)
    
    if analysis:
        print(f"\n🎯 ÉVALUATION POUR MOSAIC")
        print("-" * 30)
        
        # Critères pour un vrai puzzle Mosaic
        print("Critères attendus pour un puzzle MOSAIC:")
        print("1. Grande taille (≥ 15x15)")
        print("2. Nombreuses couleurs (≥ 6)")
        print("3. Utilisation équilibrée des couleurs (score ≥ 0.5)")
        print("4. Ratio d'utilisation élevé (≥ 0.7)")
        print("5. Pas de symétries fortes")
        
        # Recommandation
        print(f"\n💡 RECOMMANDATION")
        print("Ce puzzle devrait-il être exclu de la catégorie MOSAIC ?")
    
    # Analyser quelques autres candidats pour comparaison
    other_candidates = ["3631a71a", "8731374e", "91714a58"]
    
    for candidate_id in other_candidates:
        print(f"\n" + "="*80)
        analyze_puzzle_detailed(candidate_id)

if __name__ == "__main__":
    main()