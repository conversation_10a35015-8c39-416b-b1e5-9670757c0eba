#!/usr/bin/env python3
"""
Script pour analyser les puzzles ARC qui utilisent la commande MOTIF
et identifier les patterns communs pour améliorer ARCAnalyzer
"""

import os
import json
import glob
from typing import Dict, List, Tuple
from analyze_grid.arc_analyzer import ARCAnalyzer

def find_motif_puzzles() -> List[str]:
    """Trouve tous les puzzles qui utilisent MOTIF dans leurs solutions"""
    motif_puzzles = []
    
    # Chercher dans tous les fichiers .agi
    agi_files = glob.glob("../arcdata/training/*.agi")
    
    for agi_file in agi_files:
        try:
            with open(agi_file, 'r', encoding='utf-8') as f:
                content = f.read()
                if 'MOTIF' in content:
                    # Extraire le nom du puzzle
                    puzzle_name = os.path.basename(agi_file).replace('_TEST0_VALID.agi', '')
                    motif_puzzles.append(puzzle_name)
        except Exception as e:
            print(f"Erreur lecture {agi_file}: {e}")
    
    return motif_puzzles

def analyze_motif_puzzle(puzzle_id: str) -> Dict:
    """Analyse un puzzle spécifique avec MOTIF"""
    
    # Charger les données du puzzle
    json_file = f"../arcdata/training/{puzzle_id}.json"
    agi_file = f"../arcdata/training/{puzzle_id}_TEST0_VALID.agi"
    
    if not os.path.exists(json_file) or not os.path.exists(agi_file):
        return {"error": f"Fichiers manquants pour {puzzle_id}"}
    
    try:
        # Charger puzzle data
        with open(json_file, 'r') as f:
            puzzle_data = json.load(f)
        
        # Charger solution AGI
        with open(agi_file, 'r', encoding='utf-8') as f:
            agi_content = f.read()
        
        # Analyser avec ARCAnalyzer
        analyzer = ARCAnalyzer()
        analysis = analyzer.analyze_puzzle(puzzle_data)
        
        # Extraire les commandes MOTIF
        motif_commands = []
        for line in agi_content.split('\n'):
            if 'MOTIF' in line:
                motif_commands.append(line.strip())
        
        # Analyser les dimensions
        train_examples = puzzle_data['train']
        dimensions_analysis = []
        
        for i, example in enumerate(train_examples):
            input_shape = (len(example['input']), len(example['input'][0]))
            output_shape = (len(example['output']), len(example['output'][0]))
            
            dimensions_analysis.append({
                'example': i,
                'input_shape': input_shape,
                'output_shape': output_shape,
                'ratio': (output_shape[0] / input_shape[0], output_shape[1] / input_shape[1]),
                'is_multiple': (output_shape[0] % input_shape[0] == 0 and 
                               output_shape[1] % input_shape[1] == 0)
            })
        
        return {
            'puzzle_id': puzzle_id,
            'motif_commands': motif_commands,
            'dimensions_analysis': dimensions_analysis,
            'arc_analysis': {
                'scaling_detected': analysis['patterns']['scaling']['detected'],
                'scaling_factors': analysis['patterns']['scaling']['factors'],
                'repetition_detected': analysis['patterns']['repetition']['detected'],
                'transformations': analysis['transformations']
            }
        }
        
    except Exception as e:
        return {"error": f"Erreur analyse {puzzle_id}: {e}"}

def main():
    """Analyse principale"""
    print("🔍 Recherche des puzzles MOTIF...")
    
    motif_puzzles = find_motif_puzzles()
    print(f"Trouvé {len(motif_puzzles)} puzzles avec MOTIF:")
    
    # Analyser les premiers puzzles pour identifier les patterns
    sample_puzzles = motif_puzzles[:10]  # Commencer avec 10 puzzles
    
    results = []
    for puzzle_id in sample_puzzles:
        print(f"Analyse de {puzzle_id}...")
        result = analyze_motif_puzzle(puzzle_id)
        results.append(result)
        
        if 'error' not in result:
            print(f"  ✅ {puzzle_id}: {len(result['motif_commands'])} commandes MOTIF")
            for cmd in result['motif_commands']:
                print(f"    {cmd}")
        else:
            print(f"  ❌ {puzzle_id}: {result['error']}")
    
    # Sauvegarder les résultats
    with open('motif_analysis_results.json', 'w') as f:
        json.dump({
            'total_motif_puzzles': len(motif_puzzles),
            'all_motif_puzzles': motif_puzzles,
            'sample_analysis': results
        }, f, indent=2)
    
    print(f"\n📊 Résultats sauvegardés dans 'motif_analysis_results.json'")
    
    # Statistiques rapides
    successful_analyses = [r for r in results if 'error' not in r]
    print(f"\n📈 Statistiques:")
    print(f"  - Puzzles analysés avec succès: {len(successful_analyses)}")
    
    if successful_analyses:
        # Analyser les patterns de dimensions
        multiple_ratios = []
        for result in successful_analyses:
            for dim_analysis in result['dimensions_analysis']:
                if dim_analysis['is_multiple']:
                    multiple_ratios.append(dim_analysis['ratio'])
        
        print(f"  - Puzzles avec ratios multiples: {len(multiple_ratios)}")
        if multiple_ratios:
            unique_ratios = list(set(multiple_ratios))
            print(f"  - Ratios uniques: {unique_ratios}")

if __name__ == "__main__":
    main()