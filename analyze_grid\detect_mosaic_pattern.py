import numpy as np
from collections import Counter
import matplotlib.pyplot as plt

def detect_mosaic_pattern(grid, empty_value=0, min_block_size=3):
    """
    Détecte si une grille représente une mosaïque avec des zones manquantes
    
    Args:
        grid: grille 2D (liste de listes ou array numpy)
        empty_value: valeur représentant les zones vides (défaut: 0)
        min_block_size: taille minimale des blocs à considérer
    
    Returns:
        dict: résultats de l'analyse
    """
    grid = np.array(grid)
    h, w = grid.shape
    
    results = {
        'is_mosaic': False,
        'confidence': 0.0,
        'evidence': [],
        'empty_regions': [],
        'pattern_blocks': [],
        'symmetries': []
    }
    
    # 1. Analyser la distribution des valeurs
    unique_vals, counts = np.unique(grid, return_counts=True)
    value_dist = dict(zip(unique_vals, counts))
    
    # Vérifier si il y a beaucoup de zones vides
    if empty_value in value_dist:
        empty_ratio = value_dist[empty_value] / (h * w)
        if empty_ratio > 0.15:  # Plus de 15% de vide
            results['evidence'].append(f"Zone vide importante: {empty_ratio:.2%}")
    
    # 2. Détecter les blocs rectangulaires de motifs
    pattern_blocks = find_rectangular_blocks(grid, empty_value, min_block_size)
    results['pattern_blocks'] = pattern_blocks
    
    if len(pattern_blocks) >= 4:  # Au moins 4 blocs de motifs
        results['evidence'].append(f"Trouvé {len(pattern_blocks)} blocs de motifs rectangulaires")
    
    # 3. Analyser les symétries potentielles
    symmetries = analyze_symmetries(grid, pattern_blocks, empty_value)
    results['symmetries'] = symmetries
    
    if len(symmetries) > 0:
        results['evidence'].append(f"Trouvé {len(symmetries)} symétries potentielles")
    
    # 4. Détecter les zones vides rectangulaires
    empty_regions = find_empty_regions(grid, empty_value, min_block_size)
    results['empty_regions'] = empty_regions
    
    if len(empty_regions) >= 2:
        results['evidence'].append(f"Trouvé {len(empty_regions)} zones vides rectangulaires")
    
    # 5. Vérifier les correspondances entre zones pleines et vides
    correspondences = find_pattern_correspondences(grid, pattern_blocks, empty_regions)
    
    if len(correspondences) > 0:
        results['evidence'].append(f"Trouvé {len(correspondences)} correspondances motif-zone vide")
    
    # 6. Calculer le score de confiance
    confidence = calculate_mosaic_confidence(results, h, w)
    results['confidence'] = confidence
    results['is_mosaic'] = confidence > 0.6
    
    return results

def find_rectangular_blocks(grid, empty_value, min_size):
    """Trouve les blocs rectangulaires de motifs non-vides"""
    h, w = grid.shape
    blocks = []
    visited = np.zeros_like(grid, dtype=bool)
    
    for i in range(h):
        for j in range(w):
            if not visited[i, j] and grid[i, j] != empty_value:
                # Trouver le bloc rectangulaire maximal
                block = find_max_rectangle(grid, i, j, empty_value, visited)
                if block and (block[2] - block[0] >= min_size or block[3] - block[1] >= min_size):
                    blocks.append(block)
    
    return blocks

def find_max_rectangle(grid, start_i, start_j, empty_value, visited):
    """Trouve le rectangle maximal de motifs cohérents"""
    h, w = grid.shape
    
    # Simple implémentation : trouve un rectangle de même valeur
    value = grid[start_i, start_j]
    if value == empty_value:
        return None
    
    # Expansion vers la droite
    max_j = start_j
    while max_j < w and grid[start_i, max_j] == value:
        max_j += 1
    
    # Expansion vers le bas
    max_i = start_i
    valid = True
    while max_i < h and valid:
        for j in range(start_j, max_j):
            if grid[max_i, j] != value:
                valid = False
                break
        if valid:
            max_i += 1
    
    # Marquer comme visité
    for i in range(start_i, max_i):
        for j in range(start_j, max_j):
            visited[i, j] = True
    
    return (start_i, start_j, max_i, max_j)

def find_empty_regions(grid, empty_value, min_size):
    """Trouve les régions vides rectangulaires"""
    h, w = grid.shape
    regions = []
    visited = np.zeros_like(grid, dtype=bool)
    
    for i in range(h):
        for j in range(w):
            if not visited[i, j] and grid[i, j] == empty_value:
                region = find_empty_rectangle(grid, i, j, empty_value, visited)
                if region and (region[2] - region[0] >= min_size and region[3] - region[1] >= min_size):
                    regions.append(region)
    
    return regions

def find_empty_rectangle(grid, start_i, start_j, empty_value, visited):
    """Trouve le rectangle maximal de zones vides"""
    h, w = grid.shape
    
    # Expansion vers la droite
    max_j = start_j
    while max_j < w and grid[start_i, max_j] == empty_value:
        max_j += 1
    
    # Expansion vers le bas
    max_i = start_i
    valid = True
    while max_i < h and valid:
        for j in range(start_j, max_j):
            if grid[max_i, j] != empty_value:
                valid = False
                break
        if valid:
            max_i += 1
    
    # Marquer comme visité
    for i in range(start_i, max_i):
        for j in range(start_j, max_j):
            visited[i, j] = True
    
    return (start_i, start_j, max_i, max_j)

def analyze_symmetries(grid, pattern_blocks, empty_value):
    """Analyse les symétries potentielles entre les blocs"""
    symmetries = []
    
    for i, block1 in enumerate(pattern_blocks):
        for j, block2 in enumerate(pattern_blocks):
            if i != j:
                # Vérifier symétrie horizontale
                if is_horizontal_symmetry(grid, block1, block2):
                    symmetries.append({
                        'type': 'horizontal',
                        'block1': block1,
                        'block2': block2
                    })
                
                # Vérifier symétrie verticale
                if is_vertical_symmetry(grid, block1, block2):
                    symmetries.append({
                        'type': 'vertical',
                        'block1': block1,
                        'block2': block2
                    })
    
    return symmetries

def is_horizontal_symmetry(grid, block1, block2):
    """Vérifie si deux blocs sont symétriques horizontalement"""
    r1, c1, r1_end, c1_end = block1
    r2, c2, r2_end, c2_end = block2
    
    # Même taille ?
    if (r1_end - r1) != (r2_end - r2) or (c1_end - c1) != (c2_end - c2):
        return False
    
    # Extraire les blocs
    pattern1 = grid[r1:r1_end, c1:c1_end]
    pattern2 = grid[r2:r2_end, c2:c2_end]
    
    # Comparer avec flip horizontal
    return np.array_equal(pattern1, np.fliplr(pattern2))

def is_vertical_symmetry(grid, block1, block2):
    """Vérifie si deux blocs sont symétriques verticalement"""
    r1, c1, r1_end, c1_end = block1
    r2, c2, r2_end, c2_end = block2
    
    # Même taille ?
    if (r1_end - r1) != (r2_end - r2) or (c1_end - c1) != (c2_end - c2):
        return False
    
    # Extraire les blocs
    pattern1 = grid[r1:r1_end, c1:c1_end]
    pattern2 = grid[r2:r2_end, c2:c2_end]
    
    # Comparer avec flip vertical
    return np.array_equal(pattern1, np.flipud(pattern2))

def find_pattern_correspondences(grid, pattern_blocks, empty_regions):
    """Trouve les correspondances entre motifs et zones vides"""
    correspondences = []
    
    for empty_region in empty_regions:
        er, ec, er_end, ec_end = empty_region
        empty_h, empty_w = er_end - er, ec_end - ec
        
        for pattern_block in pattern_blocks:
            pr, pc, pr_end, pc_end = pattern_block
            pattern_h, pattern_w = pr_end - pr, pc_end - pc
            
            # Même taille ?
            if pattern_h == empty_h and pattern_w == empty_w:
                correspondences.append({
                    'empty_region': empty_region,
                    'pattern_block': pattern_block,
                    'size_match': True
                })
    
    return correspondences

def calculate_mosaic_confidence(results, height, width):
    """Calcule un score de confiance pour la détection de mosaïque"""
    score = 0.0
    
    # Points pour les zones vides
    if len(results['empty_regions']) >= 2:
        score += 0.3
    
    # Points pour les blocs de motifs
    if len(results['pattern_blocks']) >= 4:
        score += 0.3
    
    # Points pour les symétries
    if len(results['symmetries']) > 0:
        score += 0.4
    
    # Bonus pour la taille appropriée
    if height >= 20 and width >= 20:
        score += 0.1
    
    return min(score, 1.0)

# Fonction de test
def test_mosaic_detector():
    """Teste le détecteur avec l'exemple donné"""
    # Données du puzzle ARC (simplifiées pour le test)
    test_grid = [
        [8, 0, 7, 0, 7, 7, 1, 1, 0, 3, 0, 6, 0, 8, 0, 0, 0, 0, 8, 0, 6, 0, 3, 0, 1, 1, 7, 7, 0, 7],
        [0, 8, 0, 0, 7, 7, 1, 1, 3, 3, 6, 6, 8, 8, 0, 0, 0, 0, 8, 8, 6, 6, 3, 3, 1, 1, 7, 7, 0, 0],
        # ... (ajoutez plus de lignes pour un test complet)
    ]
    
    # Pour ce test, créons une grille simple avec un motif de mosaïque clair
    simple_mosaic = np.zeros((12, 12))
    # Ajouter quelques blocs de motifs
    simple_mosaic[0:3, 0:3] = 1
    simple_mosaic[0:3, 9:12] = 2  # Symétrique
    simple_mosaic[9:12, 0:3] = 3
    simple_mosaic[9:12, 9:12] = 4
    
    results = detect_mosaic_pattern(simple_mosaic)
    
    print("=== RÉSULTATS DE DÉTECTION ===")
    print(f"Est une mosaïque: {results['is_mosaic']}")
    print(f"Confiance: {results['confidence']:.2%}")
    print(f"Preuves trouvées:")
    for evidence in results['evidence']:
        print(f"  - {evidence}")
    print(f"Blocs de motifs: {len(results['pattern_blocks'])}")
    print(f"Régions vides: {len(results['empty_regions'])}")
    print(f"Symétries: {len(results['symmetries'])}")
    
    return results

if __name__ == "__main__":
    test_mosaic_detector()