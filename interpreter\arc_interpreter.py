#!/usr/bin/env python3
"""
Facade pour l'interpréteur de programmes ARC.

Ce module fournit une interface simple pour exécuter un programme textuel
sur une grille d'entrée. Il utilise le CommandDecompressor et le CommandExecutor
du répertoire `src` pour assurer une exécution cohérente avec le reste du projet.
"""

import numpy as np
from ..src.command_decompressor import CommandDecompressor
from ..src.command_executor import CommandExecutor

def execute_arc_program(program_str: str, input_grid: list[list[int]]) -> np.ndarray | None:
    """
    Exécute un programme de commandes ARC sur une grille d'entrée.

    Le processus est le suivant :
    1. Les commandes groupées (ex: FILLS {...}) sont décompressées en commandes atomiques.
    2. Chaque commande atomique est exécutée séquentiellement par l'exécuteur.
    Args:
        program_str: La chaîne de caractères contenant le programme complet.
        input_grid: La grille d'entrée sous forme de liste de listes d'entiers.
    Returns:
        La grille de sortie sous forme d'array numpy si l'exécution réussit,
        sinon None.
    """
    
    try:
        # 1. Décompresser le programme en commandes individuelles
        decompressor = CommandDecompressor()
        commands_list = program_str.strip().split('\n')
        decompressed_commands = decompressor.decompress_commands(commands_list)

        # 2. Initialiser l'exécuteur et la grille d'entrée
        executor = CommandExecutor()
        
        # La commande INIT doit être la première pour définir la taille
        # Si le programme ne commence pas par INIT, on l'ajoute implicitement
        if not decompressed_commands or not decompressed_commands[0].startswith('INIT'):
            height = len(input_grid)
            width = len(input_grid[0]) if height > 0 else 0
            init_command = f"INIT {width}x{height}"
            decompressed_commands.insert(0, init_command)
        
        # Assigner la grille initiale à l'exécuteur après l'initialisation
        executor.grid = np.array(input_grid, dtype=int)
        executor.height, executor.width = executor.grid.shape

        # 3. Exécuter les commandes
        result = executor.execute_commands(decompressed_commands)

        if result["success"]:
            return np.array(result["grid"])
        else:
            print(f"Erreur lors de l'exécution du programme: {result['error']}")
            return None

    except Exception as e:
        import traceback
        print(f"Exception inattendue dans execute_arc_program: {e}\n{traceback.format_exc()}")
        return None
