#!/usr/bin/env python3
"""
Affichage détaillé des résultats MosaicSolver pour la tâche 3631a71a
"""

import json
import numpy as np
from pathlib import Path

def display_mosaic_details():
    """Affiche les détails de la mosaïque détectée dans 3631a71a"""
    
    # Charger les résultats
    results_file = Path("mosaic_solver_specific_tasks_results.json")
    if not results_file.exists():
        print("❌ Fichier de résultats non trouvé.")
        return
    
    with open(results_file, 'r', encoding='utf-8') as f:
        all_results = json.load(f)
    
    task_id = "3631a71a"
    if task_id not in all_results:
        print(f"❌ Tâche {task_id} non trouvée")
        return
    
    task_results = all_results[task_id]
    
    print("=" * 80)
    print(f"ANALYSE DÉTAILLÉE DE LA MOSAÏQUE - TÂCHE {task_id}")
    print("=" * 80)
    
    # Résumé général
    summary = task_results['summary']
    print(f"\n📊 RÉSUMÉ GÉNÉRAL:")
    print(f"   • Mosaïques détectées (inputs): {summary['mosaic_detected_inputs']}/4")
    print(f"   • Mosaïques détectées (outputs): {summary['mosaic_detected_outputs']}/4")
    print(f"   • Confiance moyenne inputs: {summary['avg_confidence_inputs']:.2f}")
    print(f"   • Confiance moyenne outputs: {summary['avg_confidence_outputs']:.2f}")
    
    if summary['best_mosaic_example']:
        best = summary['best_mosaic_example']
        print(f"   • Meilleur exemple: {best['grid_type']} {best['example_idx']} (confiance: {best['confidence']:.2f})")
        print(f"   • Nombre de transformations: {best['num_transformations']}")
    
    # Analyser le meilleur exemple en détail
    print(f"\n🔍 ANALYSE DU MEILLEUR EXEMPLE:")
    
    # Trouver le meilleur exemple
    all_examples = task_results['train_inputs'] + task_results['train_outputs'] + task_results['test_inputs']
    best_example = max(all_examples, key=lambda x: x['confidence'])
    
    meta = best_example['metadata']
    print(f"   • Type: {meta['grid_type']} exemple {meta['example_idx']}")
    print(f"   • Forme de grille: {meta['grid_shape']}")
    print(f"   • Valeurs uniques: {meta['unique_values']}")
    print(f"   • Cellules vides: {meta['empty_cells']}")
    print(f"   • Confiance: {best_example['confidence']:.2f}")
    
    print(f"\n📦 BLOCS SOURCES DÉTECTÉS:")
    for i, block in enumerate(best_example['source_blocks']):
        coords = block['coords']
        size = block['size']
        area = block['area']
        print(f"   {i+1}. Bloc [{coords[0]},{coords[1]} {coords[2]-1},{coords[3]-1}] - Taille: {size} - Aire: {area}")
    
    print(f"\n🎯 RÉGIONS CIBLES DÉTECTÉES:")
    for i, region in enumerate(best_example['target_regions']):
        coords = region['coords']
        size = region['size']
        area = region['area']
        print(f"   {i+1}. Région [{coords[0]},{coords[1]} {coords[2]-1},{coords[3]-1}] - Taille: {size} - Aire: {area}")
    
    print(f"\n🔧 TRANSFORMATIONS PROPOSÉES (Top 10):")
    for i, trans in enumerate(best_example['proposed_transformations'][:10]):
        print(f"   {i+1}. {trans['operation']}")
        print(f"      Score: {trans['score']:.2f}")
        source_coords = trans['source']['coords']
        target_coords = trans['target']['coords']
        print(f"      Source: [{source_coords[0]},{source_coords[1]} {source_coords[2]-1},{source_coords[3]-1}]")
        print(f"      Cible: [{target_coords[0]},{target_coords[1]} {target_coords[2]-1},{target_coords[3]-1}]")
        print()
    
    # Analyser les patterns de transformation
    print(f"\n📈 ANALYSE DES PATTERNS DE TRANSFORMATION:")
    transformations = best_example['proposed_transformations']
    
    # Compter les types de transformation
    trans_types = {}
    for trans in transformations:
        operation = trans['operation']
        if 'FLIP HORIZONTAL' in operation:
            trans_types['flip_horizontal'] = trans_types.get('flip_horizontal', 0) + 1
        elif 'FLIP VERTICAL' in operation:
            trans_types['flip_vertical'] = trans_types.get('flip_vertical', 0) + 1
        elif 'ROTATE' in operation:
            trans_types['rotate'] = trans_types.get('rotate', 0) + 1
        elif 'COPY' in operation and 'PASTE' in operation:
            trans_types['copy'] = trans_types.get('copy', 0) + 1
    
    print(f"   • Types de transformations:")
    for trans_type, count in trans_types.items():
        print(f"     - {trans_type}: {count} occurrences")
    
    # Scores de qualité
    scores = [t['score'] for t in transformations]
    if scores:
        print(f"   • Scores de qualité:")
        print(f"     - Score moyen: {sum(scores)/len(scores):.2f}")
        print(f"     - Score maximum: {max(scores):.2f}")
        print(f"     - Score minimum: {min(scores):.2f}")
        print(f"     - Transformations haute qualité (>0.8): {len([s for s in scores if s > 0.8])}")
    
    # Analyse de l'analyse
    analysis = best_example['analysis']
    print(f"\n📊 MÉTRIQUES D'ANALYSE:")
    print(f"   • Couverture des régions vides: {analysis.get('coverage_ratio', 0):.1%}")
    print(f"   • Qualité moyenne des transformations: {analysis.get('avg_transformation_quality', 0):.1%}")
    
    # Comparaison avec les autres exemples
    print(f"\n🔄 COMPARAISON AVEC LES AUTRES EXEMPLES:")
    
    input_examples = task_results['train_inputs']
    output_examples = task_results['train_outputs']
    
    print(f"   • Inputs d'entraînement:")
    for i, example in enumerate(input_examples):
        conf = example['confidence']
        num_trans = len(example['proposed_transformations'])
        print(f"     - Input {i+1}: Confiance {conf:.2f}, {num_trans} transformations")
    
    print(f"   • Outputs d'entraînement:")
    for i, example in enumerate(output_examples):
        conf = example['confidence']
        num_trans = len(example['proposed_transformations'])
        print(f"     - Output {i+1}: Confiance {conf:.2f}, {num_trans} transformations")

def load_and_display_actual_grid():
    """Charge et affiche la grille réelle de la tâche 3631a71a"""
    try:
        task_path = Path("../arcdata/training/3631a71a.json")
        with open(task_path, 'r') as f:
            task_data = json.load(f)
        
        print(f"\n" + "="*60)
        print("GRILLES RÉELLES DE LA TÂCHE 3631a71a")
        print("="*60)
        
        for i, example in enumerate(task_data['train']):
            print(f"\n📥 EXEMPLE {i+1} - INPUT (30x30):")
            input_grid = np.array(example['input'])
            print(f"   Forme: {input_grid.shape}")
            print(f"   Valeurs uniques: {sorted(list(set(input_grid.flatten())))}")
            print(f"   Cellules vides (0): {np.sum(input_grid == 0)}")
            
            print(f"\n📤 EXEMPLE {i+1} - OUTPUT (30x30):")
            output_grid = np.array(example['output'])
            print(f"   Forme: {output_grid.shape}")
            print(f"   Valeurs uniques: {sorted(list(set(output_grid.flatten())))}")
            print(f"   Cellules vides (0): {np.sum(output_grid == 0)}")
            
            # Afficher un aperçu de la grille (coin supérieur gauche)
            print(f"\n   Aperçu input (10x10 coin sup-gauche):")
            for row in input_grid[:10]:
                print(f"   {' '.join(str(cell) for cell in row[:10])}")
            
            print(f"\n   Aperçu output (10x10 coin sup-gauche):")
            for row in output_grid[:10]:
                print(f"   {' '.join(str(cell) for cell in row[:10])}")
            
            if i >= 1:  # Limiter à 2 exemples pour la lisibilité
                break
                
    except Exception as e:
        print(f"❌ Erreur lors du chargement de la grille: {e}")

if __name__ == "__main__":
    display_mosaic_details()
    load_and_display_actual_grid()