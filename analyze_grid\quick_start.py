"""
Démarrage rapide pour l'analyse de grilles ARC
"""

from grid_analysis_pure import PureGridAnalyzer

def demo_analysis():
    """Démonstration rapide de l'analyseur"""
    
    print("DÉMONSTRATION - ANALYSEUR DE GRILLES ARC")
    print("=" * 45)
    
    # Exemple 1: Transformation par multiplication
    print("\n1. Transformation par multiplication")
    print("-" * 35)
    
    input_grid = [[1, 0], [0, 1]]
    output_grid = [
        [1, 0, 1, 0],
        [0, 1, 0, 1], 
        [1, 0, 1, 0],
        [0, 1, 0, 1]
    ]
    
    analyzer = PureGridAnalyzer("../arcdata/training")
    analysis = analyzer.analyze_transformation(input_grid, output_grid)
    
    print(f"Input: {analysis['input_shape']} → Output: {analysis['output_shape']}")
    print(f"Type: {analysis['transformation_type']['type']}")
    print(f"Ratio: {analysis['size_ratio']}")
    print(f"Opérations suggérées: {analysis['transformation_type']['operations']}")
    
    # Exemple 2: Transformation de remplissage
    print("\n2. Transformation de remplissage")
    print("-" * 35)
    
    input_grid = [[1, 0, 1], [0, 0, 0], [1, 0, 1]]
    output_grid = [[1, 2, 1], [2, 2, 2], [1, 2, 1]]
    
    analysis2 = analyzer.analyze_transformation(input_grid, output_grid)
    
    print(f"Type: {analysis2['transformation_type']['type']}")
    print(f"Couleurs ajoutées: {analysis2['colors_added']}")
    print(f"Changements: {analysis2['cell_analysis']['changes']} cellules")
    
    # Mapping AGI
    print("\n3. Suggestions de commandes AGI")
    print("-" * 35)
    
    # Créer un résumé fictif pour la démonstration
    computed_insights = analysis['computed_insights'].copy()
    computed_insights['consistent_transformations'] = True  # Ajouter la clé manquante
    
    puzzle_summary = {
        'dominant_transformation_type': analysis['transformation_type']['type'],
        'average_size_ratio': analysis['size_ratio'],
        'computed_insights': computed_insights,
        'common_patterns': analysis['special_patterns']
    }
    
    agi_mapping = analyzer.map_to_agi_commands(puzzle_summary)
    
    print(f"Commandes suggérées: {agi_mapping['possible_commands']}")
    print(f"Paramètres: {agi_mapping['command_parameters']}")
    print(f"Confiance: {agi_mapping['confidence_level']}")
    
    print("\n✓ Démonstration terminée")
    print("  Utilisez 'python run_grid_analysis.py' pour une analyse complète")

if __name__ == "__main__":
    demo_analysis()