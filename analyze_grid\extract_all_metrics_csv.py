#!/usr/bin/env python3
"""
Extracteur CSV complet de toutes les métriques ARCAnalyzer
Génère un fichier CSV avec tous les critères de base pour les 401 puzzles
"""

import json
import os
import csv
import numpy as np
from collections import Counter
from typing import Dict, List, Any
from analyze_grid.arc_analyzer import ARCAnalyzer

class ARCMetricsExtractor:
    """Extracteur complet des métriques ARC pour analyse CSV"""
    
    def __init__(self):
        self.analyzer = ARCAnalyzer()
        self.all_metrics: List[Dict[str, Any]] = []
    
    def extract_basic_grid_metrics(self, puzzle_data: Dict, puzzle_id: str) -> Dict:
        """Extrait les métriques de base des grilles"""
        
        metrics: Dict[str, Any] = {'puzzle_id': puzzle_id}
        
        # Analyser chaque exemple d'entraînement
        input_areas = []
        output_areas = []
        input_heights = []
        input_widths = []
        output_heights = []
        output_widths = []
        input_color_counts = []
        output_color_counts = []
        dimension_changes = []
        size_ratios = []
        
        for example in puzzle_data['train']:
            input_grid = np.array(example['input'])
            output_grid = np.array(example['output'])
            
            # Dimensions
            in_h, in_w = input_grid.shape
            out_h, out_w = output_grid.shape
            
            input_heights.append(in_h)
            input_widths.append(in_w)
            output_heights.append(out_h)
            output_widths.append(out_w)
            
            input_areas.append(in_h * in_w)
            output_areas.append(out_h * out_w)
            
            # Changements de dimensions
            dimension_changes.append((out_h - in_h, out_w - in_w))
            
            # Ratios de taille
            if in_h > 0 and in_w > 0:
                size_ratios.append((out_h / in_h, out_w / in_w))
            
            # Couleurs
            input_colors = set(input_grid.flatten())
            output_colors = set(output_grid.flatten())
            
            # Retirer le fond noir pour compter les couleurs actives
            input_active_colors = input_colors.copy()
            output_active_colors = output_colors.copy()
            if 0 in input_active_colors:
                input_active_colors.remove(0)
            if 0 in output_active_colors:
                output_active_colors.remove(0)
            
            input_color_counts.append(len(input_active_colors))
            output_color_counts.append(len(output_active_colors))
        
        # Métriques de dimensions
        metrics.update({
            'train_examples': len(puzzle_data['train']),
            'min_input_height': min(input_heights),
            'max_input_height': max(input_heights),
            'avg_input_height': np.mean(input_heights),
            'min_input_width': min(input_widths),
            'max_input_width': max(input_widths),
            'avg_input_width': np.mean(input_widths),
            'min_input_area': min(input_areas),
            'max_input_area': max(input_areas),
            'avg_input_area': np.mean(input_areas),
            'min_output_height': min(output_heights),
            'max_output_height': max(output_heights),
            'avg_output_height': np.mean(output_heights),
            'min_output_width': min(output_widths),
            'max_output_width': max(output_widths),
            'avg_output_width': np.mean(output_widths),
            'min_output_area': min(output_areas),
            'max_output_area': max(output_areas),
            'avg_output_area': np.mean(output_areas)
        })
        
        # Changements de dimensions
        height_changes = [dc[0] for dc in dimension_changes]
        width_changes = [dc[1] for dc in dimension_changes]
        
        metrics.update({
            'min_height_change': min(height_changes),
            'max_height_change': max(height_changes),
            'avg_height_change': np.mean(height_changes),
            'min_width_change': min(width_changes),
            'max_width_change': max(width_changes),
            'avg_width_change': np.mean(width_changes),
            'consistent_dimension_change': len(set(dimension_changes)) == 1
        })
        
        # Ratios de taille
        if size_ratios:
            height_ratios = [sr[0] for sr in size_ratios]
            width_ratios = [sr[1] for sr in size_ratios]
            
            metrics.update({
                'min_height_ratio': min(height_ratios),
                'max_height_ratio': max(height_ratios),
                'avg_height_ratio': np.mean(height_ratios),
                'min_width_ratio': min(width_ratios),
                'max_width_ratio': max(width_ratios),
                'avg_width_ratio': np.mean(width_ratios)
            })
        else:
            metrics.update({
                'min_height_ratio': 0, 'max_height_ratio': 0, 'avg_height_ratio': 0,
                'min_width_ratio': 0, 'max_width_ratio': 0, 'avg_width_ratio': 0
            })
        
        # Couleurs
        metrics.update({
            'min_input_colors': min(input_color_counts),
            'max_input_colors': max(input_color_counts),
            'avg_input_colors': np.mean(input_color_counts),
            'min_output_colors': min(output_color_counts),
            'max_output_colors': max(output_color_counts),
            'avg_output_colors': np.mean(output_color_counts)
        })
        
        return metrics
    
    def extract_color_usage_metrics(self, puzzle_data: Dict) -> Dict:
        """Extrait les métriques d'utilisation des couleurs"""
        
        usage_ratios = []
        balance_scores = []
        dominant_ratios = []
        
        for example in puzzle_data['train']:
            input_grid = np.array(example['input'])
            
            total_cells = input_grid.size
            color_counts = Counter(input_grid.flatten())
            
            # Retirer le fond noir
            if 0 in color_counts:
                background_cells = color_counts[0]
                del color_counts[0]
            else:
                background_cells = 0
            
            non_background_cells = total_cells - background_cells
            
            # Ratio d'utilisation
            usage_ratio = non_background_cells / total_cells if total_cells > 0 else 0
            usage_ratios.append(usage_ratio)
            
            # Score d'équilibre et dominance
            if len(color_counts) > 0 and non_background_cells > 0:
                color_ratios = [count / non_background_cells for count in color_counts.values()]
                
                # Score d'équilibre
                if len(color_ratios) > 1:
                    variance = np.var(color_ratios)
                    balance_score = 1.0 / (1.0 + variance * 10)
                else:
                    balance_score = 0.0
                
                balance_scores.append(balance_score)
                dominant_ratios.append(max(color_ratios))
            else:
                balance_scores.append(0.0)
                dominant_ratios.append(1.0)
        
        return {
            'min_color_usage_ratio': min(usage_ratios) if usage_ratios else 0,
            'max_color_usage_ratio': max(usage_ratios) if usage_ratios else 0,
            'avg_color_usage_ratio': np.mean(usage_ratios) if usage_ratios else 0,
            'min_color_balance_score': min(balance_scores) if balance_scores else 0,
            'max_color_balance_score': max(balance_scores) if balance_scores else 0,
            'avg_color_balance_score': np.mean(balance_scores) if balance_scores else 0,
            'min_dominant_color_ratio': min(dominant_ratios) if dominant_ratios else 0,
            'max_dominant_color_ratio': max(dominant_ratios) if dominant_ratios else 0,
            'avg_dominant_color_ratio': np.mean(dominant_ratios) if dominant_ratios else 0
        }
    
    def extract_arc_analyzer_metrics(self, puzzle_data: Dict) -> Dict:
        """Extrait les métriques de l'ARCAnalyzer"""
        
        try:
            analysis = self.analyzer.analyze_puzzle(puzzle_data)
            
            metrics = {}
            
            # Complexité
            complexity = analysis.get('complexity', {})
            metrics.update({
                'grid_complexity': complexity.get('grid_complexity', 0),
                'color_complexity': complexity.get('color_complexity', 0),
                'object_complexity': complexity.get('object_complexity', 0),
                'transformation_complexity': complexity.get('transformation_complexity', 0),
                'overall_complexity': complexity.get('overall_complexity', 0)
            })
            
            # Couleurs globales
            colors = analysis.get('colors', {})
            input_colors = colors.get('input_colors', [])
            output_colors = colors.get('output_colors', [])
            
            metrics.update({
                'total_input_colors': len(input_colors),
                'total_output_colors': len(output_colors),
                'color_change': len(output_colors) - len(input_colors)
            })
            
            # Symétries
            symmetries = analysis.get('symmetries', {})
            input_symmetries = symmetries.get('input_symmetries', [])
            
            # Compter les symétries
            horizontal_count = sum(1 for sym in input_symmetries if sym.get('horizontal', False))
            vertical_count = sum(1 for sym in input_symmetries if sym.get('vertical', False))
            diagonal_main_count = sum(1 for sym in input_symmetries if sym.get('diagonal_main', False))
            diagonal_anti_count = sum(1 for sym in input_symmetries if sym.get('diagonal_anti', False))
            
            metrics.update({
                'horizontal_symmetries': horizontal_count,
                'vertical_symmetries': vertical_count,
                'diagonal_main_symmetries': diagonal_main_count,
                'diagonal_anti_symmetries': diagonal_anti_count,
                'total_symmetries': horizontal_count + vertical_count + diagonal_main_count + diagonal_anti_count,
                'has_any_symmetry': (horizontal_count + vertical_count + diagonal_main_count + diagonal_anti_count) > 0
            })
            
            # Patterns
            patterns = analysis.get('patterns', {})
            
            # Répétition
            repetition = patterns.get('repetition', {})
            metrics.update({
                'repetition_detected': repetition.get('detected', False),
                'repetition_type': repetition.get('type', ''),
                'repetition_factor': repetition.get('factor', 0)
            })
            
            # Scaling
            scaling = patterns.get('scaling', {})
            metrics.update({
                'scaling_detected': scaling.get('detected', False),
                'scaling_consistent': scaling.get('consistent_factor', False)
            })
            
            # MOTIF
            motif = patterns.get('motif', {})
            metrics.update({
                'motif_detected': motif.get('detected', False),
                'motif_type': motif.get('type', ''),
                'motif_confidence': motif.get('confidence', 0.0)
            })
            
            # Transformations
            transformations = analysis.get('transformations', {})
            geometric = transformations.get('geometric', [])
            color_transforms = transformations.get('color', [])
            structural = transformations.get('structural', [])
            
            metrics.update({
                'geometric_transforms': len(geometric),
                'color_transforms': len(color_transforms),
                'structural_transforms': len(structural),
                'total_transforms': len(geometric) + len(color_transforms) + len(structural)
            })
            
            return metrics
            
        except Exception as e:
            print(f"Erreur ARCAnalyzer pour puzzle: {e}")
            # Retourner des valeurs par défaut
            return {
                'grid_complexity': 0, 'color_complexity': 0, 'object_complexity': 0,
                'transformation_complexity': 0, 'overall_complexity': 0,
                'total_input_colors': 0, 'total_output_colors': 0, 'color_change': 0,
                'horizontal_symmetries': 0, 'vertical_symmetries': 0,
                'diagonal_main_symmetries': 0, 'diagonal_anti_symmetries': 0,
                'total_symmetries': 0, 'has_any_symmetry': False,
                'repetition_detected': False, 'repetition_type': '',
                'repetition_factor': 0, 'scaling_detected': False,
                'scaling_consistent': False, 'motif_detected': False,
                'motif_type': '', 'motif_confidence': 0.0,
                'geometric_transforms': 0, 'color_transforms': 0,
                'structural_transforms': 0, 'total_transforms': 0
            }
    
    def calculate_mosaic_score(self, metrics: Dict) -> float:
        """Calcule le score MOSAIC basé sur les métriques"""
        
        score = 0.0
        
        # Taille (25%)
        if metrics['max_input_area'] >= 400:
            score += 0.25
        elif metrics['max_input_area'] >= 225:
            score += 0.20
        elif metrics['max_input_area'] >= 144:
            score += 0.15
        elif metrics['max_input_area'] >= 100:
            score += 0.10
        
        # Couleurs (20%)
        if metrics['max_input_colors'] >= 8:
            score += 0.20
        elif metrics['max_input_colors'] >= 7:
            score += 0.16
        elif metrics['max_input_colors'] >= 6:
            score += 0.12
        elif metrics['max_input_colors'] >= 5:
            score += 0.08
        
        # Usage des couleurs (25%)
        if metrics['avg_color_usage_ratio'] >= 0.8:
            score += 0.25
        elif metrics['avg_color_usage_ratio'] >= 0.6:
            score += 0.20
        elif metrics['avg_color_usage_ratio'] >= 0.4:
            score += 0.15
        elif metrics['avg_color_usage_ratio'] >= 0.2:
            score += 0.08
        
        # Équilibre des couleurs (20%)
        if metrics['avg_color_balance_score'] >= 0.8:
            score += 0.20
        elif metrics['avg_color_balance_score'] >= 0.6:
            score += 0.16
        elif metrics['avg_color_balance_score'] >= 0.4:
            score += 0.10
        
        # Non-dominance (10%)
        if metrics['avg_dominant_color_ratio'] <= 0.3:
            score += 0.10
        elif metrics['avg_dominant_color_ratio'] <= 0.5:
            score += 0.07
        elif metrics['avg_dominant_color_ratio'] <= 0.7:
            score += 0.04
        
        return min(score, 1.0)
    
    def extract_all_puzzle_metrics(self, puzzle_data: Dict, puzzle_id: str) -> Dict:
        """Extrait toutes les métriques pour un puzzle"""
        
        print(f"Traitement: {puzzle_id}")
        
        # Métriques de base des grilles
        basic_metrics = self.extract_basic_grid_metrics(puzzle_data, puzzle_id)
        
        # Métriques d'utilisation des couleurs
        color_usage_metrics = self.extract_color_usage_metrics(puzzle_data)
        
        # Métriques ARCAnalyzer
        arc_metrics = self.extract_arc_analyzer_metrics(puzzle_data)
        
        # Combiner toutes les métriques
        all_metrics = {**basic_metrics, **color_usage_metrics, **arc_metrics}
        
        # Calculer le score MOSAIC
        all_metrics['mosaic_score'] = self.calculate_mosaic_score(all_metrics)
        all_metrics['is_mosaic_candidate'] = all_metrics['mosaic_score'] >= 0.7
        
        return all_metrics
    
    def process_all_puzzles(self, data_dir: str = "../arcdata/training") -> List[Dict]:
        """Traite tous les puzzles et extrait leurs métriques"""
        
        print("🔍 EXTRACTION COMPLÈTE DES MÉTRIQUES ARC")
        print("=" * 50)
        
        all_metrics = []
        processed_count = 0
        error_count = 0
        
        for filename in os.listdir(data_dir):
            if not filename.endswith('.json'):
                continue
                
            puzzle_id = filename.replace('.json', '')
            filepath = os.path.join(data_dir, filename)
            
            try:
                with open(filepath, 'r') as f:
                    puzzle_data = json.load(f)
                
                metrics = self.extract_all_puzzle_metrics(puzzle_data, puzzle_id)
                all_metrics.append(metrics)
                processed_count += 1
                
                if processed_count % 50 == 0:
                    print(f"Traité: {processed_count} puzzles")
                
            except Exception as e:
                print(f"❌ Erreur avec {puzzle_id}: {e}")
                error_count += 1
                continue
        
        print(f"\n📊 EXTRACTION TERMINÉE")
        print(f"Puzzles traités: {processed_count}")
        print(f"Erreurs: {error_count}")
        print(f"Total métriques par puzzle: {len(all_metrics[0]) if all_metrics else 0}")
        
        return all_metrics
    
    def save_to_csv(self, all_metrics: List[Dict], filename: str = "arc_complete_metrics.csv"):
        """Sauvegarde toutes les métriques en CSV"""
        
        if not all_metrics:
            print("Aucune métrique à sauvegarder")
            return
        
        # Obtenir toutes les colonnes
        fieldnames = list(all_metrics[0].keys())
        
        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(all_metrics)
        
        print(f"💾 CSV sauvegardé: {filename}")
        print(f"Lignes: {len(all_metrics)}")
        print(f"Colonnes: {len(fieldnames)}")
        
        # Afficher un aperçu des colonnes
        print(f"\n📋 COLONNES DISPONIBLES:")
        for i, col in enumerate(fieldnames, 1):
            print(f"{i:2d}. {col}")

def main():
    """Point d'entrée principal"""
    
    extractor = ARCMetricsExtractor()
    
    # Extraire toutes les métriques
    all_metrics = extractor.process_all_puzzles()
    
    # Sauvegarder en CSV
    extractor.save_to_csv(all_metrics)
    
    print(f"\n🎯 FICHIER CSV PRÊT POUR ANALYSE")
    print("Vous pouvez maintenant utiliser ce fichier pour:")
    print("- Analyses statistiques approfondies")
    print("- Clustering des puzzles")
    print("- Identification de nouveaux patterns")
    print("- Corrélations entre métriques")

if __name__ == "__main__":
    main()