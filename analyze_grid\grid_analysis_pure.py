"""
Analyseur de grilles ARC pur - Sans DINO
Analyse directe des transformations de grilles avec algorithmes classiques
"""

import json
import numpy as np
import pandas as pd
from pathlib import Path
from collections import Counter, defaultdict
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import warnings
warnings.filterwarnings("ignore")

class PureGridAnalyzer:
    """Analyseur de grilles ARC basé uniquement sur des algorithmes classiques"""
    
    def __init__(self, data_dir="../arcdata/training"):
        self.data_dir = Path(data_dir)
        
        # Structures pour collecter les données
        self.all_results = []
        self.statistics = defaultdict(list)
        
        print(f"🔍 Analyseur de grilles initialisé pour: {self.data_dir}")
    
    def analyze_transformation(self, input_grid, output_grid):
        """Analyse complète d'une transformation input → output avec algorithmes classiques"""
        
        # Calculs de base
        input_h, input_w = len(input_grid), len(input_grid[0])
        output_h, output_w = len(output_grid), len(output_grid[0])
        
        size_ratio = (output_h * output_w) / (input_h * input_w)
        height_ratio = output_h / input_h
        width_ratio = output_w / input_w
        
        # Analyse des couleurs
        input_colors = set(cell for row in input_grid for cell in row)
        output_colors = set(cell for row in output_grid for cell in row)
        
        color_added = output_colors - input_colors
        color_removed = input_colors - output_colors
        color_preserved = input_colors & output_colors
        
        # Analyse de complexité basée sur des métriques calculées
        input_complexity = self.calculate_grid_complexity(input_grid)
        output_complexity = self.calculate_grid_complexity(output_grid)
        complexity_change = output_complexity - input_complexity
        
        # Classification du type de transformation
        transformation_type = self.classify_transformation((input_h, input_w), (output_h, output_w))
        
        # Détection des patterns spéciaux
        special_patterns = self.detect_special_patterns(input_grid, output_grid, size_ratio)
        
        # Analyse spatiale
        spatial_analysis = self.analyze_spatial_patterns(input_grid, output_grid)
        
        # Analyse des changements cellule par cellule (si même taille)
        cell_analysis = self.analyze_cell_changes(input_grid, output_grid)
        
        return {
            # Métriques de base
            'input_shape': (input_h, input_w),
            'output_shape': (output_h, output_w),
            'size_ratio': float(size_ratio),
            'height_ratio': float(height_ratio),
            'width_ratio': float(width_ratio),
            
            # Analyse des couleurs
            'input_colors': sorted(list(input_colors)),
            'output_colors': sorted(list(output_colors)),
            'colors_added': sorted(list(color_added)),
            'colors_removed': sorted(list(color_removed)),
            'colors_preserved': sorted(list(color_preserved)),
            'color_count_change': len(output_colors) - len(input_colors),
            
            # Analyse de complexité (calculs algorithmiques)
            'input_complexity': input_complexity,
            'output_complexity': output_complexity,
            'complexity_change': complexity_change,
            
            # Classification
            'transformation_type': transformation_type,
            'special_patterns': special_patterns,
            'spatial_analysis': spatial_analysis,
            'cell_analysis': cell_analysis,
            
            # Insights calculés (algorithmes Python classiques)
            'computed_insights': self.compute_transformation_insights(
                size_ratio, complexity_change, special_patterns, cell_analysis
            )
        }
    
    def calculate_grid_complexity(self, grid):
        """Calcule la complexité d'une grille avec des métriques algorithmiques"""
        h, w = len(grid), len(grid[0])
        
        # Nombre de couleurs uniques
        unique_colors = len(set(cell for row in grid for cell in row))
        
        # Nombre de transitions (changements de couleur adjacents)
        transitions = 0
        for i in range(h):
            for j in range(w):
                # Transitions horizontales
                if j < w - 1 and grid[i][j] != grid[i][j + 1]:
                    transitions += 1
                # Transitions verticales
                if i < h - 1 and grid[i][j] != grid[i + 1][j]:
                    transitions += 1
        
        # Nombre de régions connectées (approximation simple)
        regions = self.count_connected_regions(grid)
        
        # Score de complexité combiné
        complexity = unique_colors * 0.3 + transitions * 0.5 + regions * 0.2
        
        return complexity
    
    def count_connected_regions(self, grid):
        """Compte le nombre de régions connectées de même couleur"""
        h, w = len(grid), len(grid[0])
        visited = [[False] * w for _ in range(h)]
        regions = 0
        
        def dfs(i, j, color):
            if (i < 0 or i >= h or j < 0 or j >= w or 
                visited[i][j] or grid[i][j] != color):
                return
            
            visited[i][j] = True
            # Visiter les 4 directions
            for di, dj in [(0, 1), (0, -1), (1, 0), (-1, 0)]:
                dfs(i + di, j + dj, color)
        
        for i in range(h):
            for j in range(w):
                if not visited[i][j]:
                    dfs(i, j, grid[i][j])
                    regions += 1
        
        return regions
    
    def classify_transformation(self, input_shape, output_shape):
        """Classifie le type de transformation avec calculs arithmétiques"""
        
        input_h, input_w = input_shape
        output_h, output_w = output_shape
        
        # Changements en lignes et colonnes
        height_change = output_h - input_h
        width_change = output_w - input_w
        
        # Facteurs multiplicateurs
        height_factor = output_h / input_h if input_h > 0 else 1
        width_factor = output_w / input_w if input_w > 0 else 1
        
        # Détection des multiplications/divisions exactes
        is_height_multiply = (output_h % input_h == 0) and (output_h > input_h)
        is_width_multiply = (output_w % input_w == 0) and (output_w > input_w)
        is_height_divide = (input_h % output_h == 0) and (input_h > output_h)
        is_width_divide = (input_w % output_w == 0) and (input_w > output_w)
        
        result = {
            'type': 'unknown',
            'height_change': height_change,
            'width_change': width_change,
            'height_factor': height_factor,
            'width_factor': width_factor,
            'operations': []
        }
        
        # Classification par calculs arithmétiques
        if height_change == 0 and width_change == 0:
            result['type'] = "same_size"
            
        elif is_height_multiply and is_width_multiply and height_factor == width_factor:
            result['type'] = "multiply_uniform"
            result['operations'] = [f"MULTIPLY {int(height_factor)}"]
            
        elif is_height_multiply and is_width_multiply:
            result['type'] = "multiply_asymmetric"
            result['operations'] = [f"MULTIPLY_ROWS {int(height_factor)}", f"MULTIPLY_COLS {int(width_factor)}"]
            
        elif is_height_divide and is_width_divide and (input_h // output_h) == (input_w // output_w):
            divide_factor = input_h // output_h
            result['type'] = "divide_uniform"
            result['operations'] = [f"DIVIDE {divide_factor}"]
            
        elif abs(height_change) <= 3 and abs(width_change) <= 3:
            if height_change != 0 and width_change != 0:
                result['type'] = "row_and_column_modification"
            elif height_change != 0:
                result['type'] = "row_modification"
            elif width_change != 0:
                result['type'] = "column_modification"
        
        else:
            if height_change != 0 and width_change != 0:
                result['type'] = "complex_resize"
            elif height_change != 0:
                result['type'] = "row_resize"
            elif width_change != 0:
                result['type'] = "column_resize"
        
        return result
    
    def detect_special_patterns(self, input_grid, output_grid, size_ratio):
        """Détecte des patterns spéciaux avec algorithmes de comparaison"""
        patterns = []
        
        # Pattern de réplication (calcul de ratio)
        if size_ratio > 4:
            patterns.append("pattern_replication")
        
        # Pattern d'expansion systématique
        if size_ratio > 2:
            patterns.append("systematic_expansion")
        
        # Pattern de remplissage (comptage de cellules)
        input_zeros = sum(row.count(0) for row in input_grid)
        output_zeros = sum(row.count(0) for row in output_grid)
        
        if input_zeros > output_zeros:
            patterns.append("fill_pattern")
        elif input_zeros < output_zeros:
            patterns.append("clear_pattern")
        
        # Pattern de bordure (comparaison algorithmique)
        if self.has_border_pattern(input_grid, output_grid):
            patterns.append("border_pattern")
        
        # Pattern de symétrie
        if self.has_symmetry_pattern(input_grid, output_grid):
            patterns.append("symmetry_pattern")
        
        return patterns
    
    def has_border_pattern(self, input_grid, output_grid):
        """Détecte les patterns de bordure par comparaison directe"""
        if len(input_grid) != len(output_grid) or len(input_grid[0]) != len(output_grid[0]):
            return False
        
        h, w = len(input_grid), len(input_grid[0])
        
        # Comparer les bordures
        for i in range(h):
            if input_grid[i][0] != output_grid[i][0] or input_grid[i][w-1] != output_grid[i][w-1]:
                return True
        
        for j in range(w):
            if input_grid[0][j] != output_grid[0][j] or input_grid[h-1][j] != output_grid[h-1][j]:
                return True
        
        return False
    
    def has_symmetry_pattern(self, input_grid, output_grid):
        """Détecte les patterns de symétrie par calcul direct"""
        # Vérifier si la grille de sortie a des symétries
        if len(output_grid) != len(output_grid[0]):  # Pas carrée
            return False
        
        h = len(output_grid)
        
        # Symétrie horizontale
        horizontal_sym = all(
            output_grid[i][j] == output_grid[h-1-i][j] 
            for i in range(h//2) for j in range(h)
        )
        
        # Symétrie verticale
        vertical_sym = all(
            output_grid[i][j] == output_grid[i][h-1-j] 
            for i in range(h) for j in range(h//2)
        )
        
        return horizontal_sym or vertical_sym
    
    def analyze_spatial_patterns(self, input_grid, output_grid):
        """Analyse les patterns spatiaux avec calculs géométriques"""
        analysis = {
            'has_symmetry': False,
            'has_repetition': False,
            'has_rotation': False,
            'has_reflection': False,
            'pattern_density': 0.0
        }
        
        # Densité de patterns (ratio cellules non-zéro)
        total_cells = len(output_grid) * len(output_grid[0])
        non_zero_cells = sum(1 for row in output_grid for cell in row if cell != 0)
        analysis['pattern_density'] = non_zero_cells / total_cells
        
        # Détection de répétition (algorithme de comparaison)
        unique_rows = len(set(tuple(row) for row in output_grid))
        if unique_rows < len(output_grid) / 2:
            analysis['has_repetition'] = True
        
        # Symétrie (déjà calculée)
        analysis['has_symmetry'] = self.has_symmetry_pattern(input_grid, output_grid)
        
        return analysis
    
    def analyze_cell_changes(self, input_grid, output_grid):
        """Analyse les changements cellule par cellule"""
        if (len(input_grid) != len(output_grid) or 
            len(input_grid[0]) != len(output_grid[0])):
            return {'same_size': False, 'changes': 0, 'change_ratio': 0.0}
        
        h, w = len(input_grid), len(input_grid[0])
        changes = 0
        
        for i in range(h):
            for j in range(w):
                if input_grid[i][j] != output_grid[i][j]:
                    changes += 1
        
        total_cells = h * w
        change_ratio = changes / total_cells
        
        return {
            'same_size': True,
            'changes': changes,
            'change_ratio': change_ratio,
            'unchanged_cells': total_cells - changes
        }
    
    def compute_transformation_insights(self, size_ratio, complexity_change, special_patterns, cell_analysis):
        """Calcule des insights de transformation avec algorithmes classiques"""
        return {
            'is_expansion': size_ratio > 2,  # Comparaison arithmétique
            'is_contraction': size_ratio < 0.5,  # Comparaison arithmétique
            'has_pattern_replication': size_ratio > 4,  # Calcul de ratio
            'has_complexity_increase': complexity_change > 0,  # Comparaison numérique
            'has_fill_operation': 'fill_pattern' in special_patterns,  # Recherche dans liste
            'has_border_operation': 'border_pattern' in special_patterns,  # Recherche dans liste
            'has_systematic_transformation': len(special_patterns) >= 2,  # Comptage de liste
            'is_same_size_transformation': cell_analysis.get('same_size', False),  # Booléen direct
            'has_major_changes': cell_analysis.get('change_ratio', 0) > 0.5,  # Comparaison de ratio
            'preserves_structure': cell_analysis.get('change_ratio', 1) < 0.1  # Comparaison de ratio
        }   
 
    def analyze_puzzle(self, puzzle_path):
        """Analyse complète d'un puzzle"""
        try:
            with open(puzzle_path, 'r') as f:
                puzzle_data = json.load(f)
        except Exception as e:
            print(f"❌ Erreur lecture {puzzle_path}: {e}")
            return None
        
        puzzle_id = puzzle_path.stem
        
        # Analyser tous les exemples d'entraînement
        train_analyses = []
        for i, example in enumerate(puzzle_data.get('train', [])):
            analysis = self.analyze_transformation(example['input'], example['output'])
            analysis['example_id'] = i
            train_analyses.append(analysis)
        
        # Analyser le test input (sans output)
        test_analysis = None
        if puzzle_data.get('test'):
            test_input = puzzle_data['test'][0]['input']
            test_analysis = {
                'input_shape': (len(test_input), len(test_input[0])),
                'input_colors': sorted(list(set(cell for row in test_input for cell in row))),
                'input_zeros': sum(row.count(0) for row in test_input),
                'input_complexity': self.calculate_grid_complexity(test_input)
            }
        
        # Synthèse du puzzle
        puzzle_summary = self.generate_puzzle_summary(train_analyses)
        
        return {
            'puzzle_id': puzzle_id,
            'train_examples': train_analyses,
            'test_analysis': test_analysis,
            'puzzle_summary': puzzle_summary,
            'n_train_examples': len(train_analyses)
        }
    
    def generate_puzzle_summary(self, train_analyses):
        """Génère un résumé du puzzle basé sur tous les exemples"""
        if not train_analyses:
            return {}
        
        # Collecter les statistiques
        transformation_types = [a['transformation_type']['type'] for a in train_analyses]
        size_ratios = [a['size_ratio'] for a in train_analyses]
        complexity_changes = [a['complexity_change'] for a in train_analyses]
        
        # Patterns collectés
        all_patterns = []
        for analysis in train_analyses:
            all_patterns.extend(analysis['special_patterns'])
        
        # Insights calculés agrégés
        computed_insights = {
            'detects_expansion': any(a['computed_insights']['is_expansion'] for a in train_analyses),
            'detects_contraction': any(a['computed_insights']['is_contraction'] for a in train_analyses),
            'detects_pattern_replication': any(a['computed_insights']['has_pattern_replication'] for a in train_analyses),
            'detects_complexity_increase': any(a['computed_insights']['has_complexity_increase'] for a in train_analyses),
            'detects_fill_operation': any(a['computed_insights']['has_fill_operation'] for a in train_analyses),
            'detects_border_operation': any(a['computed_insights']['has_border_operation'] for a in train_analyses),
            'consistent_transformations': len(set(transformation_types)) <= 1,
            'preserves_structure': any(a['computed_insights']['preserves_structure'] for a in train_analyses)
        }
        
        return {
            'dominant_transformation_type': max(set(transformation_types), key=transformation_types.count),
            'average_size_ratio': float(np.mean(size_ratios)),
            'average_complexity_change': float(np.mean(complexity_changes)),
            'common_patterns': [p for p, c in Counter(all_patterns).most_common(3)],
            'computed_insights': computed_insights,
            'consistency_score': 1.0 - (len(set(transformation_types)) - 1) / len(transformation_types) if transformation_types else 0
        }
    
    def map_to_agi_commands(self, puzzle_summary):
        """Mappe les insights calculés vers les commandes AGI possibles"""
        agi_mapping = {
            'possible_commands': [],
            'command_parameters': {},
            'confidence_level': 'medium'
        }
        
        transformation_type = puzzle_summary['dominant_transformation_type']
        computed_insights = puzzle_summary['computed_insights']
        avg_size_ratio = puzzle_summary['average_size_ratio']
        
        # Mapping basé sur le type de transformation
        if transformation_type == "multiply_uniform":
            factor = int(round(avg_size_ratio ** 0.5))
            agi_mapping['possible_commands'].extend(['MULTIPLY', 'RESIZE'])
            agi_mapping['command_parameters']['MULTIPLY'] = f"{factor}"
        
        elif transformation_type == "same_size":
            if computed_insights['detects_fill_operation']:
                agi_mapping['possible_commands'].extend(['FILL', 'EDIT'])
                agi_mapping['command_parameters']['FILL'] = "1"
            
            if computed_insights['detects_border_operation']:
                agi_mapping['possible_commands'].append('SURROUND')
                agi_mapping['command_parameters']['SURROUND'] = "1"
        
        elif transformation_type == "divide_uniform":
            factor = int(round(1 / avg_size_ratio ** 0.5))
            agi_mapping['possible_commands'].extend(['DIVIDE', 'RESIZE'])
            agi_mapping['command_parameters']['DIVIDE'] = f"{factor}"
        
        # Patterns spéciaux
        common_patterns = puzzle_summary.get('common_patterns', [])
        if 'pattern_replication' in common_patterns:
            agi_mapping['possible_commands'].extend(['COPY', 'PASTE', 'MOTIF'])
        
        if 'fill_pattern' in common_patterns:
            agi_mapping['possible_commands'].append('FLOODFILL')
        
        # Niveau de confiance basé sur la consistance
        if computed_insights['consistent_transformations']:
            agi_mapping['confidence_level'] = 'high'
        elif len(agi_mapping['possible_commands']) >= 3:
            agi_mapping['confidence_level'] = 'low'
        
        return agi_mapping
    
    def analyze_all_puzzles(self):
        """Analyse tous les puzzles du répertoire"""
        json_files = self._get_original_arc_puzzles()
        return self._analyze_puzzles(json_files)
    
    def analyze_limited_puzzles(self, max_puzzles):
        """Analyse un nombre limité de puzzles"""
        json_files = self._get_original_arc_puzzles()[:max_puzzles]
        return self._analyze_puzzles(json_files)
    
    def _get_original_arc_puzzles(self):
        """Filtre pour ne récupérer que les puzzles ARC originaux"""
        all_json_files = list(self.data_dir.glob("*.json"))
        
        original_puzzles = []
        for json_file in all_json_files:
            filename = json_file.name
            
            # Exclure les fichiers générés par nos analyses
            if any(suffix in filename for suffix in [
                '_analysis.json',
                '_agi_mapping.json', 
                '_enriched.json',
                '_optimized.json',
                'global_vocabulary.json'
            ]):
                continue
            
            # Vérifier que c'est un puzzle ARC valide (8 caractères hex + .json)
            puzzle_id = filename.replace('.json', '')
            if len(puzzle_id) == 8 and all(c in '0123456789abcdef' for c in puzzle_id):
                original_puzzles.append(json_file)
        
        print(f"🔍 Puzzles ARC originaux trouvés: {len(original_puzzles)}")
        return original_puzzles
    
    def _analyze_puzzles(self, json_files):
        """Méthode interne pour analyser une liste de puzzles"""
        print(f"🔍 Analyse de {len(json_files)} puzzles...")
        
        results = []
        failed_count = 0
        
        for i, puzzle_path in enumerate(json_files):
            if i % 10 == 0 and len(json_files) > 10:
                print(f"   Progression: {i}/{len(json_files)} ({i/len(json_files)*100:.1f}%)")
            
            try:
                analysis = self.analyze_puzzle(puzzle_path)
                if analysis:
                    # Ajouter le mapping AGI
                    analysis['agi_mapping'] = self.map_to_agi_commands(analysis['puzzle_summary'])
                    results.append(analysis)
                    
                    # Collecter les statistiques
                    self.collect_statistics(analysis)
                else:
                    failed_count += 1
            except Exception as e:
                print(f"❌ Erreur puzzle {puzzle_path.stem}: {e}")
                failed_count += 1
        
        print(f"✅ Analyse terminée: {len(results)} réussies, {failed_count} échecs")
        self.all_results = results
        return results
    
    def collect_statistics(self, analysis):
        """Collecte les statistiques pour l'analyse globale"""
        summary = analysis['puzzle_summary']
        
        self.statistics['transformation_types'].append(summary['dominant_transformation_type'])
        self.statistics['size_ratios'].append(summary['average_size_ratio'])
        self.statistics['complexity_changes'].append(summary['average_complexity_change'])
        
        # Collecter les patterns
        for pattern in summary['common_patterns']:
            self.statistics['patterns'].append(pattern)
        
        # Collecter les insights calculés
        for insight, value in summary['computed_insights'].items():
            if value:
                self.statistics['computed_insights'].append(insight)
        
        # Collecter les commandes AGI possibles
        for command in analysis['agi_mapping']['possible_commands']:
            self.statistics['agi_commands'].append(command)
    
    def generate_comprehensive_report(self, output_file="grid_analysis_report.md"):
        """Génère un rapport complet de l'analyse"""
        
        if not self.all_results:
            print("❌ Aucun résultat à reporter")
            return
        
        report_lines = [
            "# 📊 RAPPORT D'ANALYSE DE GRILLES ARC - ALGORITHMES CLASSIQUES",
            f"*Généré le {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*",
            "",
            f"## 🎯 RÉSUMÉ EXÉCUTIF",
            f"- **Puzzles analysés** : {len(self.all_results)}",
            f"- **Méthode** : Algorithmes classiques de traitement de grilles",
            f"- **Analyse** : Transformations géométriques, patterns spatiaux, complexité calculée",
            "",
        ]
        
        # Statistiques globales
        report_lines.extend([
            "## 📈 STATISTIQUES GLOBALES",
            "",
            "### Types de Transformation",
            "| Type | Nombre | Pourcentage |",
            "|------|--------|-------------|"
        ])
        
        transform_counts = Counter(self.statistics['transformation_types'])
        total_transforms = len(self.statistics['transformation_types'])
        
        for transform_type, count in transform_counts.most_common():
            percentage = (count / total_transforms) * 100
            report_lines.append(f"| {transform_type} | {count} | {percentage:.1f}% |")
        
        report_lines.extend([
            "",
            "### Insights Calculés les Plus Fréquents",
            "| Insight | Occurrences |",
            "|---------|-------------|"
        ])
        
        insight_counts = Counter(self.statistics['computed_insights'])
        for insight, count in insight_counts.most_common(10):
            report_lines.append(f"| {insight} | {count} |")
        
        report_lines.extend([
            "",
            "### Commandes AGI Suggérées",
            "| Commande | Fréquence |",
            "|----------|-----------|"
        ])
        
        command_counts = Counter(self.statistics['agi_commands'])
        for command, count in command_counts.most_common(15):
            report_lines.append(f"| {command} | {count} |")
        
        # Sauvegarder le rapport
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(report_lines))
        
        print(f"📄 Rapport complet sauvegardé: {output_file}")
        return output_file
    
    def generate_csv_export(self, output_file="grid_analysis_data.csv"):
        """Génère un export CSV pour analyse quantitative"""
        
        csv_data = []
        
        for result in self.all_results:
            summary = result['puzzle_summary']
            agi = result['agi_mapping']
            
            row = {
                'puzzle_id': result['puzzle_id'],
                'n_train_examples': result['n_train_examples'],
                'transformation_type': summary['dominant_transformation_type'],
                'size_ratio': summary['average_size_ratio'],
                'complexity_change': summary['average_complexity_change'],
                'consistency_score': summary['consistency_score'],
                
                # Insights calculés (colonnes booléennes)
                'detects_expansion': summary['computed_insights']['detects_expansion'],
                'detects_contraction': summary['computed_insights']['detects_contraction'],
                'detects_pattern_replication': summary['computed_insights']['detects_pattern_replication'],
                'detects_complexity_increase': summary['computed_insights']['detects_complexity_increase'],
                'detects_fill_operation': summary['computed_insights']['detects_fill_operation'],
                'detects_border_operation': summary['computed_insights']['detects_border_operation'],
                'consistent_transformations': summary['computed_insights']['consistent_transformations'],
                'preserves_structure': summary['computed_insights']['preserves_structure'],
                
                # Commandes AGI
                'agi_commands': '|'.join(agi['possible_commands']),
                'agi_confidence': agi['confidence_level'],
                'n_agi_commands': len(agi['possible_commands'])
            }
            
            csv_data.append(row)
        
        # Créer le DataFrame et sauvegarder
        df = pd.DataFrame(csv_data)
        df.to_csv(output_file, index=False)
        
        print(f"📊 Données CSV exportées: {output_file}")
        return output_file
    
    def generate_visualization_dashboard(self):
        """Génère des visualisations pour le dashboard"""
        
        if not self.all_results:
            print("Aucune donnée à visualiser")
            return
        
        # Créer une figure avec plusieurs sous-graphiques
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('Analyse des Transformations de Grilles ARC', fontsize=16)
        
        # 1. Distribution des types de transformation
        transform_counts = Counter(self.statistics['transformation_types'])
        axes[0, 0].pie(transform_counts.values(), labels=transform_counts.keys(), autopct='%1.1f%%')
        axes[0, 0].set_title('Types de Transformation')
        
        # 2. Distribution des ratios de taille
        axes[0, 1].hist(self.statistics['size_ratios'], bins=20, alpha=0.7, edgecolor='black')
        axes[0, 1].set_title('Distribution des Ratios de Taille')
        axes[0, 1].set_xlabel('Ratio de Taille')
        axes[0, 1].set_ylabel('Fréquence')
        
        # 3. Changements de complexité
        axes[0, 2].hist(self.statistics['complexity_changes'], bins=15, alpha=0.7, edgecolor='black')
        axes[0, 2].set_title('Changements de Complexité')
        axes[0, 2].set_xlabel('Δ Complexité')
        axes[0, 2].set_ylabel('Fréquence')
        
        # 4. Insights calculés les plus fréquents
        insight_counts = Counter(self.statistics['computed_insights'])
        top_insights = dict(insight_counts.most_common(8))
        
        if top_insights:
            axes[1, 0].barh(list(top_insights.keys()), list(top_insights.values()))
            axes[1, 0].set_title('Insights Calculés les Plus Fréquents')
            axes[1, 0].set_xlabel('Occurrences')
        else:
            axes[1, 0].text(0.5, 0.5, 'Pas d\'insights', ha='center', va='center')
            axes[1, 0].set_title('Insights Calculés')
        
        # 5. Commandes AGI suggérées
        command_counts = Counter(self.statistics['agi_commands'])
        top_commands = dict(command_counts.most_common(10))
        
        if top_commands:
            axes[1, 1].bar(list(top_commands.keys()), list(top_commands.values()))
            axes[1, 1].set_title('Commandes AGI Suggérées')
            axes[1, 1].set_xlabel('Commande')
            axes[1, 1].set_ylabel('Fréquence')
            axes[1, 1].tick_params(axis='x', rotation=45)
        else:
            axes[1, 1].text(0.5, 0.5, 'Pas de commandes', ha='center', va='center')
            axes[1, 1].set_title('Commandes AGI')
        
        # 6. Corrélation taille vs complexité
        size_ratios = [r['puzzle_summary']['average_size_ratio'] for r in self.all_results]
        complexity_changes = [r['puzzle_summary']['average_complexity_change'] for r in self.all_results]
        
        axes[1, 2].scatter(size_ratios, complexity_changes, alpha=0.6)
        axes[1, 2].set_title('Ratio Taille vs Changement Complexité')
        axes[1, 2].set_xlabel('Ratio de Taille')
        axes[1, 2].set_ylabel('Δ Complexité')
        
        plt.tight_layout()
        plt.savefig('grid_analysis_dashboard.png', dpi=150, bbox_inches='tight')
        plt.close()
        
        print("Dashboard généré: grid_analysis_dashboard.png")

def main():
    """Fonction principale pour lancer l'analyse complète"""
    
    print("🚀 ANALYSE DE GRILLES ARC - ALGORITHMES CLASSIQUES")
    print("=" * 60)
    
    # Initialiser l'analyseur
    analyzer = PureGridAnalyzer("../arcdata/training")
    
    # Analyser un échantillon de puzzles pour test
    results = analyzer.analyze_limited_puzzles(50)
    
    if not results:
        print("❌ Aucun puzzle analysé avec succès")
        return
    
    # Générer le rapport complet
    report_file = analyzer.generate_comprehensive_report()
    
    # Générer l'export CSV
    csv_file = analyzer.generate_csv_export()
    
    # Résumé final
    print(f"\n✅ ANALYSE TERMINÉE")
    print(f"📄 Rapport: {report_file}")
    print(f"📊 Données CSV: {csv_file}")
    print(f"\n🎯 RÉSULTATS CLÉS:")
    print(f"   - {len(results)} puzzles analysés")
    print(f"   - {len(set(analyzer.statistics['transformation_types']))} types de transformation détectés")
    print(f"   - {len(set(analyzer.statistics['agi_commands']))} commandes AGI différentes suggérées")
    print(f"   - {len(set(analyzer.statistics['computed_insights']))} insights calculés uniques")

if __name__ == "__main__":
    main()