"""
Test de l'analyse de tâches spécifiques
"""

import subprocess
import sys
from pathlib import Path

def test_task_analysis(task_id):
    """Teste l'analyse d'une tâche spécifique"""
    
    print(f"Test de la tâche: {task_id}")
    print("-" * 30)
    
    try:
        # Lancer l'analyse de la tâche
        result = subprocess.run([
            sys.executable, "run_grid_analysis.py", 
            "--taskId", task_id, "--quiet", "--no-viz"
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print(f"✓ Analyse réussie pour {task_id}")
            
            # Vérifier que le fichier de résultat existe
            output_file = f"{task_id}_analysis.json"
            if Path(output_file).exists():
                print(f"✓ Fichier généré: {output_file}")
                
                # Lire et afficher un résumé
                import json
                with open(output_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                summary = data['puzzle_summary']
                print(f"  Type: {summary['dominant_transformation_type']}")
                print(f"  Ratio: {summary['average_size_ratio']:.2f}")
                print(f"  Commandes AGI: {len(data['agi_mapping']['possible_commands'])}")
                
                return True
            else:
                print(f"✗ Fichier manquant: {output_file}")
                return False
        else:
            print(f"✗ Échec de l'analyse pour {task_id}")
            print(f"Erreur: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"✗ Timeout pour {task_id}")
        return False
    except Exception as e:
        print(f"✗ Erreur pour {task_id}: {e}")
        return False

def main():
    """Teste plusieurs tâches spécifiques"""
    
    print("TEST D'ANALYSES DE TÂCHES SPÉCIFIQUES")
    print("=" * 45)
    
    # Liste de tâches à tester
    test_tasks = [
        "007bbfb7",  # Multiplication uniforme
        "00d62c1b",  # Même taille avec remplissage
        "025d127b",  # Autre type de transformation
    ]
    
    results = []
    
    for task_id in test_tasks:
        success = test_task_analysis(task_id)
        results.append((task_id, success))
        print()
    
    # Résumé
    print("RÉSUMÉ DES TESTS")
    print("-" * 20)
    
    successful = sum(1 for _, success in results if success)
    total = len(results)
    
    for task_id, success in results:
        status = "✓" if success else "✗"
        print(f"{status} {task_id}")
    
    print(f"\nRésultat: {successful}/{total} tâches analysées avec succès")
    
    if successful == total:
        print("✓ Tous les tests sont passés !")
    else:
        print("⚠ Certains tests ont échoué")

if __name__ == "__main__":
    main()