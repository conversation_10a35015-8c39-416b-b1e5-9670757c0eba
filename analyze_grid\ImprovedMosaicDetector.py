#!/usr/bin/env python3
"""
Détecteur de mosaïque amélioré basé sur l'analyse des différences input/output
Se concentre sur les indices concrets : perte de couleurs, remplissage de zones, etc.
"""

import numpy as np
from collections import Counter
from typing import Dict, List, Tuple, Optional
import json

class ImprovedMosaicDetector:
    """
    Détecteur de mosaïque basé sur l'analyse des transformations réelles input→output
    """
    
    def __init__(self, empty_value=0):
        self.empty_value = empty_value
    
    def analyze_mosaic_task(self, task_data: dict) -> dict:
        """
        Analyse une tâche ARC complète pour détecter des patterns de mosaïque
        """
        results = {
            'is_mosaic_task': False,
            'confidence': 0.0,
            'evidence': [],
            'examples_analysis': [],
            'summary': {}
        }
        
        # Analyser chaque exemple d'entraînement
        example_scores = []
        all_evidence = []
        
        for i, example in enumerate(task_data['train']):
            input_grid = np.array(example['input'])
            output_grid = np.array(example['output'])
            
            example_analysis = self.analyze_example(input_grid, output_grid, i)
            results['examples_analysis'].append(example_analysis)
            
            example_scores.append(example_analysis['mosaic_score'])
            all_evidence.extend(example_analysis['evidence'])
        
        # Calculer la confiance globale
        if example_scores:
            avg_score = sum(example_scores) / len(example_scores)
            consistency = self._calculate_consistency(results['examples_analysis'])
            
            results['confidence'] = (avg_score * 0.7) + (consistency * 0.3)
            results['is_mosaic_task'] = results['confidence'] > 0.6
            results['evidence'] = self._consolidate_evidence(all_evidence)
            results['summary'] = self._generate_summary(results)
        
        return results
    
    def analyze_example(self, input_grid: np.ndarray, output_grid: np.ndarray, example_idx: int) -> dict:
        """
        Analyse un exemple input→output pour détecter des patterns de mosaïque
        """
        analysis = {
            'example_idx': example_idx,
            'mosaic_score': 0.0,
            'evidence': [],
            'transformations': [],
            'color_analysis': {},
            'spatial_analysis': {}
        }
        
        # Vérifier que les dimensions sont identiques (critère essentiel)
        if input_grid.shape != output_grid.shape:
            analysis['evidence'].append("Dimensions différentes - pas de mosaïque in-place")
            analysis['color_analysis'] = {'colors_removed': [], 'colors_added': [], 'colors_preserved': [], 'input_colors': [], 'output_colors': [], 'color_mapping': {}}
            analysis['spatial_analysis'] = {'uniform_zones_filled': []}
            return analysis
        
        # 1. ANALYSE DES COULEURS
        color_analysis = self._analyze_color_changes(input_grid, output_grid)
        analysis['color_analysis'] = color_analysis
        
        # Indice fort : perte d'une couleur spécifique
        if color_analysis['colors_removed']:
            analysis['evidence'].append(f"Couleur(s) supprimée(s): {color_analysis['colors_removed']}")
            analysis['mosaic_score'] += 0.3
        
        # 2. ANALYSE SPATIALE DES CHANGEMENTS
        spatial_analysis = self._analyze_spatial_changes(input_grid, output_grid)
        analysis['spatial_analysis'] = spatial_analysis
        
        # Indice : zones uniformes remplacées par des patterns
        if spatial_analysis['uniform_zones_filled']:
            analysis['evidence'].append(f"Zones uniformes remplies: {len(spatial_analysis['uniform_zones_filled'])}")
            analysis['mosaic_score'] += 0.4
        
        # 3. DÉTECTION DE BLOCS RÉPÉTÉS
        block_analysis = self._detect_repeated_blocks(input_grid, output_grid)
        if block_analysis['repeated_blocks']:
            analysis['evidence'].append(f"Blocs répétés détectés: {len(block_analysis['repeated_blocks'])}")
            analysis['mosaic_score'] += 0.3
            analysis['transformations'] = block_analysis['repeated_blocks']
        
        # 4. ANALYSE DE LA COHÉRENCE GÉOMÉTRIQUE
        geometric_score = self._analyze_geometric_coherence(input_grid, output_grid)
        analysis['mosaic_score'] += geometric_score * 0.2
        
        if geometric_score > 0.5:
            analysis['evidence'].append("Cohérence géométrique détectée")
        
        return analysis
    
    def _analyze_color_changes(self, input_grid: np.ndarray, output_grid: np.ndarray) -> dict:
        """
        Analyse les changements de couleurs entre input et output
        """
        input_colors = set(input_grid.flatten())
        output_colors = set(output_grid.flatten())
        
        return {
            'input_colors': sorted(list(input_colors)),
            'output_colors': sorted(list(output_colors)),
            'colors_added': sorted(list(output_colors - input_colors)),
            'colors_removed': sorted(list(input_colors - output_colors)),
            'colors_preserved': sorted(list(input_colors & output_colors)),
            'color_mapping': self._find_color_mappings(input_grid, output_grid)
        }
    
    def _find_color_mappings(self, input_grid: np.ndarray, output_grid: np.ndarray) -> dict:
        """
        Trouve les correspondances de couleurs entre input et output
        """
        mappings = {}
        
        # Pour chaque position où la couleur change
        changed_positions = np.where(input_grid != output_grid)
        
        for i, j in zip(changed_positions[0], changed_positions[1]):
            input_color = input_grid[i, j]
            output_color = output_grid[i, j]
            
            if input_color not in mappings:
                mappings[input_color] = Counter()
            mappings[input_color][output_color] += 1
        
        # Garder seulement les mappings les plus fréquents
        clean_mappings = {}
        for input_color, counter in mappings.items():
            if counter:
                most_common = counter.most_common(1)[0]
                if most_common[1] > 1:  # Au moins 2 occurrences
                    clean_mappings[input_color] = most_common[0]
        
        return clean_mappings
    
    def _analyze_spatial_changes(self, input_grid: np.ndarray, output_grid: np.ndarray) -> dict:
        """
        Analyse les changements spatiaux pour détecter le remplissage de zones
        """
        h, w = input_grid.shape
        
        # Trouver les zones qui ont changé
        diff_mask = input_grid != output_grid
        
        # Identifier les zones uniformes dans l'input qui ont été modifiées
        uniform_zones_filled = []
        
        # Chercher des rectangles de couleur uniforme qui ont été remplis
        for color in set(input_grid.flatten()):
            if color == self.empty_value:
                continue
                
            # Trouver les zones de cette couleur dans l'input
            color_mask = input_grid == color
            
            # Voir si ces zones ont été systématiquement remplacées
            replaced_mask = color_mask & diff_mask
            replacement_ratio = np.sum(replaced_mask) / max(np.sum(color_mask), 1)
            
            if replacement_ratio > 0.8:  # 80% de la couleur remplacée
                uniform_zones_filled.append({
                    'original_color': int(color),
                    'replacement_ratio': replacement_ratio,
                    'positions': np.where(replaced_mask)
                })
        
        return {
            'total_changes': int(np.sum(diff_mask)),
            'change_ratio': np.sum(diff_mask) / (h * w),
            'uniform_zones_filled': uniform_zones_filled
        }
    
    def _detect_repeated_blocks(self, input_grid: np.ndarray, output_grid: np.ndarray) -> dict:
        """
        Détecte les blocs qui se répètent dans la transformation
        """
        h, w = input_grid.shape
        repeated_blocks = []
        
        # Tester différentes tailles de blocs
        for block_h in range(2, min(6, h//2)):
            for block_w in range(2, min(6, w//2)):
                blocks_found = self._find_repeating_blocks(input_grid, output_grid, block_h, block_w)
                repeated_blocks.extend(blocks_found)
        
        # Éliminer les doublons et garder les meilleurs
        unique_blocks = self._deduplicate_blocks(repeated_blocks)
        
        return {
            'repeated_blocks': unique_blocks[:10],  # Top 10
            'total_found': len(repeated_blocks)
        }
    
    def _find_repeating_blocks(self, input_grid: np.ndarray, output_grid: np.ndarray, 
                              block_h: int, block_w: int) -> list:
        """
        Trouve les blocs de taille donnée qui se répètent
        """
        h, w = input_grid.shape
        blocks = []
        
        # Extraire tous les blocs possibles de l'input
        input_blocks = {}
        for i in range(h - block_h + 1):
            for j in range(w - block_w + 1):
                block = input_grid[i:i+block_h, j:j+block_w]
                
                # Ignorer les blocs trop vides
                if np.sum(block != self.empty_value) < (block_h * block_w * 0.3):
                    continue
                
                block_key = tuple(block.flatten())
                if block_key not in input_blocks:
                    input_blocks[block_key] = []
                input_blocks[block_key].append((i, j, block))
        
        # Chercher les blocs qui apparaissent dans l'output
        for i in range(h - block_h + 1):
            for j in range(w - block_w + 1):
                output_block = output_grid[i:i+block_h, j:j+block_w]
                output_key = tuple(output_block.flatten())
                
                # Voir si ce bloc de l'output correspond à un bloc de l'input
                for input_key, positions in input_blocks.items():
                    if len(positions) > 1:  # Le bloc apparaît plusieurs fois dans l'input
                        similarity = self._calculate_block_similarity(input_key, output_key)
                        if similarity > 0.8:
                            blocks.append({
                                'input_positions': positions,
                                'output_position': (i, j),
                                'block_size': (block_h, block_w),
                                'similarity': similarity,
                                'pattern': np.array(input_key).reshape(block_h, block_w)
                            })
        
        return blocks
    
    def _calculate_block_similarity(self, block1_key: tuple, block2_key: tuple) -> float:
        """
        Calcule la similarité entre deux blocs
        """
        if len(block1_key) != len(block2_key):
            return 0.0
        
        matches = sum(1 for a, b in zip(block1_key, block2_key) if a == b)
        return matches / len(block1_key)
    
    def _deduplicate_blocks(self, blocks: list) -> list:
        """
        Élimine les blocs en doublon et garde les meilleurs
        """
        if not blocks:
            return []
        
        # Trier par similarité décroissante
        blocks.sort(key=lambda x: x['similarity'], reverse=True)
        
        unique_blocks = []
        used_positions = set()
        
        for block in blocks:
            output_pos = block['output_position']
            
            # Éviter les chevauchements
            if output_pos not in used_positions:
                unique_blocks.append(block)
                used_positions.add(output_pos)
        
        return unique_blocks
    
    def _analyze_geometric_coherence(self, input_grid: np.ndarray, output_grid: np.ndarray) -> float:
        """
        Analyse la cohérence géométrique des transformations
        """
        # Calculer des métriques de cohérence géométrique
        diff_mask = input_grid != output_grid
        
        if np.sum(diff_mask) == 0:
            return 0.0
        
        # Analyser la distribution des changements
        changed_positions = np.where(diff_mask)
        
        # Mesurer la régularité spatiale des changements
        if len(changed_positions[0]) < 4:
            return 0.0
        
        # Calculer la variance des distances entre changements
        positions = list(zip(changed_positions[0], changed_positions[1]))
        distances = []
        
        for i in range(min(len(positions), 20)):  # Limiter pour la performance
            for j in range(i+1, min(len(positions), 20)):
                dist = np.sqrt((positions[i][0] - positions[j][0])**2 + 
                              (positions[i][1] - positions[j][1])**2)
                distances.append(dist)
        
        if not distances:
            return 0.0
        
        # Une faible variance indique une structure régulière
        variance = np.var(distances)
        max_variance = np.var([0, np.sqrt(input_grid.shape[0]**2 + input_grid.shape[1]**2)])
        
        regularity_score = 1.0 - min(variance / max_variance, 1.0)
        return regularity_score
    
    def _calculate_consistency(self, examples_analysis: list) -> float:
        """
        Calcule la cohérence entre les exemples
        """
        if len(examples_analysis) < 2:
            return 1.0
        
        # Vérifier la cohérence des types d'evidence
        evidence_types = set()
        for analysis in examples_analysis:
            for evidence in analysis['evidence']:
                if 'Couleur' in evidence:
                    evidence_types.add('color_removal')
                elif 'Zones uniformes' in evidence:
                    evidence_types.add('zone_filling')
                elif 'Blocs répétés' in evidence:
                    evidence_types.add('block_repetition')
        
        # Plus il y a de types d'evidence cohérents, plus c'est fiable
        consistency = len(evidence_types) / 3.0  # 3 types maximum
        
        # Vérifier la cohérence des scores
        scores = [analysis['mosaic_score'] for analysis in examples_analysis]
        score_variance = np.var(scores)
        score_consistency = 1.0 - min(score_variance, 1.0)
        
        return (consistency + score_consistency) / 2.0
    
    def _consolidate_evidence(self, all_evidence: list) -> list:
        """
        Consolide les preuves de tous les exemples
        """
        evidence_counter = Counter(all_evidence)
        consolidated = []
        
        for evidence, count in evidence_counter.most_common():
            if count > 1:
                consolidated.append(f"{evidence} (dans {count} exemples)")
            else:
                consolidated.append(evidence)
        
        return consolidated
    
    def _generate_summary(self, results: dict) -> dict:
        """
        Génère un résumé de l'analyse
        """
        examples = results['examples_analysis']
        
        return {
            'total_examples': len(examples),
            'avg_mosaic_score': sum(ex['mosaic_score'] for ex in examples) / len(examples) if examples else 0,
            'examples_with_evidence': len([ex for ex in examples if ex['evidence']]),
            'most_common_evidence': results['evidence'][:3] if results['evidence'] else [],
            'color_removal_detected': any('Couleur' in ev for ev in results['evidence']),
            'zone_filling_detected': any('Zones uniformes' in ev for ev in results['evidence']),
            'block_repetition_detected': any('Blocs répétés' in ev for ev in results['evidence'])
        }
    
    def print_analysis(self, results: dict, task_id: str = ""):
        """
        Affiche l'analyse de manière lisible
        """
        print(f"{'='*60}")
        print(f"ANALYSE MOSAÏQUE AMÉLIORÉE - {task_id}")
        print(f"{'='*60}")
        
        print(f"\n🎯 RÉSULTAT GLOBAL:")
        print(f"   Mosaïque détectée: {'✅ OUI' if results['is_mosaic_task'] else '❌ NON'}")
        print(f"   Confiance: {results['confidence']:.2f}")
        
        if results['evidence']:
            print(f"\n🔍 PREUVES DÉTECTÉES:")
            for evidence in results['evidence']:
                print(f"   • {evidence}")
        
        summary = results['summary']
        print(f"\n📊 RÉSUMÉ:")
        print(f"   • Exemples analysés: {summary['total_examples']}")
        print(f"   • Score moyen: {summary['avg_mosaic_score']:.2f}")
        print(f"   • Exemples avec preuves: {summary['examples_with_evidence']}")
        print(f"   • Suppression de couleur: {'✅' if summary['color_removal_detected'] else '❌'}")
        print(f"   • Remplissage de zones: {'✅' if summary['zone_filling_detected'] else '❌'}")
        print(f"   • Répétition de blocs: {'✅' if summary['block_repetition_detected'] else '❌'}")
        
        # Détails par exemple
        print(f"\n📋 DÉTAILS PAR EXEMPLE:")
        for analysis in results['examples_analysis']:
            idx = analysis['example_idx']
            score = analysis['mosaic_score']
            evidence_count = len(analysis['evidence'])
            print(f"   Exemple {idx+1}: Score {score:.2f}, {evidence_count} preuves")
            
            if analysis['color_analysis']['colors_removed']:
                removed = analysis['color_analysis']['colors_removed']
                print(f"      → Couleurs supprimées: {removed}")
            
            if analysis['spatial_analysis']['uniform_zones_filled']:
                zones = len(analysis['spatial_analysis']['uniform_zones_filled'])
                print(f"      → Zones uniformes remplies: {zones}")

# Fonction de test
def test_improved_detector():
    """
    Test le détecteur amélioré sur les tâches spécifiées
    """
    import json
    from pathlib import Path
    
    task_ids = [
        "0dfd9992", "29ec7d0e", "3631a71a", "484b58aa", 
        "9ecd008a", "b8825c91", "c3f564a4", "dc0a314f"
    ]
    
    detector = ImprovedMosaicDetector()
    
    print("=" * 80)
    print("TEST DU DÉTECTEUR DE MOSAÏQUE AMÉLIORÉ")
    print("=" * 80)
    
    for task_id in task_ids:
        try:
            task_path = Path(f"../arcdata/training/{task_id}.json")
            with open(task_path, 'r') as f:
                task_data = json.load(f)
            
            results = detector.analyze_mosaic_task(task_data)
            detector.print_analysis(results, task_id)
            print()
            
        except Exception as e:
            print(f"❌ Erreur pour {task_id}: {e}")

if __name__ == "__main__":
    test_improved_detector()