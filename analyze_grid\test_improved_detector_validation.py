#!/usr/bin/env python3
"""
Test de validation du détecteur amélioré contre les faux positifs
"""

import numpy as np
from ImprovedMosaicDetector import ImprovedMosaicDetector

def create_fake_task(input_grids, output_grids):
    """Crée une fausse tâche ARC pour les tests"""
    return {
        'train': [
            {'input': inp.tolist(), 'output': out.tolist()} 
            for inp, out in zip(input_grids, output_grids)
        ]
    }

def test_improved_detector_validation():
    """Test le détecteur amélioré contre les faux positifs"""
    
    detector = ImprovedMosaicDetector()
    
    print("=" * 80)
    print("TEST DE VALIDATION - DÉTECTEUR AMÉLIORÉ")
    print("=" * 80)
    
    # Test 1: Grilles complètement aléatoires
    print("\n🎲 TEST 1: Grilles aléatoires (pas de mosaïque)")
    np.random.seed(42)
    random_inputs = [np.random.randint(0, 5, (10, 10)) for _ in range(3)]
    random_outputs = [np.random.randint(0, 5, (10, 10)) for _ in range(3)]
    
    fake_task1 = create_fake_task(random_inputs, random_outputs)
    result1 = detector.analyze_mosaic_task(fake_task1)
    
    print(f"   Résultat: {'✅ Mosaïque' if result1['is_mosaic_task'] else '❌ Pas mosaïque'}")
    print(f"   Confiance: {result1['confidence']:.2f}")
    print(f"   Preuves: {len(result1['evidence'])}")
    
    # Test 2: Transformation simple (pas de mosaïque)
    print("\n📐 TEST 2: Transformation simple (rotation)")
    base_grid = np.array([
        [1, 2, 0],
        [3, 4, 0],
        [0, 0, 0]
    ])
    
    simple_inputs = [base_grid, base_grid, base_grid]
    simple_outputs = [np.rot90(base_grid), np.rot90(base_grid), np.rot90(base_grid)]
    
    fake_task2 = create_fake_task(simple_inputs, simple_outputs)
    result2 = detector.analyze_mosaic_task(fake_task2)
    
    print(f"   Résultat: {'✅ Mosaïque' if result2['is_mosaic_task'] else '❌ Pas mosaïque'}")
    print(f"   Confiance: {result2['confidence']:.2f}")
    print(f"   Preuves: {len(result2['evidence'])}")
    
    # Test 3: Suppression de couleur sans mosaïque
    print("\n🎨 TEST 3: Suppression de couleur simple")
    color_inputs = []
    color_outputs = []
    
    for i in range(3):
        inp = np.random.choice([0, 1, 2, 9], (8, 8), p=[0.5, 0.2, 0.2, 0.1])
        out = inp.copy()
        out[out == 9] = 0  # Supprimer la couleur 9
        color_inputs.append(inp)
        color_outputs.append(out)
    
    fake_task3 = create_fake_task(color_inputs, color_outputs)
    result3 = detector.analyze_mosaic_task(fake_task3)
    
    print(f"   Résultat: {'✅ Mosaïque' if result3['is_mosaic_task'] else '❌ Pas mosaïque'}")
    print(f"   Confiance: {result3['confidence']:.2f}")
    print(f"   Preuves: {len(result3['evidence'])}")
    
    # Test 4: Vraie mosaïque simple (contrôle positif)
    print("\n✅ TEST 4: Vraie mosaïque (contrôle positif)")
    
    # Créer une vraie mosaïque : bloc répété avec transformation
    mosaic_inputs = []
    mosaic_outputs = []
    
    for i in range(2):
        inp = np.zeros((6, 6))
        inp[0:2, 0:2] = [[1, 2], [3, 4]]  # Bloc source
        inp[4, 4] = 9  # Marqueur à remplacer
        
        out = inp.copy()
        out[4:6, 4:6] = [[2, 1], [4, 3]]  # Bloc transformé (flip horizontal)
        out[out == 9] = 0  # Supprimer le marqueur
        
        mosaic_inputs.append(inp)
        mosaic_outputs.append(out)
    
    fake_task4 = create_fake_task(mosaic_inputs, mosaic_outputs)
    result4 = detector.analyze_mosaic_task(fake_task4)
    
    print(f"   Résultat: {'✅ Mosaïque' if result4['is_mosaic_task'] else '❌ Pas mosaïque'}")
    print(f"   Confiance: {result4['confidence']:.2f}")
    print(f"   Preuves: {len(result4['evidence'])}")
    
    # Test 5: Dimensions différentes (pas de mosaïque in-place)
    print("\n📏 TEST 5: Dimensions différentes")
    diff_inputs = [np.random.randint(0, 4, (10, 10)) for _ in range(2)]
    diff_outputs = [np.random.randint(0, 4, (5, 5)) for _ in range(2)]
    
    fake_task5 = create_fake_task(diff_inputs, diff_outputs)
    result5 = detector.analyze_mosaic_task(fake_task5)
    
    print(f"   Résultat: {'✅ Mosaïque' if result5['is_mosaic_task'] else '❌ Pas mosaïque'}")
    print(f"   Confiance: {result5['confidence']:.2f}")
    print(f"   Preuves: {len(result5['evidence'])}")
    
    # Analyse des résultats
    print("\n" + "=" * 50)
    print("ANALYSE DES RÉSULTATS:")
    
    tests = [
        ("Grilles aléatoires", result1, False),
        ("Transformation simple", result2, False),
        ("Suppression couleur", result3, False),
        ("Vraie mosaïque", result4, True),
        ("Dimensions différentes", result5, False)
    ]
    
    correct_predictions = 0
    false_positives = 0
    false_negatives = 0
    
    for name, result, expected_mosaic in tests:
        predicted_mosaic = result['is_mosaic_task']
        
        if predicted_mosaic == expected_mosaic:
            correct_predictions += 1
            print(f"✅ CORRECT: {name}")
        elif predicted_mosaic and not expected_mosaic:
            false_positives += 1
            print(f"❌ FAUX POSITIF: {name} (conf: {result['confidence']:.2f})")
        else:
            false_negatives += 1
            print(f"❌ FAUX NÉGATIF: {name} (conf: {result['confidence']:.2f})")
    
    print(f"\n📊 BILAN:")
    print(f"   Prédictions correctes: {correct_predictions}/5 ({correct_predictions/5*100:.1f}%)")
    print(f"   Faux positifs: {false_positives}")
    print(f"   Faux négatifs: {false_negatives}")
    
    if false_positives == 0 and false_negatives <= 1:
        print(f"\n✅ CONCLUSION: Le détecteur amélioré est fiable!")
    else:
        print(f"\n⚠️  CONCLUSION: Le détecteur a encore des problèmes.")
    
    return correct_predictions, false_positives, false_negatives

if __name__ == "__main__":
    test_improved_detector_validation()