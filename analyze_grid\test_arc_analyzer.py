#!/usr/bin/env python3
"""
Test de l'ARCAnalyzer avec des exemples concrets
"""

from analyze_grid.arc_analyzer import ARCAnalyzer
import json

def test_basic_functionality():
    """Test des fonctionnalités de base de l'ARCAnalyzer"""
    
    print("🧪 TEST DE L'ARCANALYZER")
    print("=" * 40)
    
    # Puzzle d'exemple avec répétition matricielle
    puzzle_example = {
        "train": [
            {
                "input": [[0, 7, 7], [7, 7, 7], [0, 7, 7]],
                "output": [[0, 0, 0, 0, 7, 7, 0, 7, 7], [0, 0, 0, 7, 7, 7, 7, 7, 7], [0, 0, 0, 0, 7, 7, 0, 7, 7], [0, 7, 7, 0, 7, 7, 0, 7, 7], [7, 7, 7, 7, 7, 7, 7, 7, 7], [0, 7, 7, 0, 7, 7, 0, 7, 7], [0, 0, 0, 0, 7, 7, 0, 7, 7], [0, 0, 0, 7, 7, 7, 7, 7, 7], [0, 0, 0, 0, 7, 7, 0, 7, 7]]
            },
            {
                "input": [[4, 0, 4], [0, 0, 0], [0, 4, 0]],
                "output": [[4, 0, 4, 0, 0, 0, 4, 0, 4], [0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 4, 0, 0, 0, 0, 0, 4, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 4, 0, 4, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 4, 0, 0, 0, 0]]
            }
        ],
        "test": [
            {
                "input": [[7, 0, 7], [7, 0, 7], [7, 7, 0]],
                "output": [[7, 0, 7, 0, 0, 0, 7, 0, 7], [7, 0, 7, 0, 0, 0, 7, 0, 7], [7, 7, 0, 0, 0, 0, 7, 7, 0], [7, 0, 7, 0, 0, 0, 7, 0, 7], [7, 0, 7, 0, 0, 0, 7, 0, 7], [7, 7, 0, 0, 0, 0, 7, 7, 0], [7, 0, 7, 7, 0, 7, 0, 0, 0], [7, 0, 7, 7, 0, 7, 0, 0, 0], [7, 7, 0, 7, 7, 0, 0, 0, 0]]
            }
        ]
    }
    
    # Initialiser l'analyseur
    analyzer = ARCAnalyzer()
    
    # Analyser le puzzle
    print("📊 Analyse en cours...")
    analysis = analyzer.analyze_puzzle(puzzle_example)
    
    # Générer le rapport
    print("\n📋 RAPPORT D'ANALYSE")
    print("-" * 40)
    report = analyzer.generate_report(analysis)
    print(report)
    
    # Afficher des détails spécifiques
    print("\n🔍 DÉTAILS SPÉCIFIQUES")
    print("-" * 40)
    
    # Informations sur les grilles
    grid_info = analysis['grid_info']
    print(f"✓ Exemples d'entraînement: {grid_info['train_examples']}")
    print(f"✓ Changement cohérent: {grid_info.get('consistent_dimension_change', False)}")
    
    # Patterns détectés
    patterns = analysis['patterns']
    print(f"✓ Répétition détectée: {patterns['repetition']['detected']}")
    if patterns['repetition']['detected']:
        print(f"  - Type: {patterns['repetition']['type']}")
        print(f"  - Facteur: {patterns['repetition']['factor']}")
    
    print(f"✓ Mise à l'échelle détectée: {patterns['scaling']['detected']}")
    if patterns['scaling']['detected']:
        print(f"  - Facteurs: {patterns['scaling']['factors']}")
    
    # Complexité
    complexity = analysis['complexity']
    print(f"✓ Complexité globale: {complexity['overall_complexity']:.2f}")
    print(f"  - Taille: {complexity['grid_size_complexity']:.1f}")
    print(f"  - Couleurs: {complexity['color_complexity']}")
    print(f"  - Objets: {complexity['object_complexity']:.1f}")
    print(f"  - Transformations: {complexity['transformation_complexity']}")
    
    # Couleurs
    colors = analysis['colors']
    print(f"✓ Couleurs d'entrée: {sorted(colors['input_colors'])}")
    print(f"✓ Couleurs de sortie: {sorted(colors['output_colors'])}")
    
    return analysis

def test_simple_puzzle():
    """Test avec un puzzle plus simple"""
    
    print("\n\n🧪 TEST PUZZLE SIMPLE")
    print("=" * 40)
    
    simple_puzzle = {
        "train": [
            {
                "input": [[1, 0], [0, 1]],
                "output": [[1, 0, 1, 0], [0, 1, 0, 1], [1, 0, 1, 0], [0, 1, 0, 1]]
            }
        ],
        "test": []
    }
    
    analyzer = ARCAnalyzer()
    analysis = analyzer.analyze_puzzle(simple_puzzle)
    
    print("📋 RAPPORT PUZZLE SIMPLE")
    print("-" * 40)
    report = analyzer.generate_report(analysis)
    print(report)
    
    return analysis

if __name__ == "__main__":
    # Test principal
    analysis1 = test_basic_functionality()
    
    # Test simple
    analysis2 = test_simple_puzzle()
    
    print("\n✅ TESTS TERMINÉS")
    print("L'ARCAnalyzer fonctionne correctement !")