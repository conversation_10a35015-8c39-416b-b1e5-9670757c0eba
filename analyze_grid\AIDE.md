# Aide Rapide - AnalysesGrilles

## Commandes Principales

### Analyse d'une tâche spécifique
```bash
python run_grid_analysis.py --taskId 007bbfb7
```
**Résultat** : <PERSON><PERSON>er `007bbfb7_analysis.json` dans `arcdata/training/`

### Analyse de plusieurs tâches
```bash
python run_grid_analysis.py --max-puzzles 5
```
**Résultat** : Rapport global + CSV + graphiques

### Démonstration rapide
```bash
python quick_start.py
```
**Résultat** : Exemples d'analyse avec explications

### Tests
```bash
python test_pure_analysis.py
```
**Résultat** : Validation des algorithmes

## Options Utiles

| Option | Description | Exemple |
|--------|-------------|---------|
| `--taskId` | Tâche spécifique | `--taskId 007bbfb7` |
| `--max-puzzles` | Limiter le nombre | `--max-puzzles 10` |
| `--no-viz` | Pas de graphiques | `--no-viz` |
| `--quiet` | Mode silencieux | `--quiet` |
| `--data-dir` | Autre répertoire | `--data-dir /path/to/data` |

## Types d'Analyse

### Transformations Détectées
- **multiply_uniform** : Multiplication uniforme (2x2 → 4x4)
- **same_size** : Même taille avec modifications
- **divide_uniform** : Division uniforme
- **row_modification** : Ajout/suppression de lignes
- **column_modification** : Ajout/suppression de colonnes

### Patterns Détectés
- **pattern_replication** : Réplication de motifs
- **fill_pattern** : Remplissage de zones
- **border_pattern** : Modifications de bordures
- **symmetry_pattern** : Symétries

### Commandes AGI Suggérées
- **MULTIPLY** : Multiplication de grille
- **FILL** : Remplissage
- **SURROUND** : Ajout de bordures
- **COPY/PASTE** : Copie de motifs
- **FLOODFILL** : Remplissage par zone

## Fichiers Générés

### Analyse Individuelle
- `arcdata/training/{taskId}_analysis.json` - Analyse complète d'une tâche

### Analyse Multiple
- `grid_analysis_report.md` - Rapport détaillé
- `grid_analysis_data.csv` - Données pour Excel
- `grid_analysis_dashboard.png` - Graphiques

## Exemples Pratiques

### Analyser une tâche difficile
```bash
python run_grid_analysis.py --taskId 007bbfb7
```

### Analyser 20 tâches sans graphiques
```bash
python run_grid_analysis.py --max-puzzles 20 --no-viz
```

### Mode silencieux pour scripts
```bash
python run_grid_analysis.py --taskId 025d127b --quiet --no-viz
```

## Interprétation des Résultats

### Score de Consistance
- **1.0** : Toutes les transformations sont identiques
- **0.5** : Transformations variées
- **0.0** : Chaque exemple est différent

### Niveau de Confiance AGI
- **high** : Transformations consistantes et claires
- **medium** : Quelques variations
- **low** : Transformations complexes ou ambiguës

### Insights Calculés
- ✓ **detects_expansion** : Grille agrandie
- ✓ **detects_fill_operation** : Remplissage détecté
- ✓ **preserves_structure** : Structure préservée
- ✗ **has_major_changes** : Pas de changements majeurs

## Dépannage

### Erreur "Fichier non trouvé"
Vérifiez que le taskId existe dans le répertoire de données

### Erreur "Modules manquants"
```bash
pip install numpy pandas matplotlib seaborn
```

### Pas de graphiques générés
Utilisez `--no-viz` si matplotlib pose problème

## Support

- Documentation complète : `README_grid_analysis.md`
- Guide d'utilisation : `UTILISATION.md`
- Tous les algorithmes sont transparents et documentés