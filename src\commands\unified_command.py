"""
Module contenant la classe UnifiedCommand pour le parsing des commandes AGI
"""

from typing import List, Dict, Any, Optional


class UnifiedCommand:
    """Classe pour le traitement unifié des commandes AGI"""

    def __init__(self, command_type: str, params: Optional[Dict[str, Any]] = None):
        """
        Initialise une commande unifiée
        
        Args:
            command_type: Type de la commande (INIT, EDIT, etc.)
            params: Paramètres optionnels de la commande
        """
        self.command_type = command_type
        self.params = params or {}
        self.parameters = []  # Liste des paramètres parsés
        self.coordinates = []  # Liste des coordonnées parsées

    @classmethod
    def parse(cls, command_str: str) -> Optional['UnifiedCommand']:
        """
        Parser pour les commandes AGI
        
        Args:
            command_str: Chaîne de commande à parser
            
        Returns:
            Instance de UnifiedCommand ou None si le parsing échoue
        """
        if not command_str or not command_str.strip():
            return None

        command_str = command_str.strip()
        
        # Extraire le type de commande et les paramètres
        parts = command_str.split()
        if not parts:
            return None

        command_type = parts[0]
        
        # Créer l'objet commande
        cmd = cls(command_type)
        
        # Parser les paramètres et coordonnées
        if len(parts) > 1:
            # Reconstituer la commande pour traiter les zones de coordonnées
            remaining_parts = parts[1:]
            params = []
            coordinates = []

            i = 0
            while i < len(remaining_parts):
                part = remaining_parts[i]

                # Vérifier si c'est le début d'une zone de coordonnées [x,y ...]
                if part.startswith('['):
                    # Reconstituer la zone complète
                    zone_parts = [part]
                    j = i + 1
                    while j < len(remaining_parts) and not remaining_parts[j-1].endswith(']'):
                        zone_parts.append(remaining_parts[j])
                        j += 1

                    # Joindre la zone complète
                    zone_str = ' '.join(zone_parts)

                    if zone_str.endswith(']'):
                        # Parser la zone [x1,y1 x2,y2] ou [x,y]
                        coord_content = zone_str[1:-1]  # Enlever [ et ]

                        # Diviser par espaces et virgules
                        coord_parts = coord_content.replace(',', ' ').split()

                        # Traiter les coordonnées par paires
                        for k in range(0, len(coord_parts), 2):
                            if k + 1 < len(coord_parts):
                                try:
                                    x = int(coord_parts[k])
                                    y = int(coord_parts[k + 1])
                                    coordinates.append([x, y])
                                except ValueError:
                                    pass

                        i = j  # Avancer après la zone
                    else:
                        # Zone mal formée, traiter comme paramètre
                        params.append(part)
                        i += 1
                else:
                    # Paramètre normal
                    params.append(part)
                    i += 1
        
        # Stocker les paramètres parsés
        cmd.parameters = params  # Liste des paramètres
        cmd.coordinates = coordinates  # Liste des coordonnées
        cmd.params = {
            'raw_command': command_str, 
            'parameters': params, 
            'coordinates': coordinates
        }
        
        return cmd

    def __str__(self) -> str:
        """Représentation string de la commande"""
        return f"{self.command_type}({self.parameters}, {self.coordinates})"

    def __repr__(self) -> str:
        """Représentation pour debug"""
        return f"UnifiedCommand(type='{self.command_type}', params={self.parameters}, coords={self.coordinates})"
