"""
Utilitaires pour le parsing et traitement des coordonnées dans les commandes AGI
"""

import re
import numpy as np
from typing import List, Callable, Any


class CoordinateUtils:
    """Classe utilitaire pour le traitement des coordonnées"""
    
    @staticmethod
    def parse_coordinate_blocks(raw_command: str) -> List[List[str]]:
        """
        Parse les blocs de coordonnées depuis la commande brute
        Retourne un tableau où chaque élément est un bloc de coordonnées :
        - [coord] pour une cellule simple
        - [coord1, coord2] pour un rectangle
        - ['INVERT', coord1, coord2] pour un modificateur INVERT
        - ['COLOR', params, coord1, coord2] pour un modificateur COLOR
        """
        coordinate_blocks = []
        
        if not raw_command:
            return coordinate_blocks
        
        # Vérifier d'abord les nouveaux formats avec modificateurs
        
        # Détecter (INVERT ([...])) ou (INVERT [...]) ou ancien format SELECT_INVERT([...])
        invert_pattern_double = r'\(INVERT\s*\(([^)]+)\)\)'
        invert_pattern_single = r'\(INVERT\s*\[([^\]]+)\]\)'
        # Support ancien format SELECT_INVERT
        legacy_invert_pattern = r'SELECT_INVERT\s*\(([^)]+)\)'
        
        invert_match = re.search(invert_pattern_double, raw_command)
        invert_content = None
        
        if invert_match:
            invert_content = invert_match.group(1)
        else:
            invert_match = re.search(invert_pattern_single, raw_command)
            if invert_match:
                invert_content = f'[{invert_match.group(1)}]'  # Remettre les crochets pour uniformiser
            else:
                # Vérifier l'ancien format SELECT_INVERT
                invert_match = re.search(legacy_invert_pattern, raw_command)
                if invert_match:
                    invert_content = invert_match.group(1)  # Contenu déjà avec crochets
        
        if invert_content:
            # Extraire TOUTES les coordonnées pour créer UN SEUL bloc INVERT
            coord_pattern = r'\[([^\]]+)\]'
            coord_matches = re.findall(coord_pattern, invert_content)
            all_coords = []
            for coord_match in coord_matches:
                if ' ' in coord_match:
                    coords = [c.strip() for c in coord_match.split() if c.strip()]
                    all_coords.extend(coords)
                else:
                    all_coords.append(coord_match)
            # Créer UN SEUL bloc INVERT avec toutes les coordonnées
            if all_coords:
                coordinate_blocks.append(['INVERT'] + all_coords)
            return coordinate_blocks
        
        # Détecter (COLOR params ([...])) ou (COLOR params [...]) ou COMMAND (COLOR params [...])
        color_pattern_double = r'\(COLOR\s+([^(]+)\s*\(([^)]+)\)\)'
        color_pattern_single = r'\(COLOR\s+([^\[]+)\s*\[([^\]]+)\]\)'
        color_pattern_with_command = r'\w+\s*\(COLOR\s+([^\[]+)\s*\[([^\]]+)\]\)'
        
        color_match = re.search(color_pattern_double, raw_command)
        color_params = None
        color_content = None
        
        if color_match:
            color_params = color_match.group(1).strip()
            color_content = color_match.group(2)
        else:
            color_match = re.search(color_pattern_single, raw_command)
            if color_match:
                color_params = color_match.group(1).strip()
                color_content = f'[{color_match.group(2)}]'  # Remettre les crochets pour uniformiser
            else:
                # Nouveau: vérifier COMMAND (COLOR params [...])
                color_match = re.search(color_pattern_with_command, raw_command)
                if color_match:
                    color_params = color_match.group(1).strip()
                    color_content = f'[{color_match.group(2)}]'  # Remettre les crochets pour uniformiser
        
        if color_params and color_content:
            # Extraire TOUTES les coordonnées pour créer UN SEUL bloc COLOR
            coord_pattern = r'\[([^\]]+)\]'
            coord_matches = re.findall(coord_pattern, color_content)
            all_coords = []
            for coord_match in coord_matches:
                if ' ' in coord_match:
                    coords = [c.strip() for c in coord_match.split() if c.strip()]
                    all_coords.extend(coords)
                else:
                    all_coords.append(coord_match)
            # Créer UN SEUL bloc COLOR avec toutes les coordonnées
            if all_coords:
                coordinate_blocks.append(['COLOR', color_params] + all_coords)
            return coordinate_blocks
        
        # Format traditionnel : extraire tous les blocs [...]
        block_pattern = r'\[([^\]]+)\]'
        matches = re.finditer(block_pattern, raw_command)
        
        for match in matches:
            block_content = match.group(1).strip()
            
            if ' ' in block_content:
                # Rectangle: "7,6 13,6" -> ["7,6", "13,6"]
                coords = [c.strip() for c in block_content.split() if c.strip()]
                coordinate_blocks.append(coords)
            else:
                # Cellule simple: "17,1" -> ["17,1"]
                coordinate_blocks.append([block_content])
        
        return coordinate_blocks

    @staticmethod
    def process_coordinate_blocks(grid: np.ndarray, coordinate_blocks: List[List[str]], action_func: Callable):
        """
        Applique une action sur chaque bloc de coordonnées
        
        Args:
            grid: Grille numpy sur laquelle appliquer les actions
            coordinate_blocks: Blocs de coordonnées parsés
            action_func: Fonction à appliquer pour chaque bloc (x1, y1, x2, y2)
                        Reçoit les coordonnées normalisées (min, min, max, max)
        """
        for block in coordinate_blocks:
            # Détecter les modificateurs de coordonnées
            if len(block) > 0 and block[0] == 'INVERT':
                CoordinateUtils._process_invert_block(grid, block, action_func)
                        
            elif len(block) > 0 and block[0] == 'COLOR':
                CoordinateUtils._process_color_block(grid, block, action_func)
                        
            elif len(block) == 1:
                # Cellule simple traditionnelle
                CoordinateUtils._process_single_cell(grid, block[0], action_func)
                    
            elif len(block) == 2:
                # Rectangle traditionnel
                CoordinateUtils._process_rectangle(grid, block[0], block[1], action_func)

    @staticmethod
    def _process_invert_block(grid: np.ndarray, block: List[str], action_func: Callable):
        """Traite un bloc avec modificateur INVERT"""
        coords = block[1:]  # Enlever le premier élément 'INVERT'
        
        # Créer un masque qui exclut toutes les zones spécifiées
        excluded_mask = np.zeros(grid.shape, dtype=bool)
        
        # Traiter les coordonnées par paires pour créer des rectangles/cellules
        for i in range(0, len(coords), 2):
            try:
                if i + 1 < len(coords):
                    # Rectangle
                    coord1_parts = coords[i].split(',')
                    coord2_parts = coords[i + 1].split(',')
                    if len(coord1_parts) == 2 and len(coord2_parts) == 2:
                        x1, y1 = int(coord1_parts[0]), int(coord1_parts[1])
                        x2, y2 = int(coord2_parts[0]), int(coord2_parts[1])
                        
                        if (0 <= x1 < grid.shape[0] and 0 <= x2 < grid.shape[0] and
                            0 <= y1 < grid.shape[1] and 0 <= y2 < grid.shape[1]):
                            min_x, max_x = min(x1, x2), max(x1, x2)
                            min_y, max_y = min(y1, y2), max(y1, y2)
                            # Marquer cette zone comme exclue
                            excluded_mask[min_x:max_x+1, min_y:max_y+1] = True
                else:
                    # Cellule simple
                    coord_parts = coords[i].split(',')
                    if len(coord_parts) == 2:
                        x, y = int(coord_parts[0]), int(coord_parts[1])
                        if (0 <= x < grid.shape[0] and 0 <= y < grid.shape[1]):
                            excluded_mask[x, y] = True
            except (ValueError, IndexError):
                continue
        
        # Créer le masque final inversé
        final_mask = ~excluded_mask
        
        # Appliquer l'action à toutes les cellules du masque inversé
        for x in range(grid.shape[0]):
            for y in range(grid.shape[1]):
                if final_mask[x, y]:
                    action_func(x, y, x, y)

    @staticmethod
    def _process_color_block(grid: np.ndarray, block: List[str], action_func: Callable):
        """Traite un bloc avec modificateur COLOR"""
        if len(block) >= 3:
            try:
                color_params = block[1]  # Les paramètres de couleur
                colors = [int(c.strip()) for c in color_params.split(',')]
                coords = block[2:]  # Toutes les coordonnées après les paramètres
                
                # Traiter les coordonnées par paires pour créer des rectangles/cellules
                for i in range(0, len(coords), 2):
                    if i + 1 < len(coords):
                        # Rectangle avec COLOR
                        coord1_parts = coords[i].split(',')
                        coord2_parts = coords[i + 1].split(',')
                        if len(coord1_parts) == 2 and len(coord2_parts) == 2:
                            x1, y1 = int(coord1_parts[0]), int(coord1_parts[1])
                            x2, y2 = int(coord2_parts[0]), int(coord2_parts[1])
                            
                            if (0 <= x1 < grid.shape[0] and 0 <= x2 < grid.shape[0] and
                                0 <= y1 < grid.shape[1] and 0 <= y2 < grid.shape[1]):
                                min_x, max_x = min(x1, x2), max(x1, x2)
                                min_y, max_y = min(y1, y2), max(y1, y2)
                                
                                # Appliquer l'action seulement aux cellules des couleurs spécifiées
                                for x in range(min_x, max_x + 1):
                                    for y in range(min_y, max_y + 1):
                                        cell_color = grid[x, y]
                                        if cell_color in colors:
                                            action_func(x, y, x, y)
                    else:
                        # Cellule simple avec COLOR
                        coord_parts = coords[i].split(',')
                        if len(coord_parts) == 2:
                            x, y = int(coord_parts[0]), int(coord_parts[1])
                            if (0 <= x < grid.shape[0] and 0 <= y < grid.shape[1]):
                                cell_color = grid[x, y]
                                if cell_color in colors:
                                    action_func(x, y, x, y)
            except (ValueError, IndexError):
                pass

    @staticmethod
    def _process_single_cell(grid: np.ndarray, coord_str: str, action_func: Callable):
        """Traite une cellule simple"""
        try:
            coord_parts = coord_str.split(',')
            if len(coord_parts) == 2:
                x, y = int(coord_parts[0]), int(coord_parts[1])
                if (0 <= x < grid.shape[0] and 0 <= y < grid.shape[1]):
                    action_func(x, y, x, y)
        except (ValueError, IndexError):
            pass

    @staticmethod
    def _process_rectangle(grid: np.ndarray, coord1_str: str, coord2_str: str, action_func: Callable):
        """Traite un rectangle"""
        try:
            coord1_parts = coord1_str.split(',')
            coord2_parts = coord2_str.split(',')
            if len(coord1_parts) == 2 and len(coord2_parts) == 2:
                x1, y1 = int(coord1_parts[0]), int(coord1_parts[1])
                x2, y2 = int(coord2_parts[0]), int(coord2_parts[1])
                
                if (0 <= x1 < grid.shape[0] and 0 <= x2 < grid.shape[0] and
                    0 <= y1 < grid.shape[1] and 0 <= y2 < grid.shape[1]):
                    # Normaliser les coordonnées
                    min_x, max_x = min(x1, x2), max(x1, x2)
                    min_y, max_y = min(y1, y2), max(y1, y2)
                    action_func(min_x, min_y, max_x, max_y)
        except (ValueError, IndexError):
            pass
