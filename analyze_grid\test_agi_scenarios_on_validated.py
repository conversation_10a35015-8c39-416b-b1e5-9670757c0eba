#!/usr/bin/env python3
"""
Test du script analyze_mosaic_scenarios.py sur les 8 tâches MOSAIC validées
Analyse les scénarios AGI des puzzles confirmés
"""

import os
import sys
from analyze_mosaic_scenarios import extract_agi_commands, analyze_agi_scenario

def test_agi_scenarios_on_validated():
    """Test les scénarios AGI sur les 8 tâches validées"""
    
    # Les 8 tâches validées comme vrais MOSAIC
    validated_mosaic_ids = [
        "0dfd9992",  # Confirmé
        "29ec7d0e",  # Confirmé mais ensemble de petite mosaic
        "3631a71a",  # Confirmé mais mosaic non centré dans le cadre
        "484b58aa",  # Confirmé
        "9ecd008a",  # Confirmé
        "b8825c91",  # Confirmé
        "c3f564a4",  # Confirmé
        "dc0a314f"   # Confirmé
    ]
    
    print("🎨 ANALYSE DES SCÉNARIOS AGI - 8 TÂCHES MOSAIC VALIDÉES")
    print("=" * 70)
    
    agi_dir = "../arcdata/training"
    results = []
    
    for puzzle_id in validated_mosaic_ids:
        print(f"\n🔍 ANALYSE DE {puzzle_id}")
        print("-" * 50)
        
        agi_file = os.path.join(agi_dir, f"{puzzle_id}_TEST0_VALID.agi")
        
        if os.path.exists(agi_file):
            # Extraire les commandes AGI
            scenario_data = extract_agi_commands(agi_file)
            
            if scenario_data:
                # Analyser le scénario
                analysis = analyze_agi_scenario(scenario_data)
                
                result = {
                    'puzzle_id': puzzle_id,
                    'scenario_data': scenario_data,
                    'analysis': analysis,
                    'has_scenario': True
                }
                
                # Affichage détaillé
                print(f"✅ Scénario AGI trouvé")
                print(f"   Type: {analysis['scenario_type']}")
                print(f"   Commande initiale: {scenario_data['initial_command']}")
                print(f"   Nombre de commandes: {analysis['total_commands']}")
                print(f"   Commandes uniques: {analysis['unique_commands']}")
                print(f"   Complexité: {analysis['complexity_score']}")
                print(f"   Contient MOTIF: {analysis['has_motif']}")
                print(f"   Commandes groupées: {analysis['has_grouped_commands']}")
                
                if analysis['mosaic_indicators']:
                    print(f"   Indicateurs MOSAIC: {', '.join(analysis['mosaic_indicators'])}")
                else:
                    print(f"   Indicateurs MOSAIC: aucun")
                
                print(f"   Commandes principales: {', '.join(analysis['most_common_commands'][:3])}")
                
                # Afficher le scénario complet
                scenario_commands = scenario_data['scenario_commands']
                print(f"\n   📋 SCÉNARIO COMPLET:")
                if len(scenario_commands) > 200:
                    print(f"      {scenario_commands[:200]}...")
                    print(f"      ... (total: {len(scenario_commands)} caractères)")
                else:
                    print(f"      {scenario_commands}")
                
                # Détail des commandes individuelles
                if analysis.get('commands_detail'):
                    print(f"\n   🔧 COMMANDES DÉTAILLÉES:")
                    for i, cmd in enumerate(analysis['commands_detail'][:5], 1):
                        print(f"      {i}. {cmd}")
                    if len(analysis['commands_detail']) > 5:
                        print(f"      ... ({len(analysis['commands_detail']) - 5} commandes supplémentaires)")
                
            else:
                result = {
                    'puzzle_id': puzzle_id,
                    'scenario_data': None,
                    'analysis': None,
                    'has_scenario': False
                }
                print(f"❌ Impossible d'extraire le scénario AGI")
        else:
            result = {
                'puzzle_id': puzzle_id,
                'scenario_data': None,
                'analysis': None,
                'has_scenario': False
            }
            print(f"❌ Fichier AGI non trouvé: {agi_file}")
        
        results.append(result)
    
    return results

def analyze_validated_scenarios_patterns(results):
    """Analyse les patterns dans les scénarios des tâches validées"""
    
    print(f"\n📊 ANALYSE DES PATTERNS - TÂCHES VALIDÉES")
    print("=" * 60)
    
    # Filtrer les résultats avec scénarios
    valid_results = [r for r in results if r['has_scenario'] and r['analysis']]
    
    if not valid_results:
        print("❌ Aucun scénario AGI trouvé pour l'analyse")
        return
    
    print(f"Scénarios analysés: {len(valid_results)}/{len(results)}")
    
    # Statistiques globales
    total_commands = [r['analysis']['total_commands'] for r in valid_results]
    unique_commands = [r['analysis']['unique_commands'] for r in valid_results]
    complexity_scores = [r['analysis']['complexity_score'] for r in valid_results]
    
    print(f"\n📈 STATISTIQUES GLOBALES:")
    print(f"   Commandes totales: min={min(total_commands)}, max={max(total_commands)}, moy={sum(total_commands)/len(total_commands):.1f}")
    print(f"   Commandes uniques: min={min(unique_commands)}, max={max(unique_commands)}, moy={sum(unique_commands)/len(unique_commands):.1f}")
    print(f"   Complexité: min={min(complexity_scores)}, max={max(complexity_scores)}, moy={sum(complexity_scores)/len(complexity_scores):.1f}")
    
    # Analyse des indicateurs MOSAIC
    all_indicators = []
    motif_count = 0
    grouped_count = 0
    
    for r in valid_results:
        analysis = r['analysis']
        all_indicators.extend(analysis['mosaic_indicators'])
        if analysis['has_motif']:
            motif_count += 1
        if analysis['has_grouped_commands']:
            grouped_count += 1
    
    print(f"\n🎯 INDICATEURS MOSAIC:")
    print(f"   Avec MOTIF: {motif_count}/{len(valid_results)} ({motif_count/len(valid_results)*100:.1f}%)")
    print(f"   Avec commandes groupées: {grouped_count}/{len(valid_results)} ({grouped_count/len(valid_results)*100:.1f}%)")
    
    # Indicateurs les plus fréquents
    from collections import Counter
    if all_indicators:
        common_indicators = Counter(all_indicators).most_common(10)
        print(f"   Indicateurs fréquents:")
        for indicator, count in common_indicators:
            print(f"     • {indicator}: {count} occurrences ({count/len(valid_results)*100:.1f}%)")
    else:
        print(f"   Aucun indicateur MOSAIC trouvé")
    
    # Analyse des types de commandes
    all_command_types = []
    for r in valid_results:
        all_command_types.extend(r['analysis']['command_types'])
    
    if all_command_types:
        common_commands = Counter(all_command_types).most_common(10)
        print(f"\n🔧 COMMANDES LES PLUS FRÉQUENTES:")
        for command, count in common_commands:
            print(f"   • {command}: {count} occurrences")
    
    # Analyse des types de scénarios
    scenario_types = [r['analysis']['scenario_type'] for r in valid_results]
    scenario_type_counts = Counter(scenario_types)
    
    print(f"\n📋 TYPES DE SCÉNARIOS:")
    for scenario_type, count in scenario_type_counts.items():
        print(f"   • {scenario_type}: {count} puzzles ({count/len(valid_results)*100:.1f}%)")

def compare_with_clipboard_paste_pattern(results):
    """Compare avec le pattern clipboard_PASTE identifié précédemment"""
    
    print(f"\n🔍 VALIDATION DU PATTERN clipboard_PASTE")
    print("=" * 50)
    
    valid_results = [r for r in results if r['has_scenario'] and r['analysis']]
    
    if not valid_results:
        print("❌ Aucun scénario pour la validation")
        return
    
    # Compter les puzzles avec clipboard_PASTE
    paste_count = 0
    copy_count = 0
    motif_count = 0
    
    print("Détail par puzzle:")
    for r in valid_results:
        puzzle_id = r['puzzle_id']
        indicators = r['analysis']['mosaic_indicators']
        
        has_paste = any('clipboard_PASTE' in ind for ind in indicators)
        has_copy = any('clipboard_COPY' in ind for ind in indicators)
        has_motif = r['analysis']['has_motif']
        
        if has_paste:
            paste_count += 1
        if has_copy:
            copy_count += 1
        if has_motif:
            motif_count += 1
        
        status_paste = "✅" if has_paste else "❌"
        status_copy = "✅" if has_copy else "❌"
        status_motif = "✅" if has_motif else "❌"
        
        print(f"   {puzzle_id}: PASTE {status_paste}, COPY {status_copy}, MOTIF {status_motif}")
    
    print(f"\n📊 RÉSULTATS:")
    print(f"   clipboard_PASTE: {paste_count}/{len(valid_results)} ({paste_count/len(valid_results)*100:.1f}%)")
    print(f"   clipboard_COPY: {copy_count}/{len(valid_results)} ({copy_count/len(valid_results)*100:.1f}%)")
    print(f"   MOTIF: {motif_count}/{len(valid_results)} ({motif_count/len(valid_results)*100:.1f}%)")
    
    # Validation du pattern
    if paste_count >= len(valid_results) * 0.8:  # 80% ou plus
        print(f"\n✅ PATTERN clipboard_PASTE CONFIRMÉ (≥80%)")
    elif paste_count >= len(valid_results) * 0.6:  # 60% ou plus
        print(f"\n⚠️ PATTERN clipboard_PASTE PARTIELLEMENT CONFIRMÉ (≥60%)")
    else:
        print(f"\n❌ PATTERN clipboard_PASTE NON CONFIRMÉ (<60%)")

def detailed_scenario_analysis(results, puzzle_id="0dfd9992"):
    """Analyse détaillée d'un scénario spécifique"""
    
    print(f"\n🔬 ANALYSE DÉTAILLÉE - {puzzle_id}")
    print("=" * 50)
    
    # Trouver le puzzle
    target_result = None
    for r in results:
        if r['puzzle_id'] == puzzle_id:
            target_result = r
            break
    
    if not target_result:
        print(f"❌ Puzzle {puzzle_id} non trouvé dans les résultats")
        return
    
    if not target_result['has_scenario']:
        print(f"❌ Aucun scénario AGI pour {puzzle_id}")
        return
    
    scenario_data = target_result['scenario_data']
    analysis = target_result['analysis']
    
    print(f"📋 Informations générales:")
    print(f"   Type de scénario: {analysis['scenario_type']}")
    print(f"   Commande initiale: {scenario_data['initial_command']}")
    print(f"   Nombre total de commandes: {analysis['total_commands']}")
    print(f"   Commandes uniques: {analysis['unique_commands']}")
    print(f"   Score de complexité: {analysis['complexity_score']}")
    
    print(f"\n🎯 Caractéristiques MOSAIC:")
    print(f"   Contient MOTIF: {analysis['has_motif']}")
    print(f"   Commandes groupées: {analysis['has_grouped_commands']}")
    print(f"   Indicateurs MOSAIC: {analysis['mosaic_indicators']}")
    
    print(f"\n🔧 Commandes détaillées:")
    if analysis.get('commands_detail'):
        for i, cmd in enumerate(analysis['commands_detail'], 1):
            print(f"   {i:2d}. {cmd}")
    
    print(f"\n📄 Scénario complet:")
    print(f"   {scenario_data['scenario_commands']}")

def main():
    """Point d'entrée principal"""
    
    # Tester les scénarios AGI sur les 8 tâches validées
    results = test_agi_scenarios_on_validated()
    
    # Analyser les patterns
    analyze_validated_scenarios_patterns(results)
    
    # Comparer avec le pattern clipboard_PASTE
    compare_with_clipboard_paste_pattern(results)
    
    # Analyse détaillée d'un exemple
    detailed_scenario_analysis(results, "0dfd9992")
    
    print(f"\n🎯 CONCLUSION")
    print("Cette analyse des scénarios AGI sur les tâches validées")
    print("confirme ou infirme les patterns identifiés précédemment.")

if __name__ == "__main__":
    main()