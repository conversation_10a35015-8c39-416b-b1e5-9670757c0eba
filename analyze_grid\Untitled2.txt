﻿import numpy as np
from collections import Counter, defaultdict
from typing import Dict, List, Tuple, Set, Any, Optional
import json
import math
from scipy.spatial.distance import cdist
from sklearn.metrics.pairwise import cosine_similarity
import itertools

class ARCAnalyzerDetailed:
    """
    Analyseur détaillé pour les puzzles ARC avec métriques précises sur les transformations d'objets
    Fournit une analyse approfondie avec des métriques quantitatives
    """
    
    def __init__(self):
        self.colors = list(range(10))  # ARC utilise les couleurs 0-9
        
    def analyze_puzzle(self, puzzle: Dict) -> Dict[str, Any]:
        """
        Analyse complète et détaillée d'un puzzle ARC
        
        Args:
            puzzle: Dict avec 'train' et 'test' contenant les exemples
            
        Returns:
            Dict avec toutes les analyses détaillées du puzzle
        """
        analysis = {
            'grid_info': self.analyze_grid_structure(puzzle),
            'objects': self.analyze_objects_detailed(puzzle),
            'transformations': self.analyze_transformations_detailed(puzzle),
            'patterns': self.detect_patterns_detailed(puzzle),
            'symmetries': self.find_symmetries_detailed(puzzle),
            'complexity': self.measure_complexity_detailed(puzzle),
            'colors': self.analyze_colors_detailed(puzzle),
            'spatial_relations': self.analyze_spatial_relations_detailed(puzzle),
            'object_evolution': self.track_object_evolution(puzzle),
            'metrics': self.compute_detailed_metrics(puzzle)
        }
        return analysis
    
    def analyze_grid_structure(self, puzzle: Dict) -> Dict[str, Any]:
        """Analyse détaillée de la structure des grilles"""
        info = {
            'train_examples': len(puzzle['train']),
            'input_dimensions': [],
            'output_dimensions': [],
            'dimension_changes': [],
            'size_ratios': [],
            'area_changes': [],
            'aspect_ratios': {'input': [], 'output': []},
            'dimension_statistics': {}
        }
        
        all_heights_in = []
        all_widths_in = []
        all_heights_out = []
        all_widths_out = []
        
        for example in puzzle['train']:
            input_grid = np.array(example['input'])
            output_grid = np.array(example['output'])
            
            in_shape = input_grid.shape
            out_shape = output_grid.shape
            
            info['input_dimensions'].append(in_shape)
            info['output_dimensions'].append(out_shape)
            info['dimension_changes'].append((out_shape[0] - in_shape[0], out_shape[1] - in_shape[1]))
            
            all_heights_in.append(in_shape[0])
            all_widths_in.append(in_shape[1])
            all_heights_out.append(out_shape[0])
            all_widths_out.append(out_shape[1])
            
            if in_shape[0] > 0 and in_shape[1] > 0:
                info['size_ratios'].append((out_shape[0] / in_shape[0], out_shape[1] / in_shape[1]))
                info['area_changes'].append((out_shape[0] * out_shape[1]) / (in_shape[0] * in_shape[1]))

                info['aspect_ratios']['input'].append(in_shape[1] / in_shape[0] if in_shape[0] > 0 else 0)
                info['aspect_ratios']['output'].append(out_shape[1] / out_shape[0] if out_shape[0] > 0 else 0)
        
        # Statistiques détaillées
        info['dimension_statistics'] = {
            'input': {
                'min_height': min(all_heights_in) if all_heights_in else 0,
                'max_height': max(all_heights_in) if all_heights_in else 0,
                'mean_height': np.mean(all_heights_in) if all_heights_in else 0,
                'min_width': min(all_widths_in) if all_widths_in else 0,
                'max_width': max(all_widths_in) if all_widths_in else 0,
                'mean_width': np.mean(all_widths_in) if all_widths_in else 0
            },
            'output': {
                'min_height': min(all_heights_out) if all_heights_out else 0,
                'max_height': max(all_heights_out) if all_heights_out else 0,
                'mean_height': np.mean(all_heights_out) if all_heights_out else 0,
                'min_width': min(all_widths_out) if all_widths_out else 0,
                'max_width': max(all_widths_out) if all_widths_out else 0,
                'mean_width': np.mean(all_widths_out) if all_widths_out else 0
            }
        }
        
        # Détection de patterns dans les changements de dimension
        if info['dimension_changes']:
            unique_changes = set(info['dimension_changes'])
            info['consistent_dimension_change'] = len(unique_changes) == 1
            info['most_common_change'] = Counter(info['dimension_changes']).most_common(1)[0][0]
            info['change_variance'] = np.var(info['dimension_changes'], axis=0)
        
        return info
    
    def analyze_objects_detailed(self, puzzle: Dict) -> Dict[str, Any]:
        """Analyse détaillée des objets avec métriques avancées"""
        objects_info = {
            'input_objects': [],
            'output_objects': [],
            'object_transformations': [],
            'object_statistics': {
                'input': {'count': [], 'sizes': [], 'colors': []},
                'output': {'count': [], 'sizes': [], 'colors': []}
            },
            'shape_analysis': []
        }
        
        for example in puzzle['train']:
            input_grid = np.array(example['input'])
            output_grid = np.array(example['output'])
            
            input_objects = self.extract_objects_detailed(input_grid)
            output_objects = self.extract_objects_detailed(output_grid)
            
            objects_info['input_objects'].append(input_objects)
            objects_info['output_objects'].append(output_objects)
            
            # Statistiques
            objects_info['object_statistics']['input']['count'].append(len(input_objects))
            objects_info['object_statistics']['output']['count'].append(len(output_objects))
            objects_info['object_statistics']['input']['sizes'].extend([obj['size'] for obj in input_objects])
            objects_info['object_statistics']['output']['sizes'].extend([obj['size'] for obj in output_objects])
            objects_info['object_statistics']['input']['colors'].extend([obj['color'] for obj in input_objects])
            objects_info['object_statistics']['output']['colors'].extend([obj['color'] for obj in output_objects])
            
            # Analyse de forme
            for obj in input_objects + output_objects:
                shape_features = self.analyze_shape_features(obj)
                objects_info['shape_analysis'].append(shape_features)
            
            objects_info['object_transformations'].append(
                self.analyze_object_transformation_detailed(input_objects, output_objects)
            )
        
        return objects_info
    
    def extract_objects_detailed(self, grid: np.ndarray) -> List[Dict]:
        """Extrait les objets avec descripteurs détaillés"""
        objects = []
        visited = np.zeros_like(grid, dtype=bool)
        
        for i in range(grid.shape[0]):
            for j in range(grid.shape[1]):
                if not visited[i, j] and grid[i, j] != 0:
                    obj = self.flood_fill_detailed(grid, i, j, visited)
                    if obj['size'] > 0:
                        objects.append(obj)
        
        return objects
    
    def flood_fill_detailed(self, grid: np.ndarray, start_i: int, start_j: int, visited: np.ndarray) -> Dict:
        """Remplit une région connexe avec descripteurs avancés"""
        color = grid[start_i, start_j]
        pixels = []
        stack = [(start_i, start_j)]
        
        while stack:
            i, j = stack.pop()
            if (i < 0 or i >= grid.shape[0] or j < 0 or j >= grid.shape[1] or 
                visited[i, j] or grid[i, j] != color):
                continue
                
            visited[i, j] = True
            pixels.append((i, j))
            
            # Connexité 4 et 8
            for di, dj in [(-1, 0), (1, 0), (0, -1), (0, 1), (-1, -1), (-1, 1), (1, -1), (1, 1)]:
                stack.append((i + di, j + dj))
        
        if pixels:
            pixels = np.array(pixels)
            bbox = (pixels[:, 0].min(), pixels[:, 1].min(), pixels[:, 0].max(), pixels[:, 1].max())
            center = (pixels[:, 0].mean(), pixels[:, 1].mean())
            
            # Calcul de l'enveloppe convexe
            hull = self.compute_convex_hull(pixels)
            
            # Descripteurs de forme
            shape_features = self.analyze_shape_features({
                'pixels': pixels,
                'bbox': bbox,
                'center': center
            })
            
            return {
                'color': int(color),
                'pixels': pixels.tolist(),
                'size': len(pixels),
                'bbox': bbox,
                'center': center,
                'hull': hull,
                'shape_features': shape_features,
                'density': len(pixels) / ((bbox[2] - bbox[0] + 1) * (bbox[3] - bbox[1] + 1)) if bbox else 0,
                'aspect_ratio': (bbox[3] - bbox[1] + 1) / (bbox[2] - bbox[0] + 1) if bbox else 0
            }
        
        return {'color': int(color), 'pixels': [], 'size': 0}
    
    def compute_convex_hull(self, pixels: np.ndarray) -> List[Tuple[int, int]]:
        """Calcule l'enveloppe convexe des pixels"""
        if len(pixels) < 3:
            return pixels.tolist()
        
        # Algorithme de Graham scan simplifié
        points = [(int(p[0]), int(p[1])) for p in pixels]
        points = list(set(points))  # Remove duplicates
        
        if len(points) < 3:
            return points
        
        # Find the point with the lowest y-coordinate
        start = min(points, key=lambda p: (p[1], p[0]))
        
        # Sort points by polar angle with start
        def polar_angle(p):
            return math.atan2(p[1] - start[1], p[0] - start[0])
        
        points_sorted = sorted([p for p in points if p != start], key=polar_angle)
        
        # Build convex hull
        hull = [start]
        for point in points_sorted:
            while len(hull) > 1 and self.cross_product(hull[-2], hull[-1], point) <= 0:
                hull.pop()
            hull.append(point)
        
        return hull
    
    def cross_product(self, a: Tuple[int, int], b: Tuple[int, int], c: Tuple[int, int]) -> float:
        """Calcule le produit vectoriel pour l'enveloppe convexe"""
        return (b[0] - a[0]) * (c[1] - a[1]) - (b[1] - a[1]) * (c[0] - a[0])
    
    def analyze_shape_features(self, obj: Dict) -> Dict[str, float]:
        """Analyse les caractéristiques de forme d'un objet"""
        if not obj['pixels']:
            return {}
        
        pixels = np.array(obj['pixels'])
        
        # Moments de forme
        moments = self.compute_shape_moments(pixels)
        
        # Compacité
        area = len(pixels)
        perimeter = self.estimate_perimeter(pixels)
        compactness = 4 * math.pi * area / (perimeter ** 2) if perimeter > 0 else 0
        
        # Circularity
        circularity = self.compute_circularity(pixels)
        
        # Elongation
        elongation = self.compute_elongation(pixels)
        
        # Symétrie
        symmetry = self.compute_symmetry_score(pixels)
        
        return {
            'area': area,
            'perimeter': perimeter,
            'compactness': compactness,
            'circularity': circularity,
            'elongation': elongation,
            'symmetry': symmetry,
            'moments': moments
        }
    
    def compute_shape_moments(self, pixels: np.ndarray) -> Dict[str, float]:
        """Calcule les moments de forme"""
        if len(pixels) == 0:
            return {}
        
        # Centrer les pixels
        center = np.mean(pixels, axis=0)
        centered = pixels - center
        
        # Moments d'ordre 2
        mu20 = np.sum(centered[:, 0] ** 2)
        mu02 = np.sum(centered[:, 1] ** 2)
        mu11 = np.sum(centered[:, 0] * centered[:, 1])
        
        return {
            'mu20': mu20,
            'mu02': mu02,
            'mu11': mu11
        }
    
    def estimate_perimeter(self, pixels: np.ndarray) -> float:
        """Estime le périmètre d'un objet"""
        if len(pixels) == 0:
            return 0
        
        # Convertir en grille binaire
        min_i, min_j = pixels.min(axis=0)
        max_i, max_j = pixels.max(axis=0)
        
        grid = np.zeros((max_i - min_i + 3, max_j - min_j + 3))
        for i, j in pixels:
            grid[i - min_i + 1, j - min_j + 1] = 1
        
        # Compter les transitions
        perimeter = 0
        for i in range(grid.shape[0]):
            for j in range(grid.shape[1]):
                if grid[i, j] == 1:
                    # Vérifier les voisins
                    for di, dj in [(-1, 0), (1, 0), (0, -1), (0, 1)]:
                        ni, nj = i + di, j + dj
                        if ni < 0 or ni >= grid.shape[0] or nj < 0 or nj >= grid.shape[1] or grid[ni, nj] == 0:
                            perimeter += 1
        
        return perimeter
    
    def compute_circularity(self, pixels: np.ndarray) -> float:
        """Calcule la circularité de l'objet"""
        if len(pixels) < 3:
            return 0
        
        # Rayon du cercle équivalent
        area = len(pixels)
        radius_equiv = math.sqrt(area / math.pi)
        
        # Distance moyenne au centre
        center = np.mean(pixels, axis=0)
        distances = np.sqrt(np.sum((pixels - center) ** 2, axis=1))
        
        # Coefficient de variation
        if np.mean(distances) > 0:
            circularity = 1 - (np.std(distances) / np.mean(distances))
        else:
            circularity = 0
        
        return max(0, circularity)
    
    def compute_elongation(self, pixels: np.ndarray) -> float:
        """Calcule l'élongation de l'objet"""
        if len(pixels) < 3:
            return 0
        
        # PCA simplifié
        center = np.mean(pixels, axis=0)
        centered = pixels - center
        
        # Matrice de covariance
        cov = np.cov(centered.T)
        if cov.shape == (2, 2):
            eigenvals = np.linalg.eigvals(cov)
            if len(eigenvals) == 2 and eigenvals[1] > 0:
                return eigenvals[0] / eigenvals[1] if eigenvals[0] > eigenvals[1] else eigenvals[1] / eigenvals[0]
        
        return 1
    
    def compute_symmetry_score(self, pixels: np.ndarray) -> float:
        """Calcule le score de symétrie"""
        if len(pixels) < 3:
            return 0
        
        center = np.mean(pixels, axis=0)
        
        # Tester la symétrie par rapport au centre
        symmetric_pixels = 2 * center - pixels
        symmetric_pixels = np.round(symmetric_pixels).astype(int)
        
        # Compter les pixels symétriques
        original_set = set(map(tuple, pixels))
        symmetric_set = set(map(tuple, symmetric_pixels))
        
        intersection = len(original_set.intersection(symmetric_set))
        return intersection / len(pixels) if len(pixels) > 0 else 0
    
    def analyze_object_transformation_detailed(self, input_objects: List[Dict], output_objects: List[Dict]) -> Dict:
        """Analyse détaillée des transformations entre objets"""
        # Appariement des objets
        matches = self.match_objects(input_objects, output_objects)
        
        transformations = {
            'input_count': len(input_objects),
            'output_count': len(output_objects),
            'count_change': len(output_objects) - len(input_objects),
            'matches': matches,
            'unmatched_input': len(input_objects) - len(matches),
            'unmatched_output': len(output_objects) - len(matches),
            'color_changes': self.analyze_color_changes_detailed(input_objects, output_objects, matches),
            'size_changes': self.analyze_size_changes_detailed(input_objects, output_objects, matches),
            'position_changes': self.analyze_position_changes(input_objects, output_objects, matches),
            'shape_changes': self.analyze_shape_changes(input_objects, output_objects, matches),
            'transformation_matrix': self.compute_transformation_matrix(input_objects, output_objects, matches)
        }
        
        return transformations
    
    def match_objects(self, input_objects: List[Dict], output_objects: List[Dict]) -> List[Dict]:
        """Apparie les objets entre entrée et sortie"""
        matches = []
        
        # Utiliser la distance entre centres et la similarité de couleur
        for i, in_obj in enumerate(input_objects):
            best_match = None
            best_score = float('inf')
            
            for j, out_obj in enumerate(output_objects):
                # Distance entre centres
                if in_obj['center'] is not None and out_obj['center'] is not None:
                    distance = np.sqrt(
                        (in_obj['center'][0] - out_obj['center'][0])**2 +
                        (in_obj['center'][1] - out_obj['center'][1])**2
                    )
                    
                    # Score basé sur distance et couleur
                    color_diff = abs(in_obj['color'] - out_obj['color'])
                    score = distance + color_diff
                    
                    if score < best_score:
                        best_score = score
                        best_match = {
                            'input_index': i,
                            'output_index': j,
                            'score': score,
                            'distance': distance,
                            'color_change': color_diff
                        }
            
            if best_match and best_score < 10:  # Seuil arbitraire
                matches.append(best_match)
        
        return matches
    
    def analyze_color_changes_detailed(self, input_objects: List[Dict], output_objects: List[Dict], matches: List[Dict]) -> Dict:
        """Analyse détaillée des changements de couleur"""
        color_changes = {
            'input_colors': [obj['color'] for obj in input_objects],
            'output_colors': [obj['color'] for obj in output_objects],
            'matched_changes': [],
            'color_mapping': {},
            'new_colors': set(),
            'removed_colors': set()
        }
        
        # Analyser les changements pour les objets appariés
        for match in matches:
            in_color = input_objects[match['input_index']]['color']
            out_color = output_objects[match['output_index']]['color']
            
            if in_color != out_color:
                color_changes['matched_changes'].append({
                    'from': in_color,
                    'to': out_color,
                    'match': match
                })
                
                if in_color not in color_changes['color_mapping']:
                    color_changes['color_mapping'][in_color] = []
                color_changes['color_mapping'][in_color].append(out_color)
        
        # Couleurs non appariées
        matched_input_colors = {input_objects[m['input_index']]['color'] for m in matches}
        matched_output_colors = {output_objects[m['output_index']]['color'] for m in matches}
        
        color_changes['new_colors'] = list(
            set(output_objects[i]['color'] for i in range(len(output_objects))) - matched_output_colors
        )
        color_changes['removed_colors'] = list(
            set(input_objects[i]['color'] for i in range(len(input_objects))) - matched_input_colors
        )
        
        return color_changes
    
    def analyze_size_changes_detailed(self, input_objects: List[Dict], output_objects: List[Dict], matches: List[Dict]) -> Dict:
        """Analyse détaillée des changements de taille"""
        size_changes = {
            'size_ratios': [],
            'area_changes': [],
            'scale_factors': [],
            'statistics': {}
        }
        
        for match in matches:
            in_size = input_objects[match['input_index']]['size']
            out_size = output_objects[match['output_index']]['size']
            
            if in_size > 0:
                ratio = out_size / in_size
                size_changes['size_ratios'].append(ratio)
                size_changes['area_changes'].append(out_size - in_size)
                size_changes['scale_factors'].append(math.sqrt(ratio))
        
        if size_changes['size_ratios']:
            size_changes['statistics'] = {
                'mean_ratio': float(np.mean(size_changes['size_ratios'])),
                'std_ratio': float(np.std(size_changes['size_ratios'])),
                'min_ratio': float(min(size_changes['size_ratios'])),
                'max_ratio': float(max(size_changes['size_ratios']))
            }
        
        return size_changes
    
    def analyze_position_changes(self, input_objects: List[Dict], output_objects: List[Dict], matches: List[Dict]) -> Dict:
        """Analyse les changements de position"""
        position_changes = {
            'translations': [],
            'rotations': [],
            'distances': [],
            'directions': []
        }
        
        for match in matches:
            in_center = input_objects[match['input_index']]['center']
            out_center = output_objects[match['output_index']]['center']
            
            if in_center is not None and out_center is not None:
                dx = out_center[0] - in_center[0]
                dy = out_center[1] - in_center[1]
                
                position_changes['translations'].append((dx, dy))
                position_changes['distances'].append(math.sqrt(dx**2 + dy**2))
                
                # Direction (angle)
                angle = math.atan2(dy, dx)
                position_changes['directions'].append(angle)
        
        return position_changes
    
    def analyze_shape_changes(self, input_objects: List[Dict], output_objects: List[Dict], matches: List[Dict]) -> Dict:
        """Analyse les changements de forme"""
        shape_changes = {
            'shape_similarity': [],
            'form_changes': [],
            'feature_changes': []
        }
        
        for match in matches:
            in_obj = input_objects[match['input_index']]
            out_obj = output_objects[match['output_index']]
            
            # Similarité de forme basée sur les moments
            if 'shape_features' in in_obj and 'shape_features' in out_obj:
                in_features = in_obj['shape_features']
                out_features = out_obj['shape_features']
                
                similarity = self.compute_shape_similarity(in_features, out_features)
                shape_changes['shape_similarity'].append(similarity)
                
                # Changements de caractéristiques
                feature_change = {
                    'compactness_change': out_features.get('compactness', 0) - in_features.get('compactness', 0),
                    'circularity_change': out_features.get('circularity', 0) - in_features.get('circularity', 0),
                    'elongation_change': out_features.get('elongation', 0) - in_features.get('elongation', 0)
                }
                shape_changes['feature_changes'].append(feature_change)
        
        return shape_changes
    
    def compute_shape_similarity(self, features1: Dict, features2: Dict) -> float:
        """Calcule la similarité entre deux formes"""
        if not features1 or not features2:
            return 0
        
        # Similarité basée sur les moments
        moments1 = features1.get('moments', {})
        moments2 = features2.get('moments', {})
        
        if moments1 and moments2:
            vec1 = [moments1.get('mu20', 0), moments1.get('mu02', 0), moments1.get('mu11', 0)]
            vec2 = [moments2.get('mu20', 0), moments2.get('mu02', 0), moments2.get('mu11', 0)]
            
            # Normaliser
            norm1 = np.linalg.norm(vec1)
            norm2 = np.linalg.norm(vec2)
            
            if norm1 > 0 and norm2 > 0:
                vec1 = vec1 / norm1
                vec2 = vec2 / norm2
                
                # Similarité cosinus
                similarity = np.dot(vec1, vec2)
                return max(0, similarity)
        
        return 0
    
    def compute_transformation_matrix(self, input_objects: List[Dict], output_objects: List[Dict], matches: List[Dict]) -> Dict:
        """Calcule la matrice de transformation globale"""
        if len(matches) < 2:
            return {'type': 'insufficient_data'}
        
        # Collecter les points de correspondance
        src_points = []
        dst_points = []
        
        for match in matches:
            in_obj = input_objects[match['input_index']]
            out_obj = output_objects[match['output_index']]
            
            if in_obj['center'] is not None and out_obj['center'] is not None:
                src_points.append(in_obj['center'])
                dst_points.append(out_obj['center'])
        
        if len(src_points) < 2:
            return {'type': 'insufficient_points'}
        
        # Estimer la transformation (translation, échelle, rotation)
        src_points = np.array(src_points)
        dst_points = np.array(dst_points)
        
        # Translation moyenne
        translation = np.mean(dst_points - src_points, axis=0)
        
        # Échelle moyenne
        src_distances = []
        dst_distances = []
        
        for i in range(len(src_points)):
            for j in range(i + 1, len(src_points)):
                src_dist = np.linalg.norm(src_points[i] - src_points[j])
                dst_dist = np.linalg.norm(dst_points[i] - dst_points[j])
                if src_dist > 0:
                    src_distances.append(src_dist)
                    dst_distances.append(dst_dist)
        
        scale = np.mean(np.array(dst_distances) / np.array(src_distances)) if src_distances else 1.0
        
        return {
            'type': 'affine',
            'translation': translation.tolist(),
            'scale': float(scale),
            'confidence': len(matches) / max(len(input_objects), len(output_objects))
        }
    
    def detect_patterns_detailed(self, puzzle: Dict) -> Dict[str, Any]:
        """Détection détaillée des patterns"""
        patterns = {
            'repetition': self.detect_repetition_patterns_detailed(puzzle),
            'scaling': self.detect_scaling_patterns_detailed(puzzle),
            'rotation': self.detect_rotation_patterns_detailed(puzzle),
            'reflection': self.detect_reflection_patterns_detailed(puzzle),
            'translation': self.detect_translation_patterns_detailed(puzzle),
            'color_patterns': self.detect_color_patterns(puzzle),
            'structural_patterns': self.detect_structural_patterns(puzzle)
        }
        return patterns
    
    def detect_repetition_patterns_detailed(self, puzzle: Dict) -> Dict:
        """Détection détaillée des patterns de répétition"""
        repetition_info = {
            'detected': False,
            'type': None,
            'factor': None,
            'confidence': 0,
            'examples': []
        }
        
        for idx, example in enumerate(puzzle['train']):
            input_grid = np.array(example['input'])
            output_grid = np.array(example['output'])
            
            # Test pour répétition en matrice
            if self.is_matrix_repetition_detailed(input_grid, output_grid):
                factor = output_grid.shape[0] // input_grid.shape[0]
                repetition_info['examples'].append({
                    'example_index': idx,
                    'factor': factor,
                    'type': 'matrix_tiling'
                })
        
        if repetition_info['examples']:
            repetition_info['detected'] = True
            repetition_info['type'] = 'matrix_tiling'
            repetition_info['factor'] = repetition_info['examples'][0]['factor']
            repetition_info['confidence'] = len(repetition_info['examples']) / len(puzzle['train'])
        
        return repetition_info
    
    def is_matrix_repetition_detailed(self, input_grid: np.ndarray, output_grid: np.ndarray) -> bool:
        """Vérifie détaillée si output est une répétition matricielle d'input"""
        in_h, in_w = input_grid.shape
        out_h, out_w = output_grid.shape
        
        if out_h % in_h != 0 or out_w % in_w != 0:
            return False
        
        h_factor = out_h // in_h
        w_factor = out_w // in_w
        
        # Vérification plus stricte avec tolérance
        for i in range(h_factor):
            for j in range(w_factor):
                start_i, start_j = i * in_h, j * in_w
                end_i, end_j = start_i + in_h, start_j + in_w
                
                if not np.array_equal(input_grid, output_grid[start_i:end_i, start_j:end_j]):
                    return False
        
        return True
    
    def detect_scaling_patterns_detailed(self, puzzle: Dict) -> Dict:
        """Détection détaillée des patterns de mise à l'échelle"""
        scaling_info = {
            'detected': False,
            'factors': [],
            'confidence': 0,
            'examples': []
        }
        
        for idx, example in enumerate(puzzle['train']):
            input_grid = np.array(example['input'])
            output_grid = np.array(example['output'])
            
            if input_grid.shape[0] > 0 and input_grid.shape[1] > 0:
                h_factor = output_grid.shape[0] / input_grid.shape[0]
                w_factor = output_grid.shape[1] / input_grid.shape[1]
                scaling_info['factors'].append((h_factor, w_factor))
                scaling_info['examples'].append({
                    'example_index': idx,
                    'factors': (h_factor, w_factor)
                })
        
        if scaling_info['factors']:
            unique_factors = set(scaling_info['factors'])
            scaling_info['detected'] = len(unique_factors) == 1 and (1.0, 1.0) not in unique_factors
            scaling_info['consistent_factor'] = len(unique_factors) == 1
            scaling_info['confidence'] = 1.0 / len(unique_factors) if unique_factors else 0
        
        return scaling_info
    
    def detect_rotation_patterns_detailed(self, puzzle: Dict) -> Dict:
        """Détection détaillée des patterns de rotation"""
        rotation_info = {
            'detected': False,
            'angles': [],
            'confidence': 0,
            'examples': []
        }
        
        # Analyse des objets pour détecter la rotation
        for example in puzzle['train']:
            input_grid = np.array(example['input'])
            output_grid = np.array(example['output'])
            
            # Comparer les moments de forme
            input_objects = self.extract_objects_detailed(input_grid)
            output_objects = self.extract_objects_detailed(output_grid)
            
            if len(input_objects) == len(output_objects):
                for in_obj, out_obj in zip(input_objects, output_objects):
                    if 'shape_features' in in_obj and 'shape_features' in out_obj:
                        # Analyse de rotation basée sur les moments
                        pass
        
        return rotation_info
    
    def detect_color_patterns(self, puzzle: Dict) -> Dict:
        """Détecte les patterns de couleur"""
        color_patterns = {
            'color_mapping': {},
            'frequency_patterns': [],
            'gradient_patterns': []
        }
        
        for example in puzzle['train']:
            input_grid = np.array(example['input'])
            output_grid = np.array(example['output'])
            
            # Analyse des fréquences de couleur
            input_freq = Counter(input_grid.flatten())
            output_freq = Counter(output_grid.flatten())
            
            color_patterns['frequency_patterns'].append({
                'input': dict(input_freq),
                'output': dict(output_freq)
            })
        
        return color_patterns
    
    def detect_structural_patterns(self, puzzle: Dict) -> Dict:
        """Détecte les patterns structurels"""
        structural_patterns = {
            'object_relationships': [],
            'spatial_patterns': [],
            'hierarchical_patterns': []
        }
        
        for example in puzzle['train']:
            input_grid = np.array(example['input'])
            output_grid = np.array(example['output'])
            
            # Analyse des relations entre objets
            input_objects = self.extract_objects_detailed(input_grid)
            output_objects = self.extract_objects_detailed(output_grid)
            
            structural_patterns['object_relationships'].append({
                'input_count': len(input_objects),
                'output_count': len(output_objects),
                'relationship': 'preserved' if len(input_objects) == len(output_objects) else 'changed'
            })
        
        return structural_patterns
    
    def find_symmetries_detailed(self, puzzle: Dict) -> Dict[str, Any]:
        """Analyse détaillée des symétries"""
        symmetries = {
            'input_symmetries': [],
            'output_symmetries': [],
            'symmetry_preservation': [],
            'symmetry_statistics': {
                'input': {},
                'output': {},
                'preservation': {}
            }
        }
        
        for example in puzzle['train']:
            input_grid = np.array(example['input'])
            output_grid = np.array(example['output'])
            
            input_sym = self.detect_symmetries_detailed(input_grid)
            output_sym = self.detect_symmetries_detailed(output_grid)
            
            symmetries['input_symmetries'].append(input_sym)
            symmetries['output_symmetries'].append(output_sym)
            symmetries['symmetry_preservation'].append(
                self.compare_symmetries_detailed(input_sym, output_sym)
            )
        
        # Statistiques
        symmetries['symmetry_statistics'] = self.compute_symmetry_statistics(symmetries)
        
        return symmetries
    
    def detect_symmetries_detailed(self, grid: np.ndarray) -> Dict[str, Any]:
        """Détection détaillée des symétries"""
        symmetries = {
            'horizontal': False,
            'vertical': False,
            'diagonal_main': False,
            'diagonal_anti': False,
            'rotational': False,
            'scores': {}
        }
        
        # Test de symétrie avec tolérance
        h, w = grid.shape
        
        # Symétrie horizontale
        if h % 2 == 0:
            top = grid[:h//2, :]
            bottom = np.flipud(grid[h//2:, :])
            symmetries['horizontal'] = np.array_equal(top, bottom)
            symmetries['scores']['horizontal'] = self.compute_symmetry_score_detailed(grid, 'horizontal')
        
        # Symétrie verticale
        if w % 2 == 0:
            left = grid[:, :w//2]
            right = np.fliplr(grid[:, w//2:])
            symmetries['vertical'] = np.array_equal(left, right)
            symmetries['scores']['vertical'] = self.compute_symmetry_score_detailed(grid, 'vertical')
        
        # Symétrie diagonale principale
        if h == w:
            symmetries['diagonal_main'] = np.array_equal(grid, grid.T)
            symmetries['scores']['diagonal_main'] = self.compute_symmetry_score_detailed(grid, 'diagonal_main')
            
            # Symétrie diagonale anti-diagonale
            rotated = np.rot90(grid, 2)
            symmetries['diagonal_anti'] = np.array_equal(grid, rotated.T)
            symmetries['scores']['diagonal_anti'] = self.compute_symmetry_score_detailed(grid, 'diagonal_anti')
        
        # Symétrie rotationnelle
        symmetries['rotational'] = self.check_rotational_symmetry(grid)
        symmetries['scores']['rotational'] = self.compute_rotational_symmetry_score(grid)
        
        return symmetries
    
    def compute_symmetry_score_detailed(self, grid: np.ndarray, symmetry_type: str) -> float:
        """Calcule le score de symétrie avec tolérance"""
        h, w = grid.shape
        
        if symmetry_type == 'horizontal':
            if h % 2 != 0:
                return 0.0
            top = grid[:h//2, :]
            bottom = np.flipud(grid[h//2:, :])
            return np.mean(top == bottom)
        
        elif symmetry_type == 'vertical':
            if w % 2 != 0:
                return 0.0
            left = grid[:, :w//2]
            right = np.fliplr(grid[:, w//2:])
            return np.mean(left == right)
        
        elif symmetry_type == 'diagonal_main':
            if h != w:
                return 0.0
            return np.mean(grid == grid.T)
        
        elif symmetry_type == 'diagonal_anti':
            if h != w:
                return 0.0
            rotated = np.rot90(grid, 2)
            return np.mean(grid == rotated.T)
        
        return 0.0
    
    def check_rotational_symmetry(self, grid: np.ndarray) -> bool:
        """Vérifie la symétrie rotationnelle de 90 degrés"""
        for k in range(1, 4):
            rotated = np.rot90(grid, k)
            if not np.array_equal(grid, rotated):
                return False
        return True
    
    def compute_rotational_symmetry_score(self, grid: np.ndarray) -> float:
        """Calcule le score de symétrie rotationnelle"""
        scores = []
        for k in range(1, 4):
            rotated = np.rot90(grid, k)
            scores.append(np.mean(grid == rotated))
        return np.mean(scores)
    
    def compare_symmetries_detailed(self, sym1: Dict[str, Any], sym2: Dict[str, Any]) -> Dict[str, Any]:
        """Compare deux ensembles de symétries de manière détaillée"""
        comparison = {
            'preserved': {},
            'changed': {},
            'scores': {}
        }
        
        for key in ['horizontal', 'vertical', 'diagonal_main', 'diagonal_anti', 'rotational']:
            comparison['preserved'][key] = sym1.get(key, False) == sym2.get(key, False)
            comparison['changed'][key] = sym1.get(key, False) != sym2.get(key, False)
            comparison['scores'][key] = {
                'input': sym1.get('scores', {}).get(key, 0),
                'output': sym2.get('scores', {}).get(key, 0),
                'difference': abs(sym1.get('scores', {}).get(key, 0) - sym2.get('scores', {}).get(key, 0))
            }
        
        return comparison
    
    def compute_symmetry_statistics(self, symmetries: Dict[str, Any]) -> Dict[str, Any]:
        """Calcule des statistiques sur les symétries"""
        stats = {
            'input': {
                'count': 0,
                'types': [],
                'scores': {}
            },
            'output': {
                'count': 0,
                'types': [],
                'scores': {}
            },
            'preservation': {
                'rate': 0,
                'changes': []
            }
        }
        
        # Compter les symétries d'entrée
        for sym in symmetries['input_symmetries']:
            for key, value in sym.items():
                if key != 'scores' and value:
                    stats['input']['count'] += 1
                    stats['input']['types'].append(key)
        
        # Compter les symétries de sortie
        for sym in symmetries['output_symmetries']:
            for key, value in sym.items():
                if key != 'scores' and value:
                    stats['output']['count'] += 1
                    stats['output']['types'].append(key)
        
        # Calculer le taux de préservation
        total_comparisons = len(symmetries['symmetry_preservation'])
        preserved = 0
        for comp in symmetries['symmetry_preservation']:
            if all(comp['preserved'].values()):
                preserved += 1
        
        stats['preservation']['rate'] = preserved / total_comparisons if total_comparisons > 0 else 0
        
        return stats
    
    def analyze_transformations_detailed(self, puzzle: Dict) -> Dict[str, Any]:
        """Analyse détaillée des transformations"""
        transformations = {
            'geometric': self.infer_geometric_transformations_detailed(puzzle),
            'color': self.infer_color_transformations_detailed(puzzle),
            'structural': self.infer_structural_transformations_detailed(puzzle),
            'object_level': self.analyze_object_level_transformations(puzzle),
            'confidence_scores': self.compute_transformation_confidence(puzzle)
        }
        return transformations
    
    def infer_geometric_transformations_detailed(self, puzzle: Dict) -> Dict[str, Any]:
        """Infère les transformations géométriques avec détails"""
        transformations = {
            'types': [],
            'confidence': {},
            'examples': []
        }
        
        patterns = self.detect_patterns_detailed(puzzle)
        
        if patterns['repetition']['detected']:
            transformations['types'].append('repetition')
            transformations['confidence']['repetition'] = patterns['repetition']['confidence']
        
        if patterns['scaling']['detected']:
            transformations['types'].append('scaling')
            transformations['confidence']['scaling'] = patterns['scaling']['confidence']
        
        if patterns['rotation']['detected']:
            transformations['types'].append('rotation')
            transformations['confidence']['rotation'] = patterns['rotation']['confidence']
        
        if patterns['reflection']['detected']:
            transformations['types'].append('reflection')
            transformations['confidence']['reflection'] = patterns['reflection']['confidence']
        
        if patterns['translation']['detected']:
            transformations['types'].append('translation')
            transformations['confidence']['translation'] = patterns['translation']['confidence']
        
        return transformations
    
    def infer_color_transformations_detailed(self, puzzle: Dict) -> Dict[str, Any]:
        """Infère les transformations de couleur avec détails"""
        transformations = {
            'types': [],
            'mapping': {},
            'confidence': {},
            'examples': []
        }
        
        color_changes = []
        for example in puzzle['train']:
            input_grid = np.array(example['input'])
            output_grid = np.array(example['output'])
            
            input_colors = set(input_grid.flatten())
            output_colors = set(output_grid.flatten())
            
            if input_colors != output_colors:
                if len(output_colors) > len(input_colors):
                    color_changes.append('color_addition')
                elif len(output_colors) < len(input_colors):
                    color_changes.append('color_removal')
                else:
                    color_changes.append('color_substitution')
        
        transformations['types'] = list(set(color_changes))
        
        return transformations
    
    def infer_structural_transformations_detailed(self, puzzle: Dict) -> Dict[str, Any]:
        """Infère les transformations structurelles avec détails"""
        transformations = {
            'types': [],
            'confidence': {},
            'examples': []
        }
        
        grid_info = self.analyze_grid_structure(puzzle)
        
        if grid_info['consistent_dimension_change']:
            if grid_info['most_common_change'] == (0, 0):
                transformations['types'].append('in_place_transformation')
            elif all(c >= 0 for c in grid_info['most_common_change']):
                transformations['types'].append('expansion')
            else:
                transformations['types'].append('contraction')
        
        return transformations
    
    def analyze_object_level_transformations(self, puzzle: Dict) -> Dict[str, Any]:
        """Analyse les transformations au niveau des objets"""
        transformations = {
            'object_count_changes': [],
            'position_changes': [],
            'size_changes': [],
            'shape_changes': [],
            'color_changes': []
        }
        
        for example in puzzle['train']:
            input_grid = np.array(example['input'])
            output_grid = np.array(example['output'])
            
            input_objects = self.extract_objects_detailed(input_grid)
            output_objects = self.extract_objects_detailed(output_grid)
            
            transformations['object_count_changes'].append(len(output_objects) - len(input_objects))
            
            # Analyse des transformations d'objets
            obj_transforms = self.analyze_object_transformation_detailed(input_objects, output_objects)
            transformations['position_changes'].extend(obj_transforms['position_changes']['translations'])
            transformations['size_changes'].extend(obj_transforms['size_changes']['size_ratios'])
        
        return transformations
    
    def compute_transformation_confidence(self, puzzle: Dict) -> Dict[str, float]:
        """Calcule la confiance dans les transformations détectées"""
        confidence = {
            'geometric': 0.0,
            'color': 0.0,
            'structural': 0.0,
            'overall': 0.0
        }
        
        # Basé sur la cohérence entre les exemples
        patterns = self.detect_patterns_detailed(puzzle)
        
        # Confiance géométrique
        geo_confidence = 0
        for pattern_type in ['repetition', 'scaling', 'rotation', 'reflection', 'translation']:
            if patterns[pattern_type]['detected']:
                geo_confidence += patterns[pattern_type].get('confidence', 0)
        confidence['geometric'] = geo_confidence / 5
        
        # Confiance structurelle
        grid_info = self.analyze_grid_structure(puzzle)
        confidence['structural'] = 1.0 if grid_info['consistent_dimension_change'] else 0.5
        
        # Confiance globale
        confidence['overall'] = (confidence['geometric'] + confidence['color'] + confidence['structural']) / 3
        
        return confidence
    
    def measure_complexity_detailed(self, puzzle: Dict) -> Dict[str, float]:
        """Mesure détaillée de la complexité"""
        complexity = {
            'grid_size_complexity': 0,
            'color_complexity': 0,
            'object_complexity': 0,
            'transformation_complexity': 0,
            'spatial_complexity': 0,
            'overall_complex