#!/usr/bin/env python3
"""
Script pour utiliser l'ARCAnalyzer sur les puzzles ARC réels
"""

import sys
import json
import argparse
import numpy as np
from pathlib import Path
from typing import Optional, Dict, List, Any
from analyze_grid.arc_analyzer import ARCAnaly<PERSON>

def analyze_arc_task(task_file: str, output_file: Optional[str] = None, verbose: bool = True):
    """
    Analyse une tâche ARC spécifique avec l'ARCAnalyzer
    
    Args:
        task_file: Chemin vers le fichier JSON de la tâche
        output_file: Fichier de sortie pour l'analyse (optionnel)
        verbose: Affichage détaillé
    """
    
    # Charger le puzzle
    try:
        with open(task_file, 'r', encoding='utf-8') as f:
            puzzle_data = json.load(f)
    except Exception as e:
        print(f"❌ Erreur lecture {task_file}: {e}")
        return None
    
    # Analyser avec ARCAnalyzer
    analyzer = ARCAnalyzer()
    
    if verbose:
        print(f"🔍 Analyse de {Path(task_file).stem}...")
    
    try:
        analysis = analyzer.analyze_puzzle(puzzle_data)
    except Exception as e:
        print(f"❌ Erreur analyse: {e}")
        return None
    
    # Générer le rapport
    report = analyzer.generate_report(analysis)
    
    if verbose:
        print("\n" + "="*50)
        print(f"📋 ANALYSE DE {Path(task_file).stem.upper()}")
        print("="*50)
        print(report)
    
    # Sauvegarder si demandé
    if output_file:
        try:
            # Préparer les données pour JSON (conversion des types numpy)
            json_safe_analysis = convert_numpy_types(analysis)
            
            output_data = {
                'task_id': Path(task_file).stem,
                'task_file': str(task_file),
                'analysis': json_safe_analysis,
                'report': report
            }
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(output_data, f, indent=2, ensure_ascii=False)
            
            if verbose:
                print(f"\n💾 Analyse sauvegardée: {output_file}")
                
        except Exception as e:
            print(f"❌ Erreur sauvegarde: {e}")
    
    return analysis

def convert_numpy_types(obj):
    """Convertit les types numpy et autres en types Python standard pour JSON"""
    import numpy as np
    
    if isinstance(obj, dict):
        # Convertir aussi les clés
        new_dict = {}
        for key, value in obj.items():
            # Convertir la clé
            if isinstance(key, np.integer):
                key = int(key)
            elif isinstance(key, np.floating):
                key = float(key)
            elif isinstance(key, np.bool_):
                key = bool(key)
            
            new_dict[key] = convert_numpy_types(value)
        return new_dict
    elif isinstance(obj, list):
        return [convert_numpy_types(item) for item in obj]
    elif isinstance(obj, tuple):
        return tuple(convert_numpy_types(item) for item in obj)
    elif isinstance(obj, set):
        return list(obj)  # Convertir les sets en listes
    elif isinstance(obj, np.ndarray):
        return obj.tolist()
    elif isinstance(obj, np.integer):
        return int(obj)
    elif isinstance(obj, np.floating):
        return float(obj)
    elif isinstance(obj, np.bool_):
        return bool(obj)
    else:
        return obj

def analyze_multiple_tasks(data_dir: str, max_tasks: Optional[int] = None, output_dir: Optional[str] = None):
    """
    Analyse plusieurs tâches ARC
    
    Args:
        data_dir: Répertoire contenant les fichiers JSON
        max_tasks: Nombre maximum de tâches à analyser
        output_dir: Répertoire de sortie pour les analyses
    """
    
    data_path = Path(data_dir)
    if not data_path.exists():
        print(f"❌ Répertoire non trouvé: {data_dir}")
        return
    
    # Trouver les fichiers JSON de tâches ARC (sans underscore)
    all_json_files = list(data_path.glob("*.json"))
    task_files = [f for f in all_json_files if '_' not in f.stem]

    if not task_files:
        print(f"❌ Aucun fichier de tâche ARC dans: {data_dir}")
        return
    
    if max_tasks:
        task_files = task_files[:max_tasks]
    
    print(f"🎯 Analyse de {len(task_files)} tâches ARC")
    print("-" * 50)
    
    results = []
    
    for i, task_file in enumerate(task_files, 1):
        print(f"\n[{i}/{len(task_files)}] {task_file.stem}")
        
        # Définir le fichier de sortie
        if output_dir:
            output_path = Path(output_dir)
            output_path.mkdir(exist_ok=True)
            output_file = output_path / f"{task_file.stem}_arc_analysis.json"
        else:
            output_file = task_file.parent / f"{task_file.stem}_arc_analysis.json"
        
        # Analyser
        analysis = analyze_arc_task(str(task_file), str(output_file), verbose=False)
        
        if analysis:
            results.append({
                'task_id': task_file.stem,
                'complexity': analysis['complexity']['overall_complexity'],
                'patterns': analysis['patterns'],
                'transformations': analysis['transformations']
            })
            print(f"  ✅ Complexité: {analysis['complexity']['overall_complexity']:.1f}")
        else:
            print(f"  ❌ Échec")
    
    # Résumé global
    print(f"\n📊 RÉSUMÉ GLOBAL")
    print("-" * 30)
    print(f"Tâches analysées: {len(results)}")
    
    if results:
        avg_complexity = sum(r['complexity'] for r in results) / len(results)
        print(f"Complexité moyenne: {avg_complexity:.1f}")
        
        # Patterns les plus fréquents
        pattern_counts = {}
        for result in results:
            for pattern_type, pattern_info in result['patterns'].items():
                if isinstance(pattern_info, dict) and pattern_info.get('detected'):
                    pattern_counts[pattern_type] = pattern_counts.get(pattern_type, 0) + 1
        
        if pattern_counts:
            print("Patterns les plus fréquents:")
            for pattern, count in sorted(pattern_counts.items(), key=lambda x: x[1], reverse=True):
                print(f"  - {pattern}: {count}")

def main():
    parser = argparse.ArgumentParser(
        description="Analyseur ARC avancé",
        epilog="""
Exemples d'utilisation:
  python run_arc_analyzer.py --taskId 007bbfb7
  python run_arc_analyzer.py --task ../arcdata/training/007bbfb7.json
  python run_arc_analyzer.py --max-tasks 10 --output-dir analyses_arc
  
Comportement par défaut:
  - Répertoire de données: ../arcdata/training
  - Fichiers de sortie: à côté des tâches (sans --output-dir)
        """,
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    parser.add_argument(
        '--task', 
        type=str,
        help='Fichier JSON d\'une tâche spécifique'
    )
    
    parser.add_argument(
        '--taskId',
        type=str,
        help='ID d\'une tâche spécifique (ex: 007bbfb7)'
    )
    
    parser.add_argument(
        '--data-dir',
        type=str,
        default='../arcdata/training',
        help='Répertoire des tâches ARC (défaut: ../arcdata/training)'
    )
    
    parser.add_argument(
        '--max-tasks',
        type=int,
        help='Nombre maximum de tâches à analyser'
    )
    
    parser.add_argument(
        '--output-dir',
        type=str,
        help='Répertoire de sortie pour les analyses (défaut: à côté des tâches)'
    )
    
    parser.add_argument(
        '--quiet',
        action='store_true',
        help='Mode silencieux'
    )
    
    args = parser.parse_args()
    
    if args.task or args.taskId:
        # Analyse d'une tâche spécifique
        if args.taskId:
            # Construire le chemin depuis l'ID
            task_file = Path(args.data_dir) / f"{args.taskId}.json"
            if not task_file.exists():
                print(f"❌ Tâche non trouvée: {task_file}")
                return
            task_file = str(task_file)
        else:
            task_file = args.task
        
        # Définir le fichier de sortie
        output_file = None
        if args.output_dir:
            output_path = Path(args.output_dir)
            output_path.mkdir(exist_ok=True)
            task_name = Path(task_file).stem
            output_file = output_path / f"{task_name}_arc_analysis.json"
        else:
            # Par défaut : à côté du fichier de tâche
            task_path = Path(task_file)
            output_file = task_path.parent / f"{task_path.stem}_arc_analysis.json"
        
        analyze_arc_task(task_file, str(output_file), not args.quiet)
    
    else:
        # Analyse multiple
        analyze_multiple_tasks(args.data_dir, args.max_tasks, args.output_dir)

if __name__ == "__main__":
    main()