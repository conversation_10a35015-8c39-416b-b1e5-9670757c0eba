#!/usr/bin/env python3
"""
Analyseur de centres et zones uniformes pour les mosaïques confirmées
Donne les coordonnées précises pour analyse manuelle
"""

import numpy as np
import json
from pathlib import Path
from collections import Counter

class MosaicCenterAnalyzer:
    """
    Analyseur spécialisé pour identifier les centres et zones uniformes des mosaïques
    """
    
    def __init__(self):
        self.empty_value = 0
        self.min_uniform_size = 4  # Minimum 2x2
    
    def analyze_all_confirmed_mosaics(self):
        """
        Analyse toutes les tâches confirmées comme mosaïques
        """
        # Tâches confirmées par le StrictMosaicDetector
        confirmed_mosaics = [
            "0dfd9992",  # <PERSON><PERSON>ï<PERSON> (0.92)
            "29ec7d0e",  # <PERSON><PERSON>ï<PERSON> (0.92) 
            "484b58aa",  # <PERSON><PERSON><PERSON><PERSON> (0.92)
            "b8825c91",  # <PERSON>saïque (1.03)
            "c3f564a4",  # <PERSON><PERSON><PERSON><PERSON> (0.92)
            "3631a71a"   # Cas de référence (symétrie diagonale confirmée)
        ]
        
        print("=" * 100)
        print("ANALYSE DES CENTRES ET ZONES UNIFORMES - MOSAÏQUES CONFIRMÉES")
        print("=" * 100)
        
        all_results = {}
        
        for task_id in confirmed_mosaics:
            print(f"\n{'='*80}")
            print(f"🎯 TÂCHE {task_id}")
            print(f"{'='*80}")
            
            try:
                results = self.analyze_task_centers_and_zones(task_id)
                all_results[task_id] = results
                self.display_task_analysis(task_id, results)
                
            except Exception as e:
                print(f"❌ Erreur pour {task_id}: {e}")
                all_results[task_id] = {'error': str(e)}
        
        # Générer le rapport de synthèse
        self.generate_centers_summary(all_results)
        
        return all_results
    
    def analyze_task_centers_and_zones(self, task_id: str) -> dict:
        """
        Analyse une tâche spécifique pour identifier centres et zones uniformes
        """
        # Charger la tâche
        task_path = Path(f"../arcdata/training/{task_id}.json")
        with open(task_path, 'r') as f:
            task_data = json.load(f)
        
        results = {
            'task_id': task_id,
            'examples_analysis': [],
            'center_consistency': {},
            'uniform_zones_summary': {}
        }
        
        # Analyser chaque exemple
        for i, example in enumerate(task_data['train']):
            input_grid = np.array(example['input'])
            output_grid = np.array(example['output'])
            
            example_analysis = self.analyze_example_centers_zones(input_grid, output_grid, i)
            results['examples_analysis'].append(example_analysis)
        
        # Analyser la cohérence des centres
        results['center_consistency'] = self.analyze_center_consistency(results['examples_analysis'])
        
        # Résumer les zones uniformes
        results['uniform_zones_summary'] = self.summarize_uniform_zones(results['examples_analysis'])
        
        return results
    
    def analyze_example_centers_zones(self, input_grid: np.ndarray, output_grid: np.ndarray, example_idx: int) -> dict:
        """
        Analyse un exemple pour identifier centre et zones uniformes
        """
        h, w = input_grid.shape
        
        analysis = {
            'example_idx': example_idx,
            'grid_dimensions': (h, w),
            'geometric_center': (h // 2, w // 2),
            'visual_center': None,
            'input_uniform_zones': [],
            'output_uniform_zones': [],
            'center_region_analysis': {},
            'color_distribution': {}
        }
        
        # 1. Centre géométrique
        geometric_center = (h // 2, w // 2)
        analysis['geometric_center'] = geometric_center
        
        # 2. Centre visuel (basé sur la distribution des couleurs)
        visual_center = self.find_visual_center(input_grid)
        analysis['visual_center'] = visual_center
        
        # 3. Analyse de la région centrale
        center_analysis = self.analyze_center_region(input_grid, geometric_center)
        analysis['center_region_analysis'] = center_analysis
        
        # 4. Zones uniformes dans l'input
        input_uniform_zones = self.find_uniform_zones(input_grid)
        analysis['input_uniform_zones'] = input_uniform_zones
        
        # 5. Zones uniformes dans l'output
        output_uniform_zones = self.find_uniform_zones(output_grid)
        analysis['output_uniform_zones'] = output_uniform_zones
        
        # 6. Distribution des couleurs
        color_dist = self.analyze_color_distribution(input_grid, output_grid)
        analysis['color_distribution'] = color_dist
        
        return analysis
    
    def find_visual_center(self, grid: np.ndarray) -> tuple:
        """
        Trouve le centre visuel basé sur la distribution des couleurs non-vides
        """
        h, w = grid.shape
        
        # Trouver toutes les cellules non-vides
        non_empty_positions = np.where(grid != self.empty_value)
        
        if len(non_empty_positions[0]) == 0:
            return (h // 2, w // 2)  # Fallback au centre géométrique
        
        # Calculer le centroïde des positions non-vides
        center_r = int(np.mean(non_empty_positions[0]))
        center_c = int(np.mean(non_empty_positions[1]))
        
        return (center_r, center_c)
    
    def analyze_center_region(self, grid: np.ndarray, center: tuple) -> dict:
        """
        Analyse la région autour du centre
        """
        h, w = grid.shape
        center_r, center_c = center
        
        # Définir différentes tailles de région centrale
        regions = {}
        
        for size in [3, 5, 7]:  # Régions 3x3, 5x5, 7x7
            margin = size // 2
            r1 = max(0, center_r - margin)
            r2 = min(h, center_r + margin + 1)
            c1 = max(0, center_c - margin)
            c2 = min(w, center_c + margin + 1)
            
            if r2 > r1 and c2 > c1:
                region = grid[r1:r2, c1:c2]
                regions[f'{size}x{size}'] = {
                    'coords': (r1, c1, r2, c2),
                    'colors': list(set(region.flatten())),
                    'dominant_color': Counter(region.flatten()).most_common(1)[0][0],
                    'empty_ratio': np.sum(region == self.empty_value) / region.size
                }
        
        return regions
    
    def find_uniform_zones(self, grid: np.ndarray) -> list:
        """
        Trouve toutes les zones de couleur uniforme significatives
        """
        h, w = grid.shape
        uniform_zones = []
        visited = np.zeros_like(grid, dtype=bool)
        
        # Parcourir toute la grille
        for i in range(h):
            for j in range(w):
                if not visited[i, j] and grid[i, j] != self.empty_value:
                    zone = self.extract_uniform_zone(grid, i, j, visited)
                    if zone and zone['area'] >= self.min_uniform_size:
                        uniform_zones.append(zone)
        
        # Trier par taille décroissante
        uniform_zones.sort(key=lambda x: x['area'], reverse=True)
        
        return uniform_zones
    
    def extract_uniform_zone(self, grid: np.ndarray, start_i: int, start_j: int, visited: np.ndarray) -> dict:
        """
        Extrait une zone uniforme par flood fill
        """
        h, w = grid.shape
        color = grid[start_i, start_j]
        
        if color == self.empty_value:
            return None
        
        # Flood fill pour trouver toute la zone de cette couleur
        pixels = []
        stack = [(start_i, start_j)]
        
        while stack:
            i, j = stack.pop()
            if (i < 0 or i >= h or j < 0 or j >= w or 
                visited[i, j] or grid[i, j] != color):
                continue
            
            visited[i, j] = True
            pixels.append((i, j))
            
            # Ajouter les voisins (connexité 4)
            for di, dj in [(-1, 0), (1, 0), (0, -1), (0, 1)]:
                stack.append((i + di, j + dj))
        
        if not pixels:
            return None
        
        # Calculer les propriétés de la zone
        pixels = np.array(pixels)
        min_r, max_r = pixels[:, 0].min(), pixels[:, 0].max()
        min_c, max_c = pixels[:, 1].min(), pixels[:, 1].max()
        
        # Calculer la rectangularité (ratio pixels / rectangle englobant)
        bounding_area = (max_r - min_r + 1) * (max_c - min_c + 1)
        rectangularity = len(pixels) / bounding_area if bounding_area > 0 else 0
        
        return {
            'color': int(color),
            'pixels': pixels.tolist(),
            'area': len(pixels),
            'bounding_box': (min_r, min_c, max_r + 1, max_c + 1),
            'center': (int(pixels[:, 0].mean()), int(pixels[:, 1].mean())),
            'rectangularity': rectangularity,
            'is_rectangular': rectangularity > 0.8
        }
    
    def analyze_color_distribution(self, input_grid: np.ndarray, output_grid: np.ndarray) -> dict:
        """
        Analyse la distribution des couleurs
        """
        input_colors = Counter(input_grid.flatten())
        output_colors = Counter(output_grid.flatten())
        
        return {
            'input_colors': dict(input_colors),
            'output_colors': dict(output_colors),
            'colors_removed': list(set(input_colors.keys()) - set(output_colors.keys())),
            'colors_added': list(set(output_colors.keys()) - set(input_colors.keys())),
            'dominant_input_color': input_colors.most_common(1)[0][0],
            'dominant_output_color': output_colors.most_common(1)[0][0]
        }
    
    def analyze_center_consistency(self, examples_analysis: list) -> dict:
        """
        Analyse la cohérence des centres entre exemples
        """
        if not examples_analysis:
            return {}
        
        geometric_centers = [ex['geometric_center'] for ex in examples_analysis]
        visual_centers = [ex['visual_center'] for ex in examples_analysis]
        
        # Vérifier si tous les centres géométriques sont identiques
        unique_geometric = list(set(geometric_centers))
        geometric_consistent = len(unique_geometric) == 1
        
        # Calculer la variance des centres visuels
        if visual_centers:
            visual_r_coords = [c[0] for c in visual_centers]
            visual_c_coords = [c[1] for c in visual_centers]
            visual_variance = (np.var(visual_r_coords) + np.var(visual_c_coords)) / 2
        else:
            visual_variance = 0
        
        return {
            'geometric_centers': geometric_centers,
            'geometric_consistent': geometric_consistent,
            'common_geometric_center': unique_geometric[0] if geometric_consistent else None,
            'visual_centers': visual_centers,
            'visual_variance': visual_variance,
            'visual_consistent': visual_variance < 2.0  # Seuil arbitraire
        }
    
    def summarize_uniform_zones(self, examples_analysis: list) -> dict:
        """
        Résume les zones uniformes trouvées
        """
        all_zones = []
        color_zone_counts = Counter()
        
        for example in examples_analysis:
            input_zones = example['input_uniform_zones']
            all_zones.extend(input_zones)
            
            for zone in input_zones:
                color_zone_counts[zone['color']] += 1
        
        # Statistiques
        if all_zones:
            areas = [zone['area'] for zone in all_zones]
            rectangularities = [zone['rectangularity'] for zone in all_zones]
            
            summary = {
                'total_zones': len(all_zones),
                'colors_with_zones': dict(color_zone_counts),
                'avg_area': np.mean(areas),
                'max_area': max(areas),
                'min_area': min(areas),
                'avg_rectangularity': np.mean(rectangularities),
                'rectangular_zones': len([z for z in all_zones if z['is_rectangular']]),
                'largest_zones': sorted(all_zones, key=lambda x: x['area'], reverse=True)[:5]
            }
        else:
            summary = {'total_zones': 0}
        
        return summary
    
    def display_task_analysis(self, task_id: str, results: dict):
        """
        Affiche l'analyse d'une tâche de manière lisible
        """
        print(f"\n📊 RÉSUMÉ POUR {task_id}:")
        print("-" * 50)
        
        # Cohérence des centres
        center_consistency = results['center_consistency']
        if center_consistency.get('geometric_consistent'):
            center = center_consistency['common_geometric_center']
            print(f"✅ Centre géométrique cohérent: {center}")
        else:
            centers = center_consistency.get('geometric_centers', [])
            print(f"❌ Centres géométriques variables: {centers}")
        
        if center_consistency.get('visual_consistent'):
            print(f"✅ Centres visuels cohérents (variance: {center_consistency['visual_variance']:.2f})")
        else:
            print(f"❌ Centres visuels variables (variance: {center_consistency['visual_variance']:.2f})")
        
        # Zones uniformes
        zones_summary = results['uniform_zones_summary']
        if zones_summary.get('total_zones', 0) > 0:
            print(f"\n🎨 ZONES UNIFORMES:")
            print(f"   • Total zones détectées: {zones_summary['total_zones']}")
            print(f"   • Zones rectangulaires: {zones_summary.get('rectangular_zones', 0)}")
            print(f"   • Aire moyenne: {zones_summary.get('avg_area', 0):.1f}")
            print(f"   • Couleurs avec zones: {zones_summary.get('colors_with_zones', {})}")
            
            # Top 3 plus grandes zones
            largest = zones_summary.get('largest_zones', [])[:3]
            if largest:
                print(f"   • Top 3 plus grandes zones:")
                for i, zone in enumerate(largest, 1):
                    bbox = zone['bounding_box']
                    print(f"     {i}. Couleur {zone['color']}: [{bbox[0]},{bbox[1]} {bbox[2]-1},{bbox[3]-1}] (aire: {zone['area']})")
        
        # Détails par exemple
        print(f"\n📋 DÉTAILS PAR EXEMPLE:")
        for example in results['examples_analysis']:
            idx = example['example_idx']
            dims = example['grid_dimensions']
            geo_center = example['geometric_center']
            vis_center = example['visual_center']
            input_zones = len(example['input_uniform_zones'])
            
            print(f"   Exemple {idx+1}: {dims}, Centre géo: {geo_center}, Centre vis: {vis_center}, {input_zones} zones")
            
            # Région centrale
            center_regions = example['center_region_analysis']
            if '5x5' in center_regions:
                region_5x5 = center_regions['5x5']
                coords = region_5x5['coords']
                dominant = region_5x5['dominant_color']
                print(f"      Région 5x5: [{coords[0]},{coords[1]} {coords[2]-1},{coords[3]-1}], couleur dominante: {dominant}")
    
    def generate_centers_summary(self, all_results: dict):
        """
        Génère un résumé global des centres et zones
        """
        print(f"\n{'='*100}")
        print("RÉSUMÉ GLOBAL - CENTRES ET ZONES UNIFORMES")
        print("=" * 100)
        
        successful_tasks = {k: v for k, v in all_results.items() if 'error' not in v}
        
        print(f"\n📊 STATISTIQUES GLOBALES:")
        print(f"   • Tâches analysées: {len(successful_tasks)}")
        
        # Analyse des centres
        consistent_geometric = 0
        consistent_visual = 0
        all_centers = []
        
        for task_id, results in successful_tasks.items():
            center_consistency = results.get('center_consistency', {})
            if center_consistency.get('geometric_consistent'):
                consistent_geometric += 1
                center = center_consistency['common_geometric_center']
                all_centers.append((task_id, center))
            
            if center_consistency.get('visual_consistent'):
                consistent_visual += 1
        
        print(f"\n🎯 COHÉRENCE DES CENTRES:")
        print(f"   • Centres géométriques cohérents: {consistent_geometric}/{len(successful_tasks)}")
        print(f"   • Centres visuels cohérents: {consistent_visual}/{len(successful_tasks)}")
        
        if all_centers:
            print(f"\n📍 CENTRES GÉOMÉTRIQUES DÉTECTÉS:")
            for task_id, center in all_centers:
                print(f"   • {task_id}: {center}")
        
        # Analyse des zones uniformes
        total_zones = 0
        total_rectangular = 0
        
        print(f"\n🎨 ZONES UNIFORMES:")
        for task_id, results in successful_tasks.items():
            zones_summary = results.get('uniform_zones_summary', {})
            task_zones = zones_summary.get('total_zones', 0)
            task_rectangular = zones_summary.get('rectangular_zones', 0)
            
            total_zones += task_zones
            total_rectangular += task_rectangular
            
            if task_zones > 0:
                print(f"   • {task_id}: {task_zones} zones ({task_rectangular} rectangulaires)")
        
        print(f"\n📈 TOTAUX:")
        print(f"   • Total zones uniformes: {total_zones}")
        print(f"   • Total zones rectangulaires: {total_rectangular}")
        if total_zones > 0:
            print(f"   • Ratio rectangulaire: {total_rectangular/total_zones:.1%}")

def main():
    """
    Fonction principale
    """
    analyzer = MosaicCenterAnalyzer()
    results = analyzer.analyze_all_confirmed_mosaics()
    
    print(f"\n✅ Analyse terminée!")
    print(f"📋 Résultats disponibles pour {len(results)} tâches")

if __name__ == "__main__":
    main()