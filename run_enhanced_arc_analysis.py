#!/usr/bin/env python3
"""
Script principal pour lancer l'analyse améliorée des puzzles ARC
avec l'ARCAnalyzer intégrant les améliorations NumPy et scikit-image.
"""

import argparse
import sys
import os
from pathlib import Path

def main():
    """Fonction principale pour lancer l'analyse améliorée."""
    
    parser = argparse.ArgumentParser(
        description="Analyse améliorée des puzzles ARC avec ARCAnalyzer 2.0",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Exemples d'utilisation:

1. Analyser l'ensemble d'entraînement (échantillon):
   python run_enhanced_arc_analysis.py --mode training --max-puzzles 20

2. Analyser l'ensemble d'évaluation complet:
   python run_enhanced_arc_analysis.py --mode evaluation

3. Analyser un puzzle spécifique:
   python run_enhanced_arc_analysis.py --mode single --puzzle-id 007bbfb7

4. Test rapide sur quelques puzzles:
   python run_enhanced_arc_analysis.py --mode test --max-puzzles 5

Fonctionnalités de l'ARCAnalyzer 2.0:
- Analyse des différences (diff) entre input et output
- Extraction d'objets avec scikit-image regionprops
- Opérations NumPy vectorisées pour de meilleures performances
- Structure JSON améliorée avec métadonnées détaillées
- Analyse des transformations géométriques avancées
        """
    )
    
    parser.add_argument(
        "--mode", 
        choices=["training", "evaluation", "single", "test"],
        default="test",
        help="Mode d'analyse (défaut: test)"
    )
    
    parser.add_argument(
        "--max-puzzles", 
        type=int, 
        default=None,
        help="Nombre maximum de puzzles à analyser"
    )
    
    parser.add_argument(
        "--puzzle-id", 
        type=str,
        help="ID du puzzle spécifique à analyser (mode single)"
    )
    
    parser.add_argument(
        "--output-dir", 
        type=str,
        default=None,
        help="Répertoire de sortie personnalisé"
    )
    
    args = parser.parse_args()
    
    print("🔬 ARCAnalyzer 2.0 - Analyse Améliorée des Puzzles ARC")
    print("=" * 60)
    print("Fonctionnalités:")
    print("✓ Analyse des différences (diff) input/output")
    print("✓ Extraction d'objets avec scikit-image regionprops")
    print("✓ Opérations NumPy vectorisées")
    print("✓ Structure JSON améliorée")
    print("✓ Métriques de complexité avancées")
    print()
    
    if args.mode == "training":
        print("🎯 Mode: Analyse de l'ensemble d'entraînement")
        try:
            from test_analyzer_on_training import analyze_training_set
        except ImportError as e:
            print(f"❌ Erreur: test_analyzer_on_training introuvable - {e}")
            return 1
        
        max_puzzles = args.max_puzzles or 50
        print(f"Analyse de {max_puzzles} puzzles d'entraînement...")
        
        results = analyze_training_set(max_puzzles=max_puzzles)
        if results is None:
            print("❌ Erreur: Les résultats de l'analyse sont None")
            return 1
        
        print(f"\n✅ Analyse terminée!")
        successful = results.get('successful_analyses', 0) if isinstance(results, dict) else 0
        total = results.get('total_puzzles', 0) if isinstance(results, dict) else 0
        print(f"Taux de réussite: {successful}/{total}")
        
    elif args.mode == "evaluation":
        print("🎯 Mode: Analyse de l'ensemble d'évaluation")
        try:
            from evaluation.enhanced_arc_evaluation import EnhancedARCEvaluator
        except ImportError as e:
            print(f"❌ Erreur: enhanced_arc_evaluation introuvable - {e}")
            return 1
        
        eval_dir = args.output_dir or "arcdata/evaluation"
        evaluator = EnhancedARCEvaluator(eval_dir)
        
        print(f"Analyse de l'ensemble d'évaluation dans: {eval_dir}")
        results = evaluator.evaluate_dataset(args.max_puzzles)
        
        if results is None:
            print("❌ Erreur: Les résultats de l'évaluation sont None")
            return 1
        
        print(f"\n✅ Évaluation terminée!")
        successful_analyses = results.get('global_stats', {}).get('successful_analyses', 0) if isinstance(results, dict) else 0
        print(f"Analyses réussies: {successful_analyses}")
        print(f"Fichiers générés dans: {evaluator.analysis_dir}")
        
    elif args.mode == "single":
        if not args.puzzle_id:
            print("❌ Erreur: --puzzle-id requis pour le mode single")
            return 1
            
        print(f"🎯 Mode: Analyse du puzzle {args.puzzle_id}")
        try:
            from analyze_grid.arc_analyzer import ARCAnalyzer
        except ImportError as e:
            print(f"❌ Erreur: ARCAnalyzer introuvable - {e}")
            return 1
        
        import json
        
        # Chercher le puzzle dans training ou evaluation
        puzzle_file = None
        for data_dir in ["arcdata/training", "arcdata/evaluation"]:
            potential_file = Path(data_dir) / f"{args.puzzle_id}.json"
            if potential_file.exists():
                puzzle_file = potential_file
                break
        
        if not puzzle_file:
            print(f"❌ Puzzle {args.puzzle_id} non trouvé")
            return 1
        
        # Analyser le puzzle
        try:
            with open(puzzle_file, 'r') as f:
                puzzle_data = json.load(f)
        except Exception as e:
            print(f"❌ Erreur lors du chargement du puzzle: {e}")
            return 1
        
        try:
            analyzer = ARCAnalyzer()
            analysis = analyzer.analyze_puzzle(puzzle_data)
            enhanced_structure = analyzer.get_enhanced_analysis_structure(analysis)
            
            # Afficher le résumé
            print(f"Puzzle analysé: {args.puzzle_id}")
            print(f"Exemples d'entraînement: {enhanced_structure.get('metadata', {}).get('total_examples', 0)}")
            print(f"Type de transformation: {enhanced_structure.get('summary', {}).get('primary_transformation_type', 'inconnu')}")
            print(f"Score de confiance: {enhanced_structure.get('summary', {}).get('confidence_score', 0)}")
            
            complexity = enhanced_structure.get('enhanced_analysis', {}).get('complexity_metrics', {}).get('overall_complexity', 0)
            print(f"Complexité: {complexity:.1f}")
            
            # Sauvegarder l'analyse
            output_file = f"{args.puzzle_id}_enhanced_analysis.json"
            with open(output_file, 'w') as f:
                json.dump(enhanced_structure, f, indent=2)
            print(f"Analyse sauvegardée: {output_file}")
        except Exception as e:
            print(f"❌ Erreur lors de l'analyse: {e}")
            return 1
            
    elif args.mode == "test":
        print("🎯 Mode: Test rapide")
        try:
            from test_enhanced_analyzer import test_enhanced_analyzer
        except ImportError:
            print("❌ Erreur: test_enhanced_analyzer introuvable")
            return 1
        
        print("Test des nouvelles fonctionnalités...")
        success = test_enhanced_analyzer()
        
        if success:
            print("\n✅ Test réussi! L'ARCAnalyzer amélioré fonctionne correctement.")
            print("\n💡 Pour tester sur l'ensemble d'entraînement, utilisez:")
            print("   python run_enhanced_arc_analysis.py --mode training --max-puzzles 10")
        else:
            print("\n❌ Test échoué!")
            return 1
    
    print(f"\n🎉 Analyse terminée avec succès!")
    return 0

if __name__ == "__main__":
    sys.exit(main())
