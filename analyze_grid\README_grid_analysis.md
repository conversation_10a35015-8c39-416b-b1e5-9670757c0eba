# Analyseur de Grilles ARC - Version Pure

Analyse des transformations de grilles ARC avec des algorithmes classiques, sans prétentions d'IA.

## Fichiers Principaux

### Scripts d'Analyse
- **`grid_analysis_pure.py`** - Analyseur principal avec algorithmes classiques
- **`run_grid_analysis.py`** - Script de lancement avec options
- **`config_minimal.py`** - Configuration basique

### Scripts de Test
- **`test_pure_analysis.py`** - Tests avec grilles synthétiques
- **`test_grid_analysis.py`** - Tests complets

## Utilisation

### Analyse Rapide
```bash
python run_grid_analysis.py --max-puzzles 10
```

### Analyse Complète
```bash
python run_grid_analysis.py
```

### Options Disponibles
- `--data-dir` : Répertoire des fichiers JSON (défaut: ../arcdata/training)
- `--max-puzzles` : Nombre maximum de puzzles à analyser
- `--no-viz` : Pas de visualisations
- `--quiet` : Mode silencieux

## Fonctionnalités

### Analyses <PERSON> (Algorithmes Classiques)
- **Calcul de complexité** : Nombre de couleurs, transitions, régions connectées
- **Classification de transformations** : Multiplication, division, redimensionnement
- **Détection de patterns** : Réplication, remplissage, bordures, symétries
- **Analyse spatiale** : Densité, répétitions, symétries
- **Analyse cellulaire** : Changements cellule par cellule

### Insights Calculés (Pas d'IA)
- Expansion/contraction (calcul de ratios)
- Changements de complexité (algorithmes de graphes)
- Patterns (comparaisons directes)
- Préservation de structure (comptage de cellules)

### Mapping AGI
- Suggestions de commandes AGI basées sur les transformations détectées
- Paramètres calculés pour les commandes
- Niveau de confiance basé sur la consistance

## Fichiers Générés

### Rapports
- `grid_analysis_report.md` - Rapport complet en markdown
- `grid_analysis_data.csv` - Données pour analyse quantitative

### Visualisations
- `grid_analysis_dashboard.png` - Dashboard avec graphiques

## Dépendances

### Bibliothèques Python
- `numpy` - Calculs numériques
- `pandas` - Manipulation de données
- `matplotlib` - Visualisations
- `seaborn` - Graphiques statistiques

### Données
- Fichiers JSON des puzzles ARC dans le format standard

## Exemple d'Utilisation

```python
from grid_analysis_pure import PureGridAnalyzer

# Initialiser l'analyseur
analyzer = PureGridAnalyzer("../arcdata/training")

# Analyser une transformation
input_grid = [[1, 0], [0, 1]]
output_grid = [[1, 0, 1, 0], [0, 1, 0, 1], [1, 0, 1, 0], [0, 1, 0, 1]]

analysis = analyzer.analyze_transformation(input_grid, output_grid)
print(f"Type: {analysis['transformation_type']['type']}")
print(f"Ratio: {analysis['size_ratio']}")
```

## Différences avec la Version DINO

### Supprimé
- Encodeur DINO et embeddings
- Références à l'intelligence artificielle
- Clustering avec KMeans sur les embeddings
- Terminologie trompeuse ("détection automatique", "modèle comprend")

### Conservé et Clarifié
- Analyse factuelle des transformations
- Calculs algorithmiques explicites
- Métriques quantitatives transparentes
- Mapping vers commandes AGI basé sur des règles

## Honnêteté Technique

Ce script utilise uniquement des **algorithmes classiques** :
- Comparaisons arithmétiques
- Parcours de graphes
- Comptage et statistiques
- Comparaisons de patterns

Aucune "intelligence artificielle" n'est impliquée dans l'analyse. Tous les "insights" sont le résultat de calculs déterministes et de comparaisons directes.