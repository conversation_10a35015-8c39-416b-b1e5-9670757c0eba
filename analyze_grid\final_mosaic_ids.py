#!/usr/bin/env python3
"""
Version finale du détecteur MOSAIC - IDs seulement
"""

import json
import os
import numpy as np
from collections import Counter

def analyze_color_usage(grid: np.ndarray):
    """Analyse l'utilisation des couleurs"""
    total_cells = grid.size
    color_counts = Counter(grid.flatten())
    
    if 0 in color_counts:
        background_cells = color_counts[0]
        del color_counts[0]
    else:
        background_cells = 0
    
    non_background_cells = total_cells - background_cells
    
    if non_background_cells == 0:
        return {
            'usage_ratio': 0.0,
            'balance_score': 0.0,
            'dominant_ratio': 1.0,
            'unique_colors': 0
        }
    
    # Distribution des couleurs
    color_ratios = [count / non_background_cells for count in color_counts.values()]
    
    # Score d'équilibre (variance faible = plus équilibré)
    if len(color_ratios) > 1:
        variance = np.var(color_ratios)
        balance_score = 1.0 / (1.0 + variance * 10)
    else:
        balance_score = 0.0
    
    return {
        'usage_ratio': non_background_cells / total_cells,
        'balance_score': balance_score,
        'dominant_ratio': max(color_ratios) if color_ratios else 1.0,
        'unique_colors': len(color_counts)
    }

def is_true_mosaic(puzzle_data, puzzle_id):
    """Détermine si un puzzle est un vrai MOSAIC"""
    
    examples_metrics = []
    
    for example in puzzle_data['train']:
        input_grid = np.array(example['input'])
        h, w = input_grid.shape
        area = h * w
        
        color_analysis = analyze_color_usage(input_grid)
        
        examples_metrics.append({
            'area': area,
            'color_analysis': color_analysis
        })
    
    # Métriques globales
    max_area = max(ex['area'] for ex in examples_metrics)
    max_colors = max(ex['color_analysis']['unique_colors'] for ex in examples_metrics)
    avg_usage = np.mean([ex['color_analysis']['usage_ratio'] for ex in examples_metrics])
    avg_balance = np.mean([ex['color_analysis']['balance_score'] for ex in examples_metrics])
    avg_dominant = np.mean([ex['color_analysis']['dominant_ratio'] for ex in examples_metrics])
    
    # CRITÈRES STRICTS POUR MOSAIC
    criteria = {
        'large_size': max_area >= 144,  # Au moins 12x12
        'many_colors': max_colors >= 6,  # Au moins 6 couleurs
        'high_usage': avg_usage >= 0.6,  # 60%+ cellules colorées
        'balanced': avg_balance >= 0.6,  # Couleurs équilibrées
        'no_dominant': avg_dominant <= 0.5  # Pas de couleur > 50%
    }
    
    # Score basé sur les critères
    score = sum([
        0.2 if criteria['large_size'] else 0,
        0.2 if criteria['many_colors'] else 0,
        0.25 if criteria['high_usage'] else 0,
        0.25 if criteria['balanced'] else 0,
        0.1 if criteria['no_dominant'] else 0
    ])
    
    # Doit satisfaire au moins 4/5 critères ET score > 0.7
    criteria_count = sum(criteria.values())
    is_mosaic = criteria_count >= 4 and score >= 0.7
    
    return {
        'is_mosaic': is_mosaic,
        'score': score,
        'criteria_count': criteria_count,
        'metrics': {
            'max_area': max_area,
            'max_colors': max_colors,
            'avg_usage': avg_usage,
            'avg_balance': avg_balance,
            'avg_dominant': avg_dominant
        }
    }

def main():
    """Détection finale des puzzles MOSAIC"""
    
    print("🎨 DÉTECTION FINALE DES PUZZLES MOSAIC")
    print("=" * 50)
    print("Critères stricts appliqués:")
    print("1. Taille ≥ 144 cellules (12x12)")
    print("2. Couleurs ≥ 6")
    print("3. Usage ≥ 60% (cellules colorées)")
    print("4. Équilibre ≥ 0.6 (couleurs équilibrées)")
    print("5. Dominance ≤ 50% (pas de couleur dominante)")
    print("Seuil: 4/5 critères + score ≥ 0.7")
    print()
    
    data_dir = "../arcdata/training"
    true_mosaic_puzzles = []
    rejected_candidates = []
    total_puzzles = 0
    
    for filename in os.listdir(data_dir):
        if not filename.endswith('.json'):
            continue
            
        puzzle_id = filename.replace('.json', '')
        filepath = os.path.join(data_dir, filename)
        
        try:
            with open(filepath, 'r') as f:
                puzzle_data = json.load(f)
            
            result = is_true_mosaic(puzzle_data, puzzle_id)
            total_puzzles += 1
            
            if result['is_mosaic']:
                true_mosaic_puzzles.append({
                    'id': puzzle_id,
                    'score': result['score'],
                    'criteria': result['criteria_count'],
                    'metrics': result['metrics']
                })
                
                m = result['metrics']
                print(f"✅ {puzzle_id} (score: {result['score']:.3f}, {result['criteria_count']}/5)")
                print(f"   {m['max_area']} cellules, {m['max_colors']} couleurs")
                print(f"   Usage: {m['avg_usage']:.1%}, Balance: {m['avg_balance']:.2f}, Dominant: {m['avg_dominant']:.2f}")
                print()
            else:
                if result['score'] > 0.5:  # Candidats proches
                    rejected_candidates.append({
                        'id': puzzle_id,
                        'score': result['score'],
                        'criteria': result['criteria_count']
                    })
            
        except Exception as e:
            print(f"❌ Erreur avec {puzzle_id}: {e}")
            continue
    
    # Trier par score
    true_mosaic_puzzles.sort(key=lambda x: x['score'], reverse=True)
    rejected_candidates.sort(key=lambda x: x['score'], reverse=True)
    
    print(f"📊 RÉSULTATS FINAUX")
    print(f"Total puzzles analysés: {total_puzzles}")
    print(f"Vrais MOSAIC détectés: {len(true_mosaic_puzzles)}")
    print(f"Candidats rejetés: {len(rejected_candidates)}")
    print(f"Pourcentage MOSAIC: {len(true_mosaic_puzzles)/total_puzzles*100:.1f}%")
    
    # Afficher les rejetés notables
    print(f"\n🚫 CANDIDATS REJETÉS (top 5):")
    for rej in rejected_candidates[:5]:
        print(f"   {rej['id']} (score: {rej['score']:.3f}, {rej['criteria']}/5 critères)")
    
    # Sauvegarder les résultats
    mosaic_ids = [p['id'] for p in true_mosaic_puzzles]
    
    with open("final_mosaic_puzzle_ids.txt", "w") as f:
        f.write("# PUZZLES MOSAIC - DÉTECTION FINALE STRICTE\n")
        f.write(f"# Total: {len(mosaic_ids)} puzzles sur {total_puzzles} ({len(mosaic_ids)/total_puzzles*100:.1f}%)\n")
        f.write("# Critères: Taille + Couleurs + Usage + Équilibre + Non-dominance\n\n")
        
        for puzzle in true_mosaic_puzzles:
            m = puzzle['metrics']
            f.write(f"{puzzle['id']} # score:{puzzle['score']:.3f} ")
            f.write(f"{m['max_area']}cells {m['max_colors']}colors ")
            f.write(f"usage:{m['avg_usage']:.1%} balance:{m['avg_balance']:.2f}\n")
    
    print(f"\n💾 Liste finale sauvegardée: final_mosaic_puzzle_ids.txt")
    
    print(f"\n🎯 LISTE FINALE DES IDs MOSAIC:")
    print("-" * 40)
    for i, puzzle_id in enumerate(mosaic_ids, 1):
        print(f"{i:2d}. {puzzle_id}")
    
    # Comparaison avec la détection précédente
    print(f"\n📈 COMPARAISON AVEC DÉTECTION PRÉCÉDENTE:")
    print("Puzzles probablement exclus:")
    excluded_examples = ["6ecd11f4", "3631a71a", "91714a58"]
    for ex_id in excluded_examples:
        if ex_id not in mosaic_ids:
            print(f"✓ {ex_id} exclu (critères trop stricts)")
        else:
            print(f"✗ {ex_id} toujours inclus")

if __name__ == "__main__":
    main()