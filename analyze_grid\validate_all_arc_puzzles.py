#!/usr/bin/env python3
"""
Validation complète de la détection MOTIF sur tous les 401 puzzles ARC
"""

import json
import os
import glob
from typing import Dict, List
from analyze_grid.arc_analyzer import ARCAnalyzer
import time

def get_all_arc_puzzles() -> List[str]:
    """Récupère tous les puzzles ARC disponibles"""
    json_files = glob.glob("../arcdata/training/*.json")
    puzzle_ids = []
    
    for json_file in json_files:
        puzzle_id = os.path.basename(json_file).replace('.json', '')
        puzzle_ids.append(puzzle_id)
    
    return sorted(puzzle_ids)

def load_motif_puzzles() -> List[str]:
    """Charge la liste des puzzles MOTIF connus"""
    try:
        with open('motif_analysis_results.json', 'r') as f:
            data = json.load(f)
            return data['all_motif_puzzles']
    except FileNotFoundError:
        return []

def analyze_puzzle_motif(puzzle_id: str, known_motif_puzzles: List[str]) -> Dict:
    """Analyse un puzzle pour la détection MOTIF"""
    json_file = f"../arcdata/training/{puzzle_id}.json"
    
    if not os.path.exists(json_file):
        return {"error": f"Fichier manquant: {puzzle_id}"}
    
    try:
        # Charger puzzle
        with open(json_file, 'r') as f:
            puzzle = json.load(f)
        
        # Analyser avec ARCAnalyzer
        analyzer = ARCAnalyzer()
        analysis = analyzer.analyze_puzzle(puzzle)
        motif_result = analysis['patterns']['motif']
        
        # Déterminer si c'est un vrai puzzle MOTIF
        is_known_motif = puzzle_id in known_motif_puzzles
        
        # Calculer métriques
        true_positive = is_known_motif and motif_result['detected']
        false_positive = not is_known_motif and motif_result['detected']
        true_negative = not is_known_motif and not motif_result['detected']
        false_negative = is_known_motif and not motif_result['detected']
        
        return {
            'puzzle_id': puzzle_id,
            'is_known_motif': is_known_motif,
            'motif_detected': motif_result['detected'],
            'motif_type': motif_result['type'],
            'confidence': motif_result['confidence'],
            'true_positive': true_positive,
            'false_positive': false_positive,
            'true_negative': true_negative,
            'false_negative': false_negative,
            'success': True
        }
        
    except Exception as e:
        return {"error": f"Erreur analyse {puzzle_id}: {e}"}

def compute_metrics(results: List[Dict]) -> Dict:
    """Calcule les métriques de performance"""
    successful_results = [r for r in results if 'error' not in r]
    
    if not successful_results:
        return {"error": "Aucun résultat valide"}
    
    # Compter les catégories
    tp = sum(1 for r in successful_results if r['true_positive'])
    fp = sum(1 for r in successful_results if r['false_positive'])
    tn = sum(1 for r in successful_results if r['true_negative'])
    fn = sum(1 for r in successful_results if r['false_negative'])
    
    # Métriques de base
    total = tp + fp + tn + fn
    accuracy = (tp + tn) / total if total > 0 else 0
    precision = tp / (tp + fp) if (tp + fp) > 0 else 0
    recall = tp / (tp + fn) if (tp + fn) > 0 else 0
    f1_score = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0
    
    # Analyse par type de MOTIF détecté
    detected_results = [r for r in successful_results if r['motif_detected']]
    type_distribution = {}
    confidence_distribution = {'high': 0, 'medium': 0, 'low': 0}
    
    for result in detected_results:
        motif_type = result['motif_type']
        confidence = result['confidence']
        
        if motif_type not in type_distribution:
            type_distribution[motif_type] = 0
        type_distribution[motif_type] += 1
        
        if confidence >= 0.8:
            confidence_distribution['high'] += 1
        elif confidence >= 0.5:
            confidence_distribution['medium'] += 1
        else:
            confidence_distribution['low'] += 1
    
    return {
        'total_puzzles': total,
        'successful_analyses': len(successful_results),
        'errors': len(results) - len(successful_results),
        'confusion_matrix': {
            'true_positive': tp,
            'false_positive': fp,
            'true_negative': tn,
            'false_negative': fn
        },
        'metrics': {
            'accuracy': accuracy,
            'precision': precision,
            'recall': recall,
            'f1_score': f1_score
        },
        'detection_stats': {
            'total_detected': len(detected_results),
            'type_distribution': type_distribution,
            'confidence_distribution': confidence_distribution
        }
    }

def main():
    """Validation complète sur tous les puzzles ARC"""
    print("🔍 VALIDATION COMPLÈTE DÉTECTION MOTIF - 401 PUZZLES ARC")
    print("=" * 60)
    
    # Charger tous les puzzles
    all_puzzles = get_all_arc_puzzles()
    known_motif_puzzles = load_motif_puzzles()
    
    print(f"📋 {len(all_puzzles)} puzzles ARC trouvés")
    print(f"📋 {len(known_motif_puzzles)} puzzles MOTIF connus")
    
    if not all_puzzles:
        print("❌ Aucun puzzle trouvé")
        return
    
    # Analyser tous les puzzles
    print(f"\n🧪 Analyse en cours...")
    start_time = time.time()
    
    results = []
    errors = 0
    
    for i, puzzle_id in enumerate(all_puzzles):
        if (i + 1) % 50 == 0:  # Progress tous les 50
            elapsed = time.time() - start_time
            rate = (i + 1) / elapsed
            eta = (len(all_puzzles) - i - 1) / rate
            print(f"  Progress: {i+1}/{len(all_puzzles)} ({(i+1)/len(all_puzzles)*100:.1f}%) - ETA: {eta:.0f}s")
        
        result = analyze_puzzle_motif(puzzle_id, known_motif_puzzles)
        results.append(result)
        
        if 'error' in result:
            errors += 1
    
    elapsed_time = time.time() - start_time
    print(f"\n⏱️ Analyse terminée en {elapsed_time:.1f}s ({len(all_puzzles)/elapsed_time:.1f} puzzles/s)")
    
    # Calculer métriques
    print("\n📊 CALCUL DES MÉTRIQUES...")
    metrics = compute_metrics(results)
    
    if 'error' in metrics:
        print(f"❌ Erreur calcul métriques: {metrics['error']}")
        return
    
    # Afficher résultats
    print("\n🎯 RÉSULTATS GLOBAUX")
    print("=" * 30)
    print(f"Puzzles analysés: {metrics['successful_analyses']}/{metrics['total_puzzles']}")
    print(f"Erreurs: {metrics['errors']}")
    
    cm = metrics['confusion_matrix']
    print(f"\n📊 Matrice de Confusion:")
    print(f"  Vrais Positifs (TP): {cm['true_positive']}")
    print(f"  Faux Positifs (FP): {cm['false_positive']}")
    print(f"  Vrais Négatifs (TN): {cm['true_negative']}")
    print(f"  Faux Négatifs (FN): {cm['false_negative']}")
    
    m = metrics['metrics']
    print(f"\n📈 Métriques de Performance:")
    print(f"  Précision: {m['precision']:.3f} ({m['precision']*100:.1f}%)")
    print(f"  Rappel: {m['recall']:.3f} ({m['recall']*100:.1f}%)")
    print(f"  F1-Score: {m['f1_score']:.3f}")
    print(f"  Exactitude: {m['accuracy']:.3f} ({m['accuracy']*100:.1f}%)")
    
    ds = metrics['detection_stats']
    print(f"\n🔍 Statistiques de Détection:")
    print(f"  Total détecté: {ds['total_detected']}")
    print(f"  Types détectés: {ds['type_distribution']}")
    print(f"  Confiance: Haute={ds['confidence_distribution']['high']}, "
          f"Moyenne={ds['confidence_distribution']['medium']}, "
          f"Faible={ds['confidence_distribution']['low']}")
    
    # Identifier cas problématiques
    false_positives = [r for r in results if 'error' not in r and r['false_positive']]
    false_negatives = [r for r in results if 'error' not in r and r['false_negative']]
    
    if false_positives:
        print(f"\n⚠️ Faux Positifs ({len(false_positives)}):")
        for fp in false_positives[:10]:  # Montrer les 10 premiers
            print(f"  - {fp['puzzle_id']}: {fp['motif_type']} (conf: {fp['confidence']:.2f})")
    
    if false_negatives:
        print(f"\n❌ Faux Négatifs ({len(false_negatives)}):")
        for fn in false_negatives[:10]:  # Montrer les 10 premiers
            print(f"  - {fn['puzzle_id']}")
    
    # Sauvegarder résultats complets
    output_data = {
        'analysis_date': time.strftime('%Y-%m-%d %H:%M:%S'),
        'total_puzzles': len(all_puzzles),
        'known_motif_puzzles': len(known_motif_puzzles),
        'metrics': metrics,
        'detailed_results': results
    }
    
    with open('complete_motif_validation.json', 'w') as f:
        json.dump(output_data, f, indent=2)
    
    print(f"\n💾 Résultats complets sauvegardés dans 'complete_motif_validation.json'")
    
    # Évaluation finale
    print(f"\n🏆 ÉVALUATION FINALE")
    if m['f1_score'] >= 0.8:
        print("✅ Performance excellente (F1 ≥ 0.8)")
    elif m['f1_score'] >= 0.6:
        print("✅ Performance satisfaisante (F1 ≥ 0.6)")
    elif m['f1_score'] >= 0.4:
        print("⚠️ Performance acceptable (F1 ≥ 0.4)")
    else:
        print("❌ Performance insuffisante (F1 < 0.4)")
    
    print(f"Recommandation: {'Prêt pour production' if m['f1_score'] >= 0.6 else 'Améliorations nécessaires'}")

if __name__ == "__main__":
    main()