#!/usr/bin/env python3
"""
Affichage détaillé et structuré des résultats d'analyse des tâches ARC
"""

import json
from pathlib import Path

def display_detailed_results():
    """Affiche les résultats détaillés de manière structurée"""
    
    # Charger les résultats
    results_file = Path("specific_tasks_analysis_results.json")
    if not results_file.exists():
        print("❌ Fichier de résultats non trouvé. Exécutez d'abord test_specific_tasks_analyzer.py")
        return
    
    with open(results_file, 'r', encoding='utf-8') as f:
        all_results = json.load(f)
    
    print("=" * 100)
    print("RÉSULTATS DÉTAILLÉS - ANALYSE DES TÂCHES ARC SPÉCIFIÉES")
    print("=" * 100)
    
    for task_id, results in all_results.items():
        if 'error' in results:
            print(f"\n❌ TÂCHE {task_id}: ERREUR - {results['error']}")
            continue
        
        print(f"\n{'='*20} TÂCHE {task_id} {'='*20}")
        
        # 1. INFORMATIONS GÉNÉRALES
        print("\n📊 INFORMATIONS GÉNÉRALES:")
        stats = results['statistics']
        print(f"   • Exemples d'entraînement: {stats['train_examples']}")
        print(f"   • Exemples de test: {stats['test_examples']}")
        print(f"   • Paires input/output même taille: {stats['same_size_pairs']}")
        
        # Changements de dimensions
        print(f"\n📐 CHANGEMENTS DE DIMENSIONS:")
        for i, change_info in enumerate(stats['dimension_changes']):
            inp_dim = change_info['input_dim']
            out_dim = change_info['output_dim']
            change = change_info['change']
            print(f"   • Exemple {i+1}: {inp_dim} → {out_dim} (Δ: {change})")
        
        # 2. PATTERNS DÉTECTÉS
        print(f"\n🔍 PATTERNS DÉTECTÉS:")
        patterns = results['puzzle_analysis']['patterns']
        
        # Répétition
        rep = patterns['repetition']
        if rep['detected']:
            print(f"   ✅ Répétition: {rep['type']}")
            if 'factor' in rep and rep['factor']:
                print(f"      - Facteur: {rep['factor']}")
        else:
            print(f"   ❌ Répétition: Non détectée")
        
        # Mise à l'échelle
        scaling = patterns['scaling']
        if scaling['detected']:
            print(f"   ✅ Mise à l'échelle: Détectée")
            if scaling['factors']:
                print(f"      - Facteurs: {scaling['factors']}")
        else:
            print(f"   ❌ Mise à l'échelle: Non détectée")
        
        # Motif
        motif = patterns['motif']
        if motif['detected']:
            print(f"   ✅ Motif: {motif['type']} (confiance: {motif['confidence']:.2f})")
            if motif['extraction_method']:
                print(f"      - Méthode d'extraction: {motif['extraction_method']}")
            if motif['tiling_factor']:
                print(f"      - Facteur de tiling: {motif['tiling_factor']}")
        else:
            print(f"   ❌ Motif: Non détecté")
        
        # 3. ANALYSE DES COULEURS
        print(f"\n🎨 ANALYSE DES COULEURS:")
        colors = results['puzzle_analysis']['colors']
        print(f"   • Couleurs input: {sorted(colors['input_colors'])} ({len(colors['input_colors'])} couleurs)")
        print(f"   • Couleurs output: {sorted(colors['output_colors'])} ({len(colors['output_colors'])} couleurs)")
        
        # Fréquences des couleurs les plus utilisées
        if colors['color_frequency']:
            top_colors = sorted(colors['color_frequency'].items(), key=lambda x: x[1], reverse=True)[:5]
            print(f"   • Top 5 couleurs: {[(c, f) for c, f in top_colors]}")
        
        # 4. COMPLEXITÉ
        print(f"\n📈 COMPLEXITÉ:")
        complexity = results['puzzle_analysis']['complexity']
        print(f"   • Complexité globale: {complexity['overall_complexity']:.2f}")
        print(f"   • Complexité grille: {complexity['grid_size_complexity']:.2f}")
        print(f"   • Complexité couleur: {complexity['color_complexity']:.2f}")
        print(f"   • Complexité objets: {complexity['object_complexity']:.2f}")
        print(f"   • Complexité transformations: {complexity['transformation_complexity']:.2f}")
        
        # 5. ANALYSE DES GRILLES D'ENTRÉE (TRAIN)
        print(f"\n📥 GRILLES D'ENTRÉE (TRAIN):")
        train_inputs = results['train_inputs_analysis']
        if train_inputs['summary']:
            summary = train_inputs['summary']
            print(f"   • Dimensions: {summary['unique_dimensions']}")
            print(f"   • Cohérence dimensions: {'Oui' if summary['dimension_consistency'] else 'Non'}")
            print(f"   • Couleurs utilisées: {summary['all_colors_used']}")
            print(f"   • Complexité moyenne: {summary['avg_complexity']:.2f}")
        
        # Détails par grille
        for i, grid_analysis in enumerate(train_inputs['individual_analyses'][:3]):  # Max 3 exemples
            print(f"   • Grille {i+1}: {grid_analysis['dimensions']}, {grid_analysis['color_count']} couleurs, {grid_analysis['object_count']} objets")
            if grid_analysis['background_color'] is not None:
                print(f"     - Fond: couleur {grid_analysis['background_color']} ({grid_analysis['non_background_ratio']:.1%} non-fond)")
        
        # 6. ANALYSE DES GRILLES DE SORTIE (TRAIN)
        print(f"\n📤 GRILLES DE SORTIE (TRAIN):")
        train_outputs = results['train_outputs_analysis']
        if train_outputs['summary']:
            summary = train_outputs['summary']
            print(f"   • Dimensions: {summary['unique_dimensions']}")
            print(f"   • Cohérence dimensions: {'Oui' if summary['dimension_consistency'] else 'Non'}")
            print(f"   • Couleurs utilisées: {summary['all_colors_used']}")
            print(f"   • Complexité moyenne: {summary['avg_complexity']:.2f}")
        
        # 7. ANALYSE DES GRILLES DE TEST
        print(f"\n🧪 GRILLES DE TEST:")
        test_inputs = results['test_inputs_analysis']
        if test_inputs['summary']:
            summary = test_inputs['summary']
            print(f"   • Dimensions: {summary['unique_dimensions']}")
            print(f"   • Couleurs utilisées: {summary['all_colors_used']}")
        
        # 8. TRANSFORMATIONS INFÉRÉES
        print(f"\n🔄 TRANSFORMATIONS INFÉRÉES:")
        transformations = results['puzzle_analysis']['transformations']
        print(f"   • Géométriques: {transformations['geometric'] if transformations['geometric'] else 'Aucune'}")
        print(f"   • Couleurs: {transformations['color'] if transformations['color'] else 'Aucune'}")
        print(f"   • Structurelles: {transformations['structural'] if transformations['structural'] else 'Aucune'}")
        
        # 9. SYMÉTRIES
        print(f"\n🪞 SYMÉTRIES:")
        symmetries = results['puzzle_analysis']['symmetries']
        if symmetries['input_symmetries']:
            for i, sym in enumerate(symmetries['input_symmetries'][:2]):  # Max 2 exemples
                sym_types = [k for k, v in sym.items() if v]
                print(f"   • Input {i+1}: {sym_types if sym_types else 'Aucune symétrie'}")
        
        print(f"\n{'-'*80}")

def main():
    display_detailed_results()

if __name__ == "__main__":
    main()