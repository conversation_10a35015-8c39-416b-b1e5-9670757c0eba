#!/usr/bin/env python3
"""
Générateur de commandes MOTIF pour les mosaïques symétriques
Analyse les transformations et génère les commandes AGI correspondantes
"""

import numpy as np
import json
from pathlib import Path
from collections import defaultdict

class MotifCommandGenerator:
    """
    Générateur de commandes MOTIF basé sur l'analyse des transformations symétriques
    """
    
    def __init__(self):
        self.empty_value = 0
        self.min_block_size = 2
    
    def analyze_and_generate_motifs(self, task_id: str):
        """
        Analyse une tâche et génère les commandes MOTIF correspondantes
        """
        # Charger la tâche
        task_path = Path(f"../arcdata/training/{task_id}.json")
        with open(task_path, 'r') as f:
            task_data = json.load(f)
        
        print("=" * 80)
        print(f"GÉNÉRATION DE COMMANDES MOTIF - TÂCHE {task_id}")
        print("=" * 80)
        
        results = {
            'task_id': task_id,
            'motif_commands': [],
            'examples_analysis': [],
            'pattern_consistency': 0.0
        }
        
        # Analyser chaque exemple
        all_motifs = []
        for i, example in enumerate(task_data['train']):
            input_grid = np.array(example['input'])
            output_grid = np.array(example['output'])
            
            print(f"\n🔍 ANALYSE EXEMPLE {i+1}:")
            example_motifs = self.detect_motif_transformations(input_grid, output_grid, i)
            
            results['examples_analysis'].append({
                'example_idx': i,
                'motifs_found': len(example_motifs),
                'motif_commands': example_motifs
            })
            
            all_motifs.extend(example_motifs)
            
            # Afficher les commandes trouvées
            if example_motifs:
                print(f"   ✅ {len(example_motifs)} commandes MOTIF générées:")
                for motif in example_motifs:
                    print(f"      {motif}")
            else:
                print(f"   ❌ Aucune commande MOTIF détectée")
        
        # Analyser la cohérence des patterns
        results['motif_commands'] = self.consolidate_motifs(all_motifs)
        results['pattern_consistency'] = self.calculate_pattern_consistency(all_motifs)
        
        print(f"\n{'='*50}")
        print(f"COMMANDES MOTIF CONSOLIDÉES:")
        print(f"{'='*50}")
        
        if results['motif_commands']:
            for i, motif in enumerate(results['motif_commands'], 1):
                print(f"{i}. {motif}")
        else:
            print("Aucune commande MOTIF cohérente détectée")
        
        print(f"\nCohérence des patterns: {results['pattern_consistency']:.2f}")
        
        return results
    
    def detect_motif_transformations(self, input_grid: np.ndarray, output_grid: np.ndarray, example_idx: int) -> list:
        """
        Détecte les transformations MOTIF entre input et output
        """
        h, w = input_grid.shape
        motif_commands = []
        
        # Trouver les différences entre input et output
        diff_mask = input_grid != output_grid
        
        if not np.any(diff_mask):
            return motif_commands
        
        # Identifier les régions qui ont changé
        changed_regions = self.find_changed_regions(input_grid, output_grid, diff_mask)
        
        # Pour chaque région changée, chercher la source possible
        for region in changed_regions:
            source_candidates = self.find_source_candidates(input_grid, region)
            
            for candidate in source_candidates:
                transformation = self.determine_transformation(candidate, region)
                if transformation:
                    motif_command = self.format_motif_command(candidate, transformation, region)
                    if motif_command:
                        motif_commands.append(motif_command)
        
        return motif_commands
    
    def find_changed_regions(self, input_grid: np.ndarray, output_grid: np.ndarray, diff_mask: np.ndarray) -> list:
        """
        Trouve les régions rectangulaires qui ont changé
        """
        regions = []
        visited = np.zeros_like(diff_mask, dtype=bool)
        
        h, w = input_grid.shape
        
        for i in range(h):
            for j in range(w):
                if diff_mask[i, j] and not visited[i, j]:
                    # Trouver le rectangle englobant cette région
                    region = self.extract_rectangular_region(input_grid, output_grid, i, j, diff_mask, visited)
                    if region and region['area'] >= 4:  # Au moins 2x2
                        regions.append(region)
        
        return regions
    
    def extract_rectangular_region(self, input_grid: np.ndarray, output_grid: np.ndarray, 
                                  start_i: int, start_j: int, diff_mask: np.ndarray, visited: np.ndarray) -> dict:
        """
        Extrait une région rectangulaire à partir d'un point de départ
        """
        h, w = input_grid.shape
        
        # Trouver les limites de la région connexe
        min_i, max_i = start_i, start_i
        min_j, max_j = start_j, start_j
        
        # Expansion simple pour former un rectangle
        changed_points = []
        stack = [(start_i, start_j)]
        
        while stack:
            i, j = stack.pop()
            if (i < 0 or i >= h or j < 0 or j >= w or 
                visited[i, j] or not diff_mask[i, j]):
                continue
            
            visited[i, j] = True
            changed_points.append((i, j))
            
            min_i, max_i = min(min_i, i), max(max_i, i)
            min_j, max_j = min(min_j, j), max(max_j, j)
            
            # Ajouter les voisins
            for di, dj in [(-1, 0), (1, 0), (0, -1), (0, 1)]:
                stack.append((i + di, j + dj))
        
        if not changed_points:
            return None
        
        # Créer le rectangle englobant
        rect_h = max_i - min_i + 1
        rect_w = max_j - min_j + 1
        
        return {
            'coords': (min_i, min_j, max_i + 1, max_j + 1),
            'size': (rect_h, rect_w),
            'area': rect_h * rect_w,
            'input_content': input_grid[min_i:max_i+1, min_j:max_j+1].copy(),
            'output_content': output_grid[min_i:max_i+1, min_j:max_j+1].copy(),
            'changed_points': changed_points
        }
    
    def find_source_candidates(self, input_grid: np.ndarray, target_region: dict) -> list:
        """
        Trouve les candidats sources possibles pour une région cible
        """
        h, w = input_grid.shape
        target_size = target_region['size']
        target_content = target_region['output_content']
        
        candidates = []
        
        # Chercher des blocs de même taille dans l'input
        for i in range(h - target_size[0] + 1):
            for j in range(w - target_size[1] + 1):
                candidate_block = input_grid[i:i+target_size[0], j:j+target_size[1]]
                
                # Ignorer les blocs trop vides
                if np.sum(candidate_block != self.empty_value) < target_size[0] * target_size[1] * 0.3:
                    continue
                
                # Tester différentes transformations
                transformations = {
                    'identity': candidate_block,
                    'flip_horizontal': np.fliplr(candidate_block),
                    'flip_vertical': np.flipud(candidate_block),
                    'rotate_90': np.rot90(candidate_block, 1),
                    'rotate_180': np.rot90(candidate_block, 2),
                    'rotate_270': np.rot90(candidate_block, 3)
                }
                
                for trans_name, transformed in transformations.items():
                    if transformed.shape == target_content.shape:
                        similarity = self.calculate_block_similarity(transformed, target_content)
                        if similarity > 0.7:  # Seuil de similarité
                            candidates.append({
                                'coords': (i, j, i + target_size[0], j + target_size[1]),
                                'content': candidate_block,
                                'transformation': trans_name,
                                'similarity': similarity,
                                'transformed_content': transformed
                            })
        
        # Trier par similarité décroissante
        candidates.sort(key=lambda x: x['similarity'], reverse=True)
        return candidates[:3]  # Top 3 candidats
    
    def calculate_block_similarity(self, block1: np.ndarray, block2: np.ndarray) -> float:
        """
        Calcule la similarité entre deux blocs
        """
        if block1.shape != block2.shape:
            return 0.0
        
        # Ignorer les cellules vides pour le calcul
        mask1 = block1 != self.empty_value
        mask2 = block2 != self.empty_value
        
        if not np.any(mask1) or not np.any(mask2):
            return 0.0
        
        # Comparer seulement les cellules non-vides
        common_mask = mask1 & mask2
        if not np.any(common_mask):
            return 0.0
        
        matches = np.sum(block1[common_mask] == block2[common_mask])
        total = np.sum(common_mask)
        
        return matches / total if total > 0 else 0.0
    
    def determine_transformation(self, source_candidate: dict, target_region: dict) -> str:
        """
        Détermine le type de transformation entre source et cible
        """
        return source_candidate['transformation']
    
    def format_motif_command(self, source: dict, transformation: str, target: dict) -> str:
        """
        Formate une commande MOTIF
        """
        src_coords = source['coords']
        tgt_coords = target['coords']
        
        # Format des coordonnées : [r1,c1 r2,c2] (indices inclusifs)
        src_str = f"[{src_coords[0]},{src_coords[1]} {src_coords[2]-1},{src_coords[3]-1}]"
        tgt_str = f"[{tgt_coords[0]},{tgt_coords[1]}]"
        
        # Conversion des noms de transformation
        trans_map = {
            'identity': 'COPY',
            'flip_horizontal': 'FLIP HORIZONTAL',
            'flip_vertical': 'FLIP VERTICAL',
            'rotate_90': 'ROTATE 90',
            'rotate_180': 'ROTATE 180',
            'rotate_270': 'ROTATE 270'
        }
        
        trans_str = trans_map.get(transformation, transformation.upper())
        
        if transformation == 'identity':
            return f"MOTIF {{COPY {src_str}; PASTE {tgt_str}}}"
        else:
            return f"MOTIF {{COPY {src_str}; {trans_str}; PASTE {tgt_str}}}"
    
    def consolidate_motifs(self, all_motifs: list) -> list:
        """
        Consolide les commandes MOTIF similaires
        """
        if not all_motifs:
            return []
        
        # Grouper les commandes similaires
        motif_groups = defaultdict(list)
        
        for motif in all_motifs:
            # Extraire le pattern de transformation
            if 'FLIP HORIZONTAL' in motif:
                pattern = 'flip_horizontal'
            elif 'FLIP VERTICAL' in motif:
                pattern = 'flip_vertical'
            elif 'ROTATE' in motif:
                pattern = 'rotate'
            else:
                pattern = 'copy'
            
            motif_groups[pattern].append(motif)
        
        # Prendre les commandes les plus fréquentes
        consolidated = []
        for pattern, motifs in motif_groups.items():
            if len(motifs) >= 2:  # Au moins 2 occurrences
                # Prendre la première occurrence comme représentative
                consolidated.append(motifs[0])
        
        return consolidated
    
    def calculate_pattern_consistency(self, all_motifs: list) -> float:
        """
        Calcule la cohérence des patterns détectés
        """
        if not all_motifs:
            return 0.0
        
        # Analyser les types de transformation
        transformations = []
        for motif in all_motifs:
            if 'FLIP HORIZONTAL' in motif:
                transformations.append('flip_horizontal')
            elif 'FLIP VERTICAL' in motif:
                transformations.append('flip_vertical')
            elif 'ROTATE' in motif:
                transformations.append('rotate')
            else:
                transformations.append('copy')
        
        # Calculer la diversité des transformations
        unique_transforms = set(transformations)
        consistency = len(unique_transforms) / len(transformations) if transformations else 0
        
        return 1.0 - consistency  # Plus il y a de diversité, moins c'est cohérent

def test_motif_generator():
    """
    Test le générateur sur les tâches spécifiées
    """
    generator = MotifCommandGenerator()
    
    # Tester sur 3631a71a (cas confirmé de mosaïque)
    print("Test sur la tâche 3631a71a (mosaïque confirmée):")
    results_3631 = generator.analyze_and_generate_motifs("3631a71a")
    
    print("\n" + "="*80)
    
    # Tester sur une autre tâche pour comparaison
    print("Test sur la tâche 0dfd9992 (pour comparaison):")
    results_0dfd = generator.analyze_and_generate_motifs("0dfd9992")
    
    return results_3631, results_0dfd

if __name__ == "__main__":
    test_motif_generator()