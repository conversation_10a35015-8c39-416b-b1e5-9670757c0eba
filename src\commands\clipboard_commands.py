"""
Module contenant les commandes de presse-papier pour les grilles ARC
"""

import numpy as np
from typing import Dict, Any
from .unified_command import UnifiedCommand
from .coordinate_utils import CoordinateUtils


class ClipboardCommands:
    """Classe contenant les commandes de presse-papier pour les grilles ARC"""
    
    def __init__(self, executor):
        """
        Initialise les commandes de presse-papier
        
        Args:
            executor: Instance de CommandExecutor pour accéder à la grille et aux propriétés
        """
        self.executor = executor

    def cmd_copy(self, cmd: UnifiedCommand) -> bool:
        """Copie une ou plusieurs sélections dans le presse-papier en tant que motif unifié."""
        if self.executor.grid is None:
            self.executor.error = "La grille n'a pas été initialisée."
            return False

        try:
            # Utiliser uniquement la fonction générique pour parser les blocs de coordonnées
            coordinate_blocks = CoordinateUtils.parse_coordinate_blocks(cmd.raw_command) if hasattr(cmd, 'raw_command') and cmd.raw_command else []

            # Si aucun bloc de coordonnées n'est trouvé, essayer les coordonnées standards
            if not coordinate_blocks and cmd.coordinates:
                # Fallback: traiter les coordonnées par paires de 4
                coords = cmd.coordinates
                coordinate_blocks = []
                i = 0
                while i + 3 < len(coords):
                    x1, y1, x2, y2 = coords[i], coords[i+1], coords[i+2], coords[i+3]
                    if x1 == x2 and y1 == y2:
                        # Cellule simple
                        coordinate_blocks.append([f"{x1},{y1}"])
                    else:
                        # Rectangle
                        coordinate_blocks.append([f"{x1},{y1}", f"{x2},{y2}"])
                    i += 4

            # Traitement unifié des blocs avec modificateurs
            selected_cells = []
            min_x, min_y = float('inf'), float('inf')
            max_x, max_y = -1, -1

            def copy_action(x1, y1, x2, y2):
                nonlocal min_x, min_y, max_x, max_y
                min_x, min_y = min(min_x, x1), min(min_y, y1)
                max_x, max_y = max(max_x, x2), max(max_y, y2)
                
                for x in range(x1, x2 + 1):
                    for y in range(y1, y2 + 1):
                        selected_cells.append((x, y, self.executor.grid[x, y]))

            # Utiliser la fonction générique pour traiter les blocs
            CoordinateUtils.process_coordinate_blocks(self.executor.grid, coordinate_blocks, copy_action)

            if not selected_cells:
                self.executor.error = "Aucune cellule à copier"
                return False

            # Créer le presse-papier avec les dimensions minimales
            clip_height = max_x - min_x + 1
            clip_width = max_y - min_y + 1
            self.executor.clipboard = np.zeros((clip_height, clip_width), dtype=int)
            self.executor.clipboard_mask = np.zeros((clip_height, clip_width), dtype=bool)

            # Remplir le presse-papier avec les cellules sélectionnées
            for x, y, value in selected_cells:
                rel_x, rel_y = x - min_x, y - min_y
                self.executor.clipboard[rel_x, rel_y] = value
                self.executor.clipboard_mask[rel_x, rel_y] = True

            return True
        except (ValueError, TypeError, IndexError) as e:
            self.executor.error = f"Exception dans cmd_copy: {e}"
            return False

    def cmd_cut(self, cmd: UnifiedCommand) -> bool:
        """Coupe un rectangle ou une sélection spéciale et le place dans le presse-papier"""
        if self.cmd_copy(cmd):
            # Effacer la zone copiée en utilisant le parsing générique
            # Utiliser la fonction générique pour parser les blocs de coordonnées
            if hasattr(cmd, 'raw_command') and cmd.raw_command:
                coordinate_blocks = CoordinateUtils.parse_coordinate_blocks(cmd.raw_command)
            else:
                # Fallback: ancienne méthode par paires de 4 coordonnées
                coords = cmd.coordinates
                coordinate_blocks = []
                i = 0
                while i + 3 < len(coords):
                    x1, y1, x2, y2 = coords[i], coords[i+1], coords[i+2], coords[i+3]
                    if x1 == x2 and y1 == y2:
                        # Cellule simple
                        coordinate_blocks.append([f"{x1},{y1}"])
                    else:
                        # Rectangle
                        coordinate_blocks.append([f"{x1},{y1}", f"{x2},{y2}"])
                    i += 4
            
            # Utiliser la fonction générique pour traiter les blocs
            def cut_action(x1, y1, x2, y2):
                self.executor.grid[x1:x2+1, y1:y2+1] = 0
            
            CoordinateUtils.process_coordinate_blocks(self.executor.grid, coordinate_blocks, cut_action)
            return True
        return False

    def cmd_paste(self, cmd: UnifiedCommand) -> bool:
        """Colle le contenu du presse-papier."""
        if self.executor.grid is None or self.executor.clipboard is None:
            self.executor.error = "La grille n'est pas initialisée ou le presse-papiers est vide."
            return False

        try:
            # Si aucune coordonnée n'est fournie, on ne fait rien (le collage doit être explicite)
            if not cmd.coordinates:
                self.executor.error = "La commande PASTE nécessite au moins une coordonnée de destination."
                return False

            # Traiter les coordonnées par paires (x, y)
            for i in range(0, len(cmd.coordinates), 2):
                x_dest, y_dest = cmd.coordinates[i], cmd.coordinates[i+1]

                clip_height, clip_width = self.executor.clipboard.shape

                # Vérifier les limites de la destination
                if x_dest < 0 or y_dest < 0 or x_dest >= self.executor.height or y_dest >= self.executor.width:
                    self.executor.error = f"Coordonnée de collage ({x_dest},{y_dest}) hors limites."
                    return False

                # Coller en respectant le masque
                for r in range(clip_height):
                    for c in range(clip_width):
                        if self.executor.clipboard_mask is None or self.executor.clipboard_mask[r, c]:
                            grid_r, grid_c = x_dest + r, y_dest + c
                            if 0 <= grid_r < self.executor.height and 0 <= grid_c < self.executor.width:
                                self.executor.grid[grid_r, grid_c] = self.executor.clipboard[r, c]
            return True
        except (ValueError, TypeError, IndexError) as e:
            self.executor.error = f"Exception dans cmd_paste: {e}"
            return False
