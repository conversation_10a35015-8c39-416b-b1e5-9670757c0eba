# Guide d'Utilisation - AnalysesGrilles

## Démarrage Rapide

### 1. Démonstration

```bash
python quick_start.py
```

### 2. Test Simple

```bash
python test_pure_analysis.py
```

### 3. Analyse d'une Tâche Spécifique

```bash
python run_grid_analysis.py --taskId 007bbfb7
```

### 4. <PERSON><PERSON><PERSON>

```bash
python run_grid_analysis.py --max-puzzles 10
```

### 5. Analyse <PERSON>l<PERSON>

```bash
python run_grid_analysis.py
```

## Fichiers Principaux

| Fichier                 | Description                           |
| ----------------------- | ------------------------------------- |
| `run_grid_analysis.py`  | Script principal d'analyse            |
| `grid_analysis_pure.py` | Analyseur avec algorithmes classiques |
| `config_minimal.py`     | Configuration de base                 |
| `quick_start.py`        | Démonstration rapide                  |
| `test_pure_analysis.py` | Tests unitaires                       |

## Options du Script Principal

```bash
python run_grid_analysis.py [OPTIONS]

Options:
  --data-dir PATH     Répertoire des puzzles JSON (défaut: ../arcdata/training)
  --taskId ID         Analyser une tâche spécifique (ex: 007bbfb7)
  --max-puzzles N     Limiter à N puzzles
  --no-viz           Pas de visualisations
  --quiet            Mode silencieux
```

## Fichiers Générés

### Analyse Multiple

Après une analyse multiple, vous obtiendrez :

- `grid_analysis_report.md` - Rapport détaillé
- `grid_analysis_data.csv` - Données pour Excel/analyse
- `grid_analysis_dashboard.png` - Graphiques

### Analyse Individuelle (--taskId)

Après une analyse de tâche spécifique :

- `arcdata/training/{taskId}_analysis.json` - Analyse complète de la tâche

## Utilisation Programmatique

```python
from grid_analysis_pure import PureGridAnalyzer

# Initialiser
analyzer = PureGridAnalyzer("../arcdata/training")

# Analyser une transformation
input_grid = [[1, 0], [0, 1]]
output_grid = [[1, 0, 1, 0], [0, 1, 0, 1], [1, 0, 1, 0], [0, 1, 0, 1]]

analysis = analyzer.analyze_transformation(input_grid, output_grid)

# Résultats
print(f"Type: {analysis['transformation_type']['type']}")
print(f"Ratio: {analysis['size_ratio']}")
print(f"Insights: {analysis['computed_insights']}")
```

## Dépendances

Installer avec :

```bash
pip install numpy pandas matplotlib seaborn
```

## Support

- Consultez `README_grid_analysis.md` pour la documentation complète
- Les algorithmes utilisés sont transparents et documentés
- Aucune "boîte noire" IA - tout est calculé explicitement
