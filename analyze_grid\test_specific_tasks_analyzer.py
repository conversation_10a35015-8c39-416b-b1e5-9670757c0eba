#!/usr/bin/env python3
"""
Test de l'ARCAnalyzer sur des tâches spécifiques
Analyse les tâches : 0dfd9992, 29ec7d0e, 3631a71a, 484b58aa, 9ecd008a, b8825c91, c3f564a4, dc0a314f
"""

import json
import os
import sys
from pathlib import Path

# Ajouter le dossier parent au path pour importer ARCAnalyzer
sys.path.append(str(Path(__file__).parent))

from analyze_grid.arc_analyzer import ARCAnalyzer

def load_arc_task(task_id: str) -> dict:
    """Charge une tâche ARC depuis le fichier JSON"""
    task_path = Path("../arcdata/training") / f"{task_id}.json"
    
    if not task_path.exists():
        raise FileNotFoundError(f"Tâche {task_id} non trouvée dans {task_path}")
    
    with open(task_path, 'r') as f:
        return json.load(f)

def analyze_grids_detailed(grids: list, grid_type: str, task_id: str) -> dict:
    """Analyse détaillée d'une liste de grilles"""
    analyzer = ARCAnalyzer()
    results = {
        'grid_type': grid_type,
        'task_id': task_id,
        'count': len(grids),
        'individual_analyses': [],
        'summary': {}
    }
    
    all_dimensions = []
    all_colors = set()
    all_complexities = []
    
    for i, grid in enumerate(grids):
        grid_analysis = {
            'index': i,
            'dimensions': (len(grid), len(grid[0]) if grid else 0),
            'total_cells': len(grid) * (len(grid[0]) if grid else 0),
            'colors_used': list(set([cell for row in grid for cell in row])),
            'color_count': len(set([cell for row in grid for cell in row])),
            'background_color': None,
            'non_background_ratio': 0.0,
            'objects': [],
            'symmetries': {},
            'patterns': {}
        }
        
        # Déterminer couleur de fond (plus fréquente)
        if grid:
            from collections import Counter
            color_counts = Counter([cell for row in grid for cell in row])
            most_common = color_counts.most_common(1)[0]
            grid_analysis['background_color'] = most_common[0]
            grid_analysis['background_ratio'] = most_common[1] / grid_analysis['total_cells']
            grid_analysis['non_background_ratio'] = 1.0 - grid_analysis['background_ratio']
        
        # Analyse des objets
        import numpy as np
        grid_array = np.array(grid)
        objects = analyzer.extract_objects(grid_array)
        grid_analysis['objects'] = [{
            'color': obj['color'],
            'size': obj['size'],
            'bbox': obj['bbox'],
            'center': obj['center']
        } for obj in objects]
        grid_analysis['object_count'] = len(objects)
        
        # Analyse des symétries
        grid_analysis['symmetries'] = analyzer.detect_symmetries(grid_array)
        
        # Collecte pour statistiques globales
        all_dimensions.append(grid_analysis['dimensions'])
        all_colors.update(grid_analysis['colors_used'])
        all_complexities.append(grid_analysis['total_cells'] * grid_analysis['color_count'])
        
        results['individual_analyses'].append(grid_analysis)
    
    # Résumé statistique
    if all_dimensions:
        results['summary'] = {
            'unique_dimensions': list(set(all_dimensions)),
            'dimension_consistency': len(set(all_dimensions)) == 1,
            'all_colors_used': sorted(list(all_colors)),
            'total_unique_colors': len(all_colors),
            'avg_complexity': sum(all_complexities) / len(all_complexities) if all_complexities else 0,
            'min_dimensions': min(all_dimensions) if all_dimensions else (0, 0),
            'max_dimensions': max(all_dimensions) if all_dimensions else (0, 0)
        }
    
    return results

def test_specific_tasks():
    """Test l'ARCAnalyzer sur les tâches spécifiées"""
    
    # Tâches à analyser
    task_ids = [
        "0dfd9992", "29ec7d0e", "3631a71a", "484b58aa", 
        "9ecd008a", "b8825c91", "c3f564a4", "dc0a314f"
    ]
    
    analyzer = ARCAnalyzer()
    all_results = {}
    
    print("=== ANALYSE DÉTAILLÉE DES TÂCHES ARC SPÉCIFIÉES ===\n")
    
    for task_id in task_ids:
        print(f"🔍 Analyse de la tâche {task_id}...")
        
        try:
            # Charger la tâche
            task_data = load_arc_task(task_id)
            
            # Analyse complète du puzzle
            puzzle_analysis = analyzer.analyze_puzzle(task_data)
            
            # Analyses détaillées des grilles
            train_inputs = [example['input'] for example in task_data['train']]
            train_outputs = [example['output'] for example in task_data['train']]
            test_inputs = [example['input'] for example in task_data['test']]
            
            # Filtrer les outputs qui ont la même taille que l'input correspondant
            same_size_outputs = []
            for i, (inp, out) in enumerate(zip(train_inputs, train_outputs)):
                if len(inp) == len(out) and len(inp[0]) == len(out[0]):
                    same_size_outputs.append(out)
            
            results = {
                'task_id': task_id,
                'puzzle_analysis': puzzle_analysis,
                'train_inputs_analysis': analyze_grids_detailed(train_inputs, 'train_input', task_id),
                'train_outputs_analysis': analyze_grids_detailed(train_outputs, 'train_output', task_id),
                'test_inputs_analysis': analyze_grids_detailed(test_inputs, 'test_input', task_id),
                'same_size_outputs_analysis': analyze_grids_detailed(same_size_outputs, 'same_size_output', task_id) if same_size_outputs else None,
                'statistics': {
                    'train_examples': len(task_data['train']),
                    'test_examples': len(task_data['test']),
                    'same_size_pairs': len(same_size_outputs),
                    'dimension_changes': []
                }
            }
            
            # Calcul des changements de dimensions
            for example in task_data['train']:
                inp_dim = (len(example['input']), len(example['input'][0]))
                out_dim = (len(example['output']), len(example['output'][0]))
                change = (out_dim[0] - inp_dim[0], out_dim[1] - inp_dim[1])
                results['statistics']['dimension_changes'].append({
                    'input_dim': inp_dim,
                    'output_dim': out_dim,
                    'change': change
                })
            
            all_results[task_id] = results
            
            # Affichage des résultats principaux
            print(f"✅ Tâche {task_id} analysée:")
            print(f"   - {results['statistics']['train_examples']} exemples d'entraînement")
            print(f"   - {results['statistics']['test_examples']} exemples de test")
            print(f"   - {results['statistics']['same_size_pairs']} paires input/output de même taille")
            
            # Patterns détectés
            patterns = puzzle_analysis['patterns']
            detected_patterns = []
            if patterns['repetition']['detected']:
                detected_patterns.append(f"Répétition ({patterns['repetition']['type']})")
            if patterns['scaling']['detected']:
                detected_patterns.append("Mise à l'échelle")
            if patterns['motif']['detected']:
                detected_patterns.append(f"Motif ({patterns['motif']['type']})")
            
            if detected_patterns:
                print(f"   - Patterns: {', '.join(detected_patterns)}")
            else:
                print("   - Aucun pattern majeur détecté")
            
            print()
            
        except Exception as e:
            print(f"❌ Erreur lors de l'analyse de {task_id}: {e}")
            all_results[task_id] = {'error': str(e)}
    
    # Sauvegarde des résultats détaillés
    output_file = "specific_tasks_analysis_results.json"
    
    def convert_numpy_types(obj):
        """Convertit les types numpy en types Python natifs pour JSON"""
        import numpy as np
        if isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, dict):
            return {str(k): convert_numpy_types(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [convert_numpy_types(item) for item in obj]
        elif isinstance(obj, tuple):
            return tuple(convert_numpy_types(item) for item in obj)
        else:
            return obj
    
    # Convertir les types numpy avant sauvegarde
    converted_results = convert_numpy_types(all_results)
    
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(converted_results, f, indent=2, ensure_ascii=False, default=str)
    
    print(f"📊 Résultats détaillés sauvegardés dans {output_file}")
    
    # Génération du rapport de synthèse
    generate_synthesis_report(all_results)
    
    return all_results

def generate_synthesis_report(all_results: dict):
    """Génère un rapport de synthèse des analyses"""
    
    report_lines = []
    report_lines.append("=" * 80)
    report_lines.append("RAPPORT DE SYNTHÈSE - ANALYSE DES TÂCHES ARC SPÉCIFIÉES")
    report_lines.append("=" * 80)
    report_lines.append("")
    
    successful_analyses = {k: v for k, v in all_results.items() if 'error' not in v}
    
    report_lines.append(f"Tâches analysées avec succès: {len(successful_analyses)}/{len(all_results)}")
    report_lines.append("")
    
    # Statistiques globales
    total_train_examples = sum(r['statistics']['train_examples'] for r in successful_analyses.values())
    total_test_examples = sum(r['statistics']['test_examples'] for r in successful_analyses.values())
    total_same_size = sum(r['statistics']['same_size_pairs'] for r in successful_analyses.values())
    
    report_lines.append("STATISTIQUES GLOBALES:")
    report_lines.append(f"- Total exemples d'entraînement: {total_train_examples}")
    report_lines.append(f"- Total exemples de test: {total_test_examples}")
    report_lines.append(f"- Total paires même taille: {total_same_size}")
    report_lines.append("")
    
    # Analyse par tâche
    report_lines.append("ANALYSE DÉTAILLÉE PAR TÂCHE:")
    report_lines.append("-" * 50)
    
    for task_id, results in successful_analyses.items():
        report_lines.append(f"\n🔍 TÂCHE {task_id}:")
        
        # Informations générales
        stats = results['statistics']
        report_lines.append(f"   Exemples: {stats['train_examples']} train, {stats['test_examples']} test")
        report_lines.append(f"   Paires même taille: {stats['same_size_pairs']}")
        
        # Changements de dimensions
        changes = [change['change'] for change in stats['dimension_changes']]
        unique_changes = list(set(changes))
        if len(unique_changes) == 1:
            report_lines.append(f"   Changement dimension cohérent: {unique_changes[0]}")
        else:
            report_lines.append(f"   Changements dimensions variés: {unique_changes}")
        
        # Patterns détectés
        patterns = results['puzzle_analysis']['patterns']
        pattern_info = []
        
        if patterns['repetition']['detected']:
            rep_type = patterns['repetition']['type']
            if 'factor' in patterns['repetition']:
                factor = patterns['repetition']['factor']
                pattern_info.append(f"Répétition {rep_type} (facteur {factor})")
            else:
                pattern_info.append(f"Répétition {rep_type}")
        
        if patterns['scaling']['detected']:
            pattern_info.append("Mise à l'échelle")
        
        if patterns['motif']['detected']:
            motif_type = patterns['motif']['type']
            confidence = patterns['motif']['confidence']
            pattern_info.append(f"Motif {motif_type} (conf: {confidence:.2f})")
        
        if pattern_info:
            report_lines.append(f"   Patterns: {', '.join(pattern_info)}")
        else:
            report_lines.append("   Patterns: Aucun pattern majeur")
        
        # Complexité
        complexity = results['puzzle_analysis']['complexity']
        report_lines.append(f"   Complexité globale: {complexity['overall_complexity']:.2f}")
        
        # Couleurs
        colors = results['puzzle_analysis']['colors']
        report_lines.append(f"   Couleurs utilisées: {len(colors['input_colors'])} input, {len(colors['output_colors'])} output")
        
        # Analyse des grilles d'entrée
        train_inputs = results['train_inputs_analysis']
        if train_inputs['summary']:
            dims = train_inputs['summary']['unique_dimensions']
            consistent = train_inputs['summary']['dimension_consistency']
            report_lines.append(f"   Dimensions train inputs: {dims} {'(cohérent)' if consistent else '(varié)'}")
        
        # Analyse des grilles de sortie
        train_outputs = results['train_outputs_analysis']
        if train_outputs['summary']:
            dims = train_outputs['summary']['unique_dimensions']
            consistent = train_outputs['summary']['dimension_consistency']
            report_lines.append(f"   Dimensions train outputs: {dims} {'(cohérent)' if consistent else '(varié)'}")
    
    # Patterns les plus fréquents
    report_lines.append("\n" + "=" * 50)
    report_lines.append("PATTERNS LES PLUS FRÉQUENTS:")
    
    pattern_counts = {
        'repetition': 0,
        'scaling': 0,
        'motif_tiling': 0,
        'motif_detached': 0,
        'motif_coordinate': 0
    }
    
    for results in successful_analyses.values():
        patterns = results['puzzle_analysis']['patterns']
        if patterns['repetition']['detected']:
            pattern_counts['repetition'] += 1
        if patterns['scaling']['detected']:
            pattern_counts['scaling'] += 1
        if patterns['motif']['detected']:
            motif_type = patterns['motif']['type']
            if motif_type == 'tiling':
                pattern_counts['motif_tiling'] += 1
            elif motif_type == 'detached':
                pattern_counts['motif_detached'] += 1
            elif motif_type == 'coordinate':
                pattern_counts['motif_coordinate'] += 1
    
    for pattern, count in pattern_counts.items():
        if count > 0:
            percentage = (count / len(successful_analyses)) * 100
            report_lines.append(f"- {pattern}: {count}/{len(successful_analyses)} tâches ({percentage:.1f}%)")
    
    # Sauvegarde du rapport
    report_content = "\n".join(report_lines)
    
    with open("specific_tasks_synthesis_report.txt", 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    print("📋 Rapport de synthèse sauvegardé dans specific_tasks_synthesis_report.txt")
    print("\n" + "=" * 50)
    print("APERÇU DU RAPPORT:")
    print("=" * 50)
    print(report_content[:2000] + "..." if len(report_content) > 2000 else report_content)

if __name__ == "__main__":
    # Changer vers le dossier AnalysesGrilles
    os.chdir(Path(__file__).parent)
    
    print("Démarrage de l'analyse des tâches ARC spécifiées...")
    results = test_specific_tasks()
    print("\n✅ Analyse terminée!")