#!/usr/bin/env python3
"""
Analyseur du fichier CSV des métriques ARC
Fournit des statistiques et validations du fichier généré
"""

import pandas as pd
import numpy as np
from typing import Dict, List

def analyze_csv_file(csv_file: str = "arc_complete_metrics.csv"):
    """Analyse le fichier CSV des métriques ARC"""
    
    print("📊 ANALYSE DU FICHIER CSV DES MÉTRIQUES ARC")
    print("=" * 60)
    
    # Charger le CSV
    try:
        df = pd.read_csv(csv_file)
        print(f"✅ Fichier chargé avec succès")
        print(f"Dimensions: {df.shape[0]} lignes × {df.shape[1]} colonnes")
    except Exception as e:
        print(f"❌ Erreur lors du chargement: {e}")
        return
    
    # Informations générales
    print(f"\n📋 INFORMATIONS GÉNÉRALES")
    print("-" * 30)
    print(f"Nombre de puzzles: {len(df)}")
    print(f"Nombre de métriques: {len(df.columns) - 1}")  # -1 pour puzzle_id
    print(f"Mémoire utilisée: {df.memory_usage(deep=True).sum() / 1024 / 1024:.2f} MB")
    
    # Vérifier les valeurs manquantes
    missing_values = df.isnull().sum()
    if missing_values.sum() > 0:
        print(f"\n⚠️ VALEURS MANQUANTES")
        print("-" * 25)
        for col in missing_values[missing_values > 0].index:
            print(f"{col}: {missing_values[col]} valeurs manquantes")
    else:
        print(f"\n✅ Aucune valeur manquante détectée")
    
    # Statistiques des métriques principales
    print(f"\n📈 STATISTIQUES DES MÉTRIQUES PRINCIPALES")
    print("-" * 45)
    
    key_metrics = [
        'max_input_area', 'max_input_colors', 'avg_color_usage_ratio',
        'avg_color_balance_score', 'overall_complexity', 'total_symmetries',
        'mosaic_score'
    ]
    
    for metric in key_metrics:
        if metric in df.columns:
            values = df[metric]
            print(f"{metric}:")
            print(f"  Min: {values.min():.3f}, Max: {values.max():.3f}")
            print(f"  Moyenne: {values.mean():.3f}, Médiane: {values.median():.3f}")
            print(f"  Écart-type: {values.std():.3f}")
            print()
    
    # Distribution des puzzles MOSAIC
    if 'is_mosaic_candidate' in df.columns:
        mosaic_count = df['is_mosaic_candidate'].sum()
        print(f"🎨 PUZZLES MOSAIC DÉTECTÉS")
        print("-" * 30)
        print(f"Nombre: {mosaic_count}")
        print(f"Pourcentage: {mosaic_count/len(df)*100:.1f}%")
        
        if mosaic_count > 0:
            mosaic_puzzles = df[df['is_mosaic_candidate'] == True]
            print(f"\nTop 10 puzzles MOSAIC par score:")
            top_mosaic = mosaic_puzzles.nlargest(10, 'mosaic_score')
            for idx, row in top_mosaic.iterrows():
                print(f"  {row['puzzle_id']}: {row['mosaic_score']:.3f}")
    
    # Distribution des patterns détectés
    pattern_columns = ['repetition_detected', 'scaling_detected', 'motif_detected']
    print(f"\n🔍 PATTERNS DÉTECTÉS")
    print("-" * 20)
    
    for pattern in pattern_columns:
        if pattern in df.columns:
            count = df[pattern].sum()
            print(f"{pattern.replace('_detected', '').upper()}: {count} ({count/len(df)*100:.1f}%)")
    
    # Distribution des symétries
    if 'total_symmetries' in df.columns:
        print(f"\n🔄 DISTRIBUTION DES SYMÉTRIES")
        print("-" * 30)
        symmetry_dist = df['total_symmetries'].value_counts().sort_index()
        for sym_count, puzzle_count in symmetry_dist.items():
            print(f"{sym_count} symétries: {puzzle_count} puzzles ({puzzle_count/len(df)*100:.1f}%)")
    
    # Corrélations intéressantes
    print(f"\n🔗 CORRÉLATIONS INTÉRESSANTES")
    print("-" * 35)
    
    numeric_cols = df.select_dtypes(include=[np.number]).columns
    correlation_matrix = df[numeric_cols].corr()
    
    # Trouver les corrélations fortes (> 0.5 ou < -0.5)
    strong_correlations = []
    for i in range(len(correlation_matrix.columns)):
        for j in range(i+1, len(correlation_matrix.columns)):
            corr_value = correlation_matrix.iloc[i, j]
            if abs(corr_value) > 0.5:
                col1 = correlation_matrix.columns[i]
                col2 = correlation_matrix.columns[j]
                strong_correlations.append((col1, col2, corr_value))
    
    # Trier par valeur absolue de corrélation
    strong_correlations.sort(key=lambda x: abs(x[2]), reverse=True)
    
    print("Corrélations fortes (|r| > 0.5):")
    for col1, col2, corr in strong_correlations[:10]:  # Top 10
        print(f"  {col1} ↔ {col2}: {corr:.3f}")
    
    # Validation des données
    print(f"\n✅ VALIDATION DES DONNÉES")
    print("-" * 30)
    
    validations = []
    
    # Vérifier que les aires sont cohérentes
    if all(col in df.columns for col in ['min_input_area', 'max_input_area']):
        invalid_areas = df[df['min_input_area'] > df['max_input_area']]
        if len(invalid_areas) > 0:
            validations.append(f"❌ {len(invalid_areas)} puzzles avec min_area > max_area")
        else:
            validations.append("✅ Aires cohérentes")
    
    # Vérifier que les couleurs sont dans les limites
    if 'max_input_colors' in df.columns:
        invalid_colors = df[df['max_input_colors'] > 10]  # ARC a max 10 couleurs (0-9)
        if len(invalid_colors) > 0:
            validations.append(f"❌ {len(invalid_colors)} puzzles avec > 10 couleurs")
        else:
            validations.append("✅ Nombre de couleurs valide")
    
    # Vérifier les ratios d'usage
    if 'avg_color_usage_ratio' in df.columns:
        invalid_usage = df[(df['avg_color_usage_ratio'] < 0) | (df['avg_color_usage_ratio'] > 1)]
        if len(invalid_usage) > 0:
            validations.append(f"❌ {len(invalid_usage)} puzzles avec ratio d'usage invalide")
        else:
            validations.append("✅ Ratios d'usage valides")
    
    for validation in validations:
        print(validation)
    
    # Recommandations d'analyse
    print(f"\n💡 RECOMMANDATIONS D'ANALYSE")
    print("-" * 35)
    print("1. Clustering basé sur les métriques de complexité")
    print("2. Analyse des corrélations entre taille et nombre de couleurs")
    print("3. Étude des patterns par type de transformation")
    print("4. Classification supervisée pour prédire les types de puzzles")
    print("5. Analyse des outliers (puzzles avec métriques extrêmes)")
    
    return df

def export_summary_stats(df: pd.DataFrame, output_file: str = "arc_metrics_summary.txt"):
    """Exporte un résumé statistique"""
    
    with open(output_file, 'w') as f:
        f.write("RÉSUMÉ STATISTIQUE DES MÉTRIQUES ARC\n")
        f.write("=" * 50 + "\n\n")
        
        f.write(f"Nombre total de puzzles: {len(df)}\n")
        f.write(f"Nombre de métriques: {len(df.columns) - 1}\n\n")
        
        # Statistiques descriptives pour les métriques numériques
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        desc_stats = df[numeric_cols].describe()
        
        f.write("STATISTIQUES DESCRIPTIVES\n")
        f.write("-" * 30 + "\n")
        f.write(desc_stats.to_string())
        f.write("\n\n")
        
        # Distribution des puzzles MOSAIC
        if 'is_mosaic_candidate' in df.columns:
            mosaic_count = df['is_mosaic_candidate'].sum()
            f.write(f"PUZZLES MOSAIC: {mosaic_count} ({mosaic_count/len(df)*100:.1f}%)\n\n")
        
        # Top puzzles par complexité
        if 'overall_complexity' in df.columns:
            f.write("TOP 10 PUZZLES PAR COMPLEXITÉ\n")
            f.write("-" * 35 + "\n")
            top_complex = df.nlargest(10, 'overall_complexity')
            for idx, row in top_complex.iterrows():
                f.write(f"{row['puzzle_id']}: {row['overall_complexity']:.2f}\n")
    
    print(f"📄 Résumé statistique exporté: {output_file}")

def main():
    """Point d'entrée principal"""
    
    # Analyser le fichier CSV
    df = analyze_csv_file()
    
    if df is not None:
        # Exporter le résumé
        export_summary_stats(df)
        
        print(f"\n🎯 FICHIER CSV PRÊT POUR VOS ANALYSES")
        print("Vous pouvez maintenant utiliser ce fichier avec:")
        print("- Python/Pandas pour analyses statistiques")
        print("- R pour modélisation avancée") 
        print("- Excel/Google Sheets pour visualisations")
        print("- Outils de Machine Learning (scikit-learn, etc.)")
        
        print(f"\n📁 FICHIERS DISPONIBLES:")
        print("- arc_complete_metrics.csv (données complètes)")
        print("- arc_metrics_summary.txt (résumé statistique)")

if __name__ == "__main__":
    main()