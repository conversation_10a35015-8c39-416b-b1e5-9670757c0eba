#!/usr/bin/env python3
"""
Test du détecteur unifié sur les vrais puzzles MOSAIC identifiés
"""

import numpy as np
import json
import os
from unified_mosaic_detector import UnifiedMosaicDetector

def load_arc_puzzle(puzzle_id, data_dir="../arcdata/training"):
    """Charge un puzzle ARC depuis les fichiers JSON"""
    
    json_file = os.path.join(data_dir, f"{puzzle_id}.json")
    
    try:
        with open(json_file, 'r') as f:
            data = json.load(f)
        
        # Prendre le premier exemple d'entraînement
        if data['train']:
            train_example = data['train'][0]
            input_grid = np.array(train_example['input'])
            output_grid = np.array(train_example['output'])
            return input_grid, output_grid
        
        return None, None
    
    except Exception as e:
        print(f"Erreur chargement {puzzle_id}: {e}")
        return None, None

def test_confirmed_mosaic_puzzles():
    """Test sur les puzzles MOSAIC confirmés par l'analyse humaine"""
    
    # Puzzles MOSAIC confirmés (avec clipboard_PASTE dans les scénarios)
    confirmed_mosaic_ids = [
        "0dfd9992",  # Confirmé
        "29ec7d0e",  # Confirmé mais ensemble de petite mosaic
        "3631a71a",  # Confirmé mais mosaic non centré dans le cadre
        "484b58aa",  # Confirmé
        "9ecd008a",  # Confirmé
        "b8825c91",  # Confirmé
        "c3f564a4",  # Confirmé
        "dc0a314f"   # Confirmé
    ]
    
    # Faux positifs confirmés (pour comparaison)
    false_positive_ids = [
        "780d0b14",  # Couleur regroupé
        "1c786137",  # Que 4 couleur + 1 cadre rouge
        "855e0971",  # Bande de couleur
        "de1cd16c"   # Zone de même couleur
    ]
    
    detector = UnifiedMosaicDetector()
    
    print("🎨 TEST SUR LES VRAIS PUZZLES MOSAIC")
    print("=" * 60)
    
    # Test des vrais MOSAIC
    print(f"\n✅ VRAIS MOSAIC CONFIRMÉS ({len(confirmed_mosaic_ids)} puzzles)")
    print("-" * 50)
    
    true_mosaic_results = []
    
    for puzzle_id in confirmed_mosaic_ids:
        input_grid, output_grid = load_arc_puzzle(puzzle_id)
        
        if input_grid is not None:
            print(f"\n🔍 Analyse de {puzzle_id}:")
            print(f"   Taille: {input_grid.shape}")
            
            results = detector.detect_and_solve(input_grid)
            
            print(f"   Détecté comme mosaïque: {results['is_mosaic']}")
            print(f"   Confiance: {results['confidence']:.1%}")
            print(f"   Blocs sources: {len(results['source_blocks'])}")
            print(f"   Régions cibles: {len(results['target_regions'])}")
            print(f"   Transformations: {len(results['transformations'])}")
            
            if results['detection_evidence']:
                print(f"   Preuves: {', '.join(results['detection_evidence'][:2])}")
            
            true_mosaic_results.append({
                'puzzle_id': puzzle_id,
                'is_mosaic': results['is_mosaic'],
                'confidence': results['confidence'],
                'results': results
            })
        else:
            print(f"❌ Impossible de charger {puzzle_id}")
    
    # Test des faux positifs
    print(f"\n🚫 FAUX POSITIFS CONFIRMÉS ({len(false_positive_ids)} puzzles)")
    print("-" * 50)
    
    false_positive_results = []
    
    for puzzle_id in false_positive_ids:
        input_grid, output_grid = load_arc_puzzle(puzzle_id)
        
        if input_grid is not None:
            print(f"\n🔍 Analyse de {puzzle_id}:")
            print(f"   Taille: {input_grid.shape}")
            
            results = detector.detect_and_solve(input_grid)
            
            print(f"   Détecté comme mosaïque: {results['is_mosaic']}")
            print(f"   Confiance: {results['confidence']:.1%}")
            print(f"   Blocs sources: {len(results['source_blocks'])}")
            print(f"   Régions cibles: {len(results['target_regions'])}")
            
            false_positive_results.append({
                'puzzle_id': puzzle_id,
                'is_mosaic': results['is_mosaic'],
                'confidence': results['confidence'],
                'results': results
            })
        else:
            print(f"❌ Impossible de charger {puzzle_id}")
    
    # Statistiques finales
    print(f"\n📊 STATISTIQUES FINALES")
    print("=" * 40)
    
    # Vrais MOSAIC
    true_detected = sum(1 for r in true_mosaic_results if r['is_mosaic'])
    true_total = len(true_mosaic_results)
    true_recall = true_detected / true_total * 100 if true_total > 0 else 0
    
    print(f"Vrais MOSAIC détectés: {true_detected}/{true_total} ({true_recall:.1f}%)")
    
    if true_mosaic_results:
        avg_confidence_true = sum(r['confidence'] for r in true_mosaic_results) / len(true_mosaic_results)
        print(f"Confiance moyenne (vrais): {avg_confidence_true:.1%}")
    
    # Faux positifs
    false_detected = sum(1 for r in false_positive_results if r['is_mosaic'])
    false_total = len(false_positive_results)
    false_precision = (true_total - false_detected) / true_total * 100 if true_total > 0 else 0
    
    print(f"Faux positifs rejetés: {false_total - false_detected}/{false_total}")
    
    if false_positive_results:
        avg_confidence_false = sum(r['confidence'] for r in false_positive_results) / len(false_positive_results)
        print(f"Confiance moyenne (faux): {avg_confidence_false:.1%}")
    
    # Analyse détaillée d'un cas représentatif
    if true_mosaic_results:
        print(f"\n🔬 ANALYSE DÉTAILLÉE - {true_mosaic_results[0]['puzzle_id']}")
        print("-" * 40)
        detector.print_analysis(true_mosaic_results[0]['results'])
    
    return true_mosaic_results, false_positive_results

def analyze_detection_patterns(true_results, false_results):
    """Analyse les patterns de détection"""
    
    print(f"\n🎯 ANALYSE DES PATTERNS DE DÉTECTION")
    print("=" * 50)
    
    # Caractéristiques des vrais MOSAIC
    if true_results:
        print(f"\n✅ CARACTÉRISTIQUES DES VRAIS MOSAIC:")
        
        confidences = [r['confidence'] for r in true_results]
        source_blocks = [len(r['results']['source_blocks']) for r in true_results]
        target_regions = [len(r['results']['target_regions']) for r in true_results]
        
        print(f"   Confiance: min={min(confidences):.1%}, max={max(confidences):.1%}, moy={sum(confidences)/len(confidences):.1%}")
        print(f"   Blocs sources: min={min(source_blocks)}, max={max(source_blocks)}, moy={sum(source_blocks)/len(source_blocks):.1f}")
        print(f"   Régions cibles: min={min(target_regions)}, max={max(target_regions)}, moy={sum(target_regions)/len(target_regions):.1f}")
    
    # Caractéristiques des faux positifs
    if false_results:
        print(f"\n🚫 CARACTÉRISTIQUES DES FAUX POSITIFS:")
        
        confidences = [r['confidence'] for r in false_results]
        source_blocks = [len(r['results']['source_blocks']) for r in false_results]
        target_regions = [len(r['results']['target_regions']) for r in false_results]
        
        print(f"   Confiance: min={min(confidences):.1%}, max={max(confidences):.1%}, moy={sum(confidences)/len(confidences):.1%}")
        print(f"   Blocs sources: min={min(source_blocks)}, max={max(source_blocks)}, moy={sum(source_blocks)/len(source_blocks):.1f}")
        print(f"   Régions cibles: min={min(target_regions)}, max={max(target_regions)}, moy={sum(target_regions)/len(target_regions):.1f}")

def main():
    """Point d'entrée principal"""
    
    true_results, false_results = test_confirmed_mosaic_puzzles()
    analyze_detection_patterns(true_results, false_results)
    
    print(f"\n🎯 CONCLUSION")
    print("Le détecteur unifié permet d'identifier les caractéristiques")
    print("visuelles qui distinguent les vrais puzzles MOSAIC.")

if __name__ == "__main__":
    main()