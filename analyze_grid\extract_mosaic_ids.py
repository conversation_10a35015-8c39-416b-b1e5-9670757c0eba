#!/usr/bin/env python3
"""
Extracteur simple des IDs de puzzles MOSAIC
Version simplifiée pour éviter les problèmes de sérialisation
"""

import json
import os
import numpy as np
from typing import Dict, List
from analyze_grid.arc_analyzer import <PERSON><PERSON><PERSON><PERSON><PERSON>

def analyze_puzzle_for_mosaic_simple(puzzle_data: Dict, puzzle_id: str) -> Dict:
    """Version simplifiée de l'analyse Mosaic"""
    
    # Analyser les dimensions et couleurs directement
    max_area = 0
    max_colors = 0
    max_height = 0
    max_width = 0
    
    for example in puzzle_data['train']:
        input_grid = np.array(example['input'])
        h, w = input_grid.shape
        area = h * w
        
        # Compter les couleurs uniques (excluant le fond noir)
        unique_colors = set(input_grid.flatten())
        if 0 in unique_colors:
            unique_colors.remove(0)
        color_count = len(unique_colors)
        
        max_area = max(max_area, area)
        max_colors = max(max_colors, color_count)
        max_height = max(max_height, h)
        max_width = max(max_width, w)
    
    # Calculer le score Mosaic simplifié
    area_score = 0.0
    if max_area >= 400:  # 20x20 ou plus
        area_score = 1.0
    elif max_area >= 225:  # 15x15 ou plus
        area_score = 0.8
    elif max_area >= 100:  # 10x10 ou plus
        area_score = 0.5
    elif max_area >= 64:   # 8x8 ou plus
        area_score = 0.3
    
    color_score = 0.0
    if max_colors >= 8:
        color_score = 1.0
    elif max_colors >= 6:
        color_score = 0.8
    elif max_colors >= 5:
        color_score = 0.6
    elif max_colors >= 4:
        color_score = 0.4
    
    mosaic_score = (area_score * 0.6) + (color_score * 0.4)
    
    return {
        'puzzle_id': puzzle_id,
        'max_area': int(max_area),
        'max_colors': int(max_colors),
        'max_height': int(max_height),
        'max_width': int(max_width),
        'mosaic_score': float(mosaic_score),
        'is_mosaic': mosaic_score > 0.7
    }

def main():
    """Extraction simple des puzzles Mosaic"""
    
    print("🔍 EXTRACTION SIMPLE DES PUZZLES MOSAIC")
    print("=" * 50)
    
    data_dir = "../arcdata/training"
    mosaic_puzzles = []
    total_puzzles = 0
    
    # Parcourir tous les fichiers JSON
    for filename in os.listdir(data_dir):
        if not filename.endswith('.json'):
            continue
            
        puzzle_id = filename.replace('.json', '')
        filepath = os.path.join(data_dir, filename)
        
        try:
            with open(filepath, 'r') as f:
                puzzle_data = json.load(f)
            
            # Analyser pour Mosaic
            result = analyze_puzzle_for_mosaic_simple(puzzle_data, puzzle_id)
            total_puzzles += 1
            
            if result['is_mosaic']:
                mosaic_puzzles.append(result)
                print(f"✅ MOSAIC: {puzzle_id} (score: {result['mosaic_score']:.3f})")
                print(f"   {result['max_height']}x{result['max_width']} - {result['max_colors']} couleurs - aire: {result['max_area']}")
            
        except Exception as e:
            print(f"❌ Erreur avec {puzzle_id}: {e}")
            continue
    
    # Trier par score décroissant
    mosaic_puzzles.sort(key=lambda x: x['mosaic_score'], reverse=True)
    
    print(f"\n📊 RÉSULTATS")
    print(f"Total puzzles: {total_puzzles}")
    print(f"Puzzles MOSAIC: {len(mosaic_puzzles)}")
    print(f"Pourcentage: {len(mosaic_puzzles)/total_puzzles*100:.1f}%")
    
    print(f"\n🏆 TOP MOSAIC PUZZLES")
    print("-" * 40)
    for i, puzzle in enumerate(mosaic_puzzles[:15], 1):
        print(f"{i:2d}. {puzzle['puzzle_id']} (score: {puzzle['mosaic_score']:.3f}) - {puzzle['max_height']}x{puzzle['max_width']} - {puzzle['max_colors']} couleurs")
    
    print(f"\n📋 LISTE COMPLÈTE DES IDs MOSAIC:")
    print("-" * 40)
    ids_only = [p['puzzle_id'] for p in mosaic_puzzles]
    
    # Afficher par groupes de 5
    for i in range(0, len(ids_only), 5):
        group = ids_only[i:i+5]
        print(" | ".join(group))
    
    # Sauvegarder la liste simple
    with open("mosaic_puzzle_ids.txt", "w") as f:
        f.write("# PUZZLES DE TYPE MOSAIC DÉTECTÉS\n")
        f.write(f"# Total: {len(mosaic_puzzles)} puzzles sur {total_puzzles} ({len(mosaic_puzzles)/total_puzzles*100:.1f}%)\n")
        f.write("# Critères: Grandes dimensions + Nombreuses couleurs\n\n")
        
        for puzzle in mosaic_puzzles:
            f.write(f"{puzzle['puzzle_id']} # score:{puzzle['mosaic_score']:.3f} {puzzle['max_height']}x{puzzle['max_width']} {puzzle['max_colors']}couleurs\n")
    
    print(f"\n💾 Liste sauvegardée dans: mosaic_puzzle_ids.txt")
    
    # Sauvegarder aussi en JSON simple
    simple_results = {
        'total_puzzles': total_puzzles,
        'mosaic_count': len(mosaic_puzzles),
        'percentage': round(len(mosaic_puzzles)/total_puzzles*100, 1),
        'puzzle_ids': ids_only,
        'detailed_results': mosaic_puzzles
    }
    
    with open("mosaic_puzzles_simple.json", "w") as f:
        json.dump(simple_results, f, indent=2)
    
    print(f"💾 Données détaillées sauvegardées dans: mosaic_puzzles_simple.json")

if __name__ == "__main__":
    main()