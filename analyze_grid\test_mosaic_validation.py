#!/usr/bin/env python3
"""
Test de validation du MosaicSolver pour détecter les faux positifs
"""

import numpy as np
from MosaicSolver import MosaicSolver

def test_false_positives():
    """Test le MosaicSolver sur des grilles qui ne sont clairement PAS des mosaïques"""
    
    solver = MosaicSolver(empty_value=0, min_block_size=2)
    
    print("=" * 80)
    print("TEST DE VALIDATION - DÉTECTION DE FAUX POSITIFS")
    print("=" * 80)
    
    # Test 1: Grille complètement aléatoire
    print("\n🎲 TEST 1: Grille complètement aléatoire (10x10)")
    np.random.seed(42)
    random_grid = np.random.randint(0, 5, (10, 10))
    
    result1 = solver.analyze_mosaic(random_grid)
    print(f"   Résultat: {'✅ Mosaïque' if result1['is_mosaic'] else '❌ Pas mosaïque'} (confiance: {result1['confidence']:.2f})")
    print(f"   Transformations proposées: {len(result1['proposed_transformations'])}")
    
    # Test 2: Grille avec pattern simple (pas de mosaïque)
    print("\n📐 TEST 2: Grille avec lignes horizontales (8x8)")
    lines_grid = np.zeros((8, 8))
    lines_grid[1, :] = 1
    lines_grid[3, :] = 2
    lines_grid[5, :] = 3
    lines_grid[7, :] = 4
    
    result2 = solver.analyze_mosaic(lines_grid)
    print(f"   Résultat: {'✅ Mosaïque' if result2['is_mosaic'] else '❌ Pas mosaïque'} (confiance: {result2['confidence']:.2f})")
    print(f"   Transformations proposées: {len(result2['proposed_transformations'])}")
    
    # Test 3: Grille avec beaucoup de zéros (comme ARC)
    print("\n🕳️ TEST 3: Grille sparse comme ARC (15x15)")
    sparse_grid = np.zeros((15, 15))
    # Ajouter quelques éléments épars
    sparse_grid[2, 3] = 1
    sparse_grid[5, 8] = 2
    sparse_grid[10, 12] = 3
    sparse_grid[7, 2] = 4
    sparse_grid[12, 6] = 5
    
    result3 = solver.analyze_mosaic(sparse_grid)
    print(f"   Résultat: {'✅ Mosaïque' if result3['is_mosaic'] else '❌ Pas mosaïque'} (confiance: {result3['confidence']:.2f})")
    print(f"   Transformations proposées: {len(result3['proposed_transformations'])}")
    
    # Test 4: Vraie mosaïque simple pour contrôle
    print("\n✅ TEST 4: Vraie mosaïque simple (contrôle) (6x6)")
    true_mosaic = np.array([
        [1, 2, 0, 0, 0, 0],
        [3, 4, 0, 0, 0, 0],
        [0, 0, 0, 0, 0, 0],
        [0, 0, 0, 0, 0, 0],
        [0, 0, 0, 0, 2, 1],
        [0, 0, 0, 0, 4, 3]
    ])
    
    result4 = solver.analyze_mosaic(true_mosaic)
    print(f"   Résultat: {'✅ Mosaïque' if result4['is_mosaic'] else '❌ Pas mosaïque'} (confiance: {result4['confidence']:.2f})")
    print(f"   Transformations proposées: {len(result4['proposed_transformations'])}")
    
    # Test 5: Grille de la taille de 3631a71a avec pattern aléatoire
    print("\n🎯 TEST 5: Grille 30x30 aléatoire (comme 3631a71a)")
    np.random.seed(123)
    large_random = np.random.choice([0, 1, 2, 3, 4, 5], (30, 30), p=[0.6, 0.08, 0.08, 0.08, 0.08, 0.08])
    
    result5 = solver.analyze_mosaic(large_random)
    print(f"   Résultat: {'✅ Mosaïque' if result5['is_mosaic'] else '❌ Pas mosaïque'} (confiance: {result5['confidence']:.2f})")
    print(f"   Transformations proposées: {len(result5['proposed_transformations'])}")
    
    # Analyse des résultats
    print("\n" + "=" * 50)
    print("ANALYSE DES RÉSULTATS:")
    
    false_positives = 0
    tests = [result1, result2, result3, result5]  # Exclure le vrai contrôle
    test_names = ["Aléatoire 10x10", "Lignes horizontales", "Sparse ARC", "Aléatoire 30x30"]
    
    for i, (result, name) in enumerate(zip(tests, test_names)):
        if result['is_mosaic']:
            false_positives += 1
            print(f"❌ FAUX POSITIF: {name} détecté comme mosaïque (conf: {result['confidence']:.2f})")
        else:
            print(f"✅ CORRECT: {name} correctement rejeté")
    
    print(f"\n📊 BILAN:")
    print(f"   Faux positifs: {false_positives}/4 tests ({false_positives/4*100:.1f}%)")
    print(f"   Vrai contrôle: {'✅ Détecté' if result4['is_mosaic'] else '❌ Raté'}")
    
    if false_positives > 0:
        print(f"\n⚠️  CONCLUSION: Le MosaicSolver génère des faux positifs!")
        print(f"   Il trouve des 'mosaïques' là où il n'y en a pas.")
    else:
        print(f"\n✅ CONCLUSION: Le MosaicSolver semble fiable.")
    
    return false_positives

if __name__ == "__main__":
    false_positives = test_false_positives()