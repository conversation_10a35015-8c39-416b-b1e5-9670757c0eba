#!/usr/bin/env python3
"""
Visualiseur comparatif pour les fichiers d'analyse JSON
Permet une comparaison humaine entre PureGridAnalyzer et ARCAnalyzer
"""

import json
import sys
from pathlib import Path
from typing import Dict, Any, List
import pandas as pd

class JSONVisualizer:
    """Visualiseur pour comparer les analyses JSON"""
    
    def __init__(self, base_path: str = "../arcdata/training"):
        self.base_path = Path(base_path)
        
    def load_json(self, filename: str) -> Dict[str, Any]:
        """Charge un fichier JSON"""
        try:
            with open(self.base_path / filename, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"❌ Erreur chargement {filename}: {e}")
            return {}
    
    def display_comparison(self, task_id: str):
        """Affiche une comparaison visuelle entre les deux analyses"""
        
        # Charger les fichiers
        pure_data = self.load_json(f"{task_id}_analysis.json")
        arc_data = self.load_json(f"{task_id}_arc_analysis.json")
        
        if not pure_data or not arc_data:
            return
        
        print("=" * 80)
        print(f"📊 COMPARAISON VISUELLE - Tâche {task_id}")
        print("=" * 80)
        
        # 1. Vue d'ensemble
        print("\n🎯 VUE D'ENSEMBLE")
        print("-" * 40)
        
        print(f"\n📋 PureGridAnalyzer:")
        print(f"   • Exemples analysés: {pure_data.get('n_train_examples', 'N/A')}")
        print(f"   • Type principal: {pure_data.get('puzzle_summary', {}).get('dominant_transformation_type', 'N/A')}")
        print(f"   • Ratio taille: {pure_data.get('puzzle_summary', {}).get('average_size_ratio', 'N/A')}")
        print(f"   • Complexité: {pure_data.get('puzzle_summary', {}).get('average_complexity_change', 'N/A')}")
        
        print(f"\n🔍 ARCAnalyzer:")
        print(f"   • Exemples analysés: {arc_data.get('analysis', {}).get('grid_info', {}).get('train_examples', 'N/A')}")
        print(f"   • Type principal: {', '.join(arc_data.get('analysis', {}).get('transformations', {}).get('geometric', []))}")
        print(f"   • Complexité: {arc_data.get('analysis', {}).get('complexity', {}).get('overall_complexity', 'N/A')}")
        
        # 2. Transformations
        print("\n🔄 TRANSFORMATIONS")
        print("-" * 40)
        
        # PureGridAnalyzer
        if 'puzzle_summary' in pure_data:
            summary = pure_data['puzzle_summary']
            print("\n📊 PureGridAnalyzer:")
            print(f"   • Type: {summary.get('dominant_transformation_type', 'N/A')}")
            print(f"   • Ratio: {summary.get('average_size_ratio', 'N/A')}x")
            print(f"   • Consistance: {summary.get('consistency_score', 'N/A')}")
            
            # Insights calculés
            insights = summary.get('computed_insights', {})
            print("   • Insights:")
            for insight, value in insights.items():
                status = "✓" if value else "✗"
                print(f"     {status} {insight}")
        
        # ARCAnalyzer
        if 'analysis' in arc_data:
            analysis = arc_data['analysis']
            print("\n🔍 ARCAnalyzer:")
            print(f"   • Dimensions: {analysis.get('grid_info', {}).get('input_dimensions', 'N/A')} → {analysis.get('grid_info', {}).get('output_dimensions', 'N/A')}")
            print(f"   • Transformations: {', '.join(analysis.get('transformations', {}).get('geometric', []))}")
            print(f"   • Structurelles: {', '.join(analysis.get('transformations', {}).get('structural', []))}")
        
        # 3. Commandes AGI
        print("\n🤖 COMMANDES AGI")
        print("-" * 40)
        
        # PureGridAnalyzer
        if 'agi_mapping' in pure_data:
            agi = pure_data['agi_mapping']
            print("\n📋 PureGridAnalyzer:")
            print(f"   • Confiance: {agi.get('confidence_level', 'N/A')}")
            print(f"   • Commandes: {', '.join(agi.get('possible_commands', []))}")
            if 'command_parameters' in agi:
                for cmd, param in agi['command_parameters'].items():
                    print(f"     - {cmd}: {param}")
        
        # 4. Détails techniques
        print("\n⚙️ DÉTAILS TECHNIQUES")
        print("-" * 40)
        
        # PureGridAnalyzer - exemples
        if 'train_examples' in pure_data:
            examples = pure_data['train_examples'][:2]  # Limiter à 2 exemples
            print("\n📊 PureGridAnalyzer - Exemples:")
            for i, ex in enumerate(examples, 1):
                print(f"\n   Exemple {i}:")
                print(f"     • Input: {ex.get('input_shape', 'N/A')}")
                print(f"     • Output: {ex.get('output_shape', 'N/A')}")
                print(f"     • Patterns: {', '.join(ex.get('special_patterns', []))}")
        
        # ARCAnalyzer - objets
        if 'analysis' in arc_data:
            objects = arc_data['analysis'].get('objects', {})
            print("\n🔍 ARCAnalyzer - Objets:")
            input_objs = objects.get('input_objects', [])
            output_objs = objects.get('output_objects', [])
            print(f"   • Objets input: {sum(len(objs) for objs in input_objs)}")
            print(f"   • Objets output: {sum(len(objs) for objs in output_objs)}")
        
        # 5. Résumé pour humain
        print("\n📝 RÉSUMÉ POUR HUMAIN")
        print("=" * 80)
        print("\n✅ CE QUE VOUS DEVEZ SAVOIR:")
        print("\n1. **Transformation identique** : Les deux analyseurs confirment une multiplication")
        print("   uniforme 3x3 → 9x9")
        print("\n2. **PureGridAnalyzer** : Plus simple, donne directement les commandes AGI")
        print("   à utiliser (MULTIPLY 3, COPY, PASTE...)")
        print("\n3. **ARCAnalyzer** : Plus détaillé, montre comment les objets se transforment")
        print("   et donne des métriques précises")
        print("\n4. **Utilisation** : Utilisez PureGridAnalyzer pour générer des solutions,")
        print("   ARCAnalyzer pour comprendre en détail.")
        
        # 6. Commande pour comparer les fichiers
        print("\n💻 COMMANDES UTILES:")
        print("-" * 40)
        print(f"python visualize_comparison.py {task_id}")
        print("python -m json.tool 007bbfb7_analysis.json")
        print("python -m json.tool 007bbfb7_arc_analysis.json")
        
    def create_summary_table(self, task_id: str) -> pd.DataFrame:
        """Crée un tableau récapitulatif"""
        
        pure_data = self.load_json(f"{task_id}_analysis.json")
        arc_data = self.load_json(f"{task_id}_arc_analysis.json")
        
        summary = {
            "Aspect": [
                "Type de transformation",
                "Ratio de taille",
                "Complexité",
                "Commandes AGI",
                "Confiance",
                "Patterns détectés"
            ],
            "PureGridAnalyzer": [
                pure_data.get('puzzle_summary', {}).get('dominant_transformation_type', 'N/A'),
                f"{pure_data.get('puzzle_summary', {}).get('average_size_ratio', 'N/A')}x",
                f"{pure_data.get('puzzle_summary', {}).get('average_complexity_change', 'N/A'):.1f}",
                len(pure_data.get('agi_mapping', {}).get('possible_commands', [])),
                pure_data.get('agi_mapping', {}).get('confidence_level', 'N/A'),
                len(pure_data.get('puzzle_summary', {}).get('common_patterns', []))
            ],
            "ARCAnalyzer": [
                ', '.join(arc_data.get('analysis', {}).get('transformations', {}).get('geometric', [])),
                "3.0x",
                f"{arc_data.get('analysis', {}).get('complexity', {}).get('overall_complexity', 'N/A'):.1f}",
                len(arc_data.get('analysis', {}).get('transformations', {}).get('geometric', [])) + 
                len(arc_data.get('analysis', {}).get('transformations', {}).get('structural', [])),
                "HIGH",
                len(arc_data.get('analysis', {}).get('patterns', {}).get('scaling', {}).get('factors', []))
            ]
        }
        
        return pd.DataFrame(summary)

def main():
    """Fonction principale"""
    if len(sys.argv) > 1:
        task_id = sys.argv[1]
    else:
        task_id = "007bbfb7"
    
    visualizer = JSONVisualizer()
    visualizer.display_comparison(task_id)
    
    # Optionnel : créer un tableau
    try:
        df = visualizer.create_summary_table(task_id)
        print("\n📊 TABLEAU RÉCAPITULATIF")
        print("-" * 40)
        print(df.to_string(index=False))
    except:
        pass

if __name__ == "__main__":
    main()