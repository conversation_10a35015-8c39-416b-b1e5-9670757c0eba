#!/usr/bin/env python3
"""
Script pour tester l'ARCAnalyzer amélioré sur l'ensemble d'entraînement
"""

import sys
import os
import json
import time
from pathlib import Path
from collections import Counter, defaultdict

sys.path.append('AnalysesGrilles')
from analyze_grid.arc_analyzer import ARCAnalyzer

def load_training_puzzles(training_dir="arcdata/training", max_puzzles=None):
    """Charge les puzzles d'entraînement"""
    training_path = Path(training_dir)
    if not training_path.exists():
        print(f"Erreur: Le répertoire {training_dir} n'existe pas")
        return []
    
    # Filtrer uniquement les fichiers de tâches ARC (sans underscore)
    all_json_files = list(training_path.glob("*.json"))
    puzzle_files = [f for f in all_json_files if '_' not in f.stem]
    if max_puzzles:
        puzzle_files = puzzle_files[:max_puzzles]
    
    puzzles = []
    for file_path in puzzle_files:
        try:
            with open(file_path, 'r') as f:
                puzzle_data = json.load(f)
                puzzle_data['id'] = file_path.stem
                puzzles.append(puzzle_data)
        except Exception as e:
            print(f"Erreur lors du chargement de {file_path}: {e}")
    
    return puzzles

def analyze_training_set(max_puzzles=50):
    """Analyse l'ensemble d'entraînement avec l'ARCAnalyzer amélioré"""
    
    print("=== TEST DE L'ARCANALYZER AMÉLIORÉ SUR L'ENSEMBLE D'ENTRAÎNEMENT ===")
    print(f"Chargement des puzzles (max: {max_puzzles})...")
    
    puzzles = load_training_puzzles(max_puzzles=max_puzzles)
    if not puzzles:
        print("Aucun puzzle trouvé!")
        return
    
    print(f"Puzzles chargés: {len(puzzles)}")
    
    analyzer = ARCAnalyzer()
    results = {
        'total_puzzles': len(puzzles),
        'successful_analyses': 0,
        'failed_analyses': 0,
        'statistics': {
            'diff_compatible': 0,
            'transformation_types': Counter(),
            'complexity_scores': [],
            'object_counts': {'input': [], 'output': []},
            'regionprops_features': {
                'total_objects': 0,
                'average_areas': [],
                'shape_distributions': Counter()
            }
        },
        'detailed_results': []
    }
    
    start_time = time.time()
    
    for i, puzzle in enumerate(puzzles):
        puzzle_id = puzzle['id']
        print(f"Analyse {i+1}/{len(puzzles)}: {puzzle_id}")
        
        try:
            # Analyser le puzzle
            analysis = analyzer.analyze_puzzle(puzzle)
            
            # Structure améliorée
            enhanced_structure = analyzer.get_enhanced_analysis_structure(analysis)
            
            # Collecter les statistiques
            results['successful_analyses'] += 1
            
            # Diff analysis
            if 'diff_analysis' in analysis:
                diff_data = analysis['diff_analysis']
                if diff_data.get('compatible_examples', 0) > 0:
                    results['statistics']['diff_compatible'] += 1
            
            # Type de transformation
            primary_type = enhanced_structure['summary']['primary_transformation_type']
            results['statistics']['transformation_types'][primary_type] += 1
            
            # Complexité
            complexity = enhanced_structure['enhanced_analysis']['complexity_metrics']['overall_complexity']
            results['statistics']['complexity_scores'].append(complexity)
            
            # Objets avec regionprops
            if 'enhanced_objects' in analysis:
                enhanced_objs = analysis['enhanced_objects']
                stats = enhanced_objs.get('object_statistics', {})
                
                results['statistics']['regionprops_features']['total_objects'] += stats.get('total_input_objects', 0)
                results['statistics']['regionprops_features']['total_objects'] += stats.get('total_output_objects', 0)
                
                if stats.get('average_object_area', 0) > 0:
                    results['statistics']['regionprops_features']['average_areas'].append(stats['average_object_area'])
                
                # Distribution des formes
                shapes = stats.get('common_shapes', {})
                for shape, count in shapes.items():
                    results['statistics']['regionprops_features']['shape_distributions'][shape] += count
            
            # Résultat détaillé
            puzzle_result = {
                'id': puzzle_id,
                'success': True,
                'primary_transformation': primary_type,
                'confidence': enhanced_structure['summary']['confidence_score'],
                'complexity': complexity,
                'compatible_examples': analysis.get('diff_analysis', {}).get('compatible_examples', 0),
                'total_examples': len(puzzle['train'])
            }
            
            results['detailed_results'].append(puzzle_result)
            
        except Exception as e:
            print(f"  Erreur: {e}")
            results['failed_analyses'] += 1
            results['detailed_results'].append({
                'id': puzzle_id,
                'success': False,
                'error': str(e)
            })
    
    # Calculer les statistiques finales
    elapsed_time = time.time() - start_time
    
    if results['statistics']['complexity_scores']:
        results['statistics']['average_complexity'] = sum(results['statistics']['complexity_scores']) / len(results['statistics']['complexity_scores'])
    
    if results['statistics']['regionprops_features']['average_areas']:
        areas = results['statistics']['regionprops_features']['average_areas']
        results['statistics']['regionprops_features']['global_average_area'] = sum(areas) / len(areas)
    
    # Afficher le rapport
    print("\n" + "="*60)
    print("RAPPORT D'ANALYSE DE L'ENSEMBLE D'ENTRAÎNEMENT")
    print("="*60)
    
    print(f"Puzzles analysés: {results['total_puzzles']}")
    print(f"Analyses réussies: {results['successful_analyses']}")
    print(f"Analyses échouées: {results['failed_analyses']}")
    print(f"Taux de réussite: {results['successful_analyses']/results['total_puzzles']*100:.1f}%")
    print(f"Temps d'exécution: {elapsed_time:.1f}s")
    print(f"Temps moyen par puzzle: {elapsed_time/results['total_puzzles']:.2f}s")
    
    print(f"\nPuzzles avec transformations compatibles (diff): {results['statistics']['diff_compatible']}")
    print(f"Complexité moyenne: {results['statistics'].get('average_complexity', 0):.1f}")
    
    print("\nTypes de transformations détectés:")
    for trans_type, count in results['statistics']['transformation_types'].most_common():
        percentage = count / results['successful_analyses'] * 100
        print(f"  {trans_type}: {count} ({percentage:.1f}%)")
    
    print(f"\nObjets analysés avec regionprops: {results['statistics']['regionprops_features']['total_objects']}")
    if results['statistics']['regionprops_features'].get('global_average_area'):
        print(f"Aire moyenne des objets: {results['statistics']['regionprops_features']['global_average_area']:.1f}")
    
    print("\nDistribution des formes:")
    for shape, count in results['statistics']['regionprops_features']['shape_distributions'].items():
        print(f"  {shape}: {count}")
    
    # Sauvegarder les résultats
    output_file = f"enhanced_analyzer_training_results_{int(time.time())}.json"
    with open(output_file, 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"\nRésultats détaillés sauvegardés dans: {output_file}")
    
    return results

if __name__ == "__main__":
    # Test sur un échantillon de puzzles
    analyze_training_set(max_puzzles=20)
