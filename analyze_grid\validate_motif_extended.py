#!/usr/bin/env python3
"""
Validation étendue de la détection MOTIF sur un échantillon des 119 puzzles
"""

import json
import os
from analyze_grid.arc_analyzer import ARCAnalyzer
from typing import Dict, List

def load_motif_puzzles() -> List[str]:
    """Charge la liste des puzzles MOTIF depuis l'analyse précédente"""
    try:
        with open('motif_analysis_results.json', 'r') as f:
            data = json.load(f)
            return data['all_motif_puzzles']
    except FileNotFoundError:
        print("❌ Fichier motif_analysis_results.json non trouvé")
        return []

def test_puzzle_motif(puzzle_id: str) -> Dict:
    """Test la détection MOTIF sur un puzzle spécifique"""
    json_file = f"../arcdata/training/{puzzle_id}.json"
    agi_file = f"../arcdata/training/{puzzle_id}_TEST0_VALID.agi"
    
    if not os.path.exists(json_file):
        return {"error": f"Fichier JSON manquant: {puzzle_id}"}
    
    try:
        # Charger puzzle
        with open(json_file, 'r') as f:
            puzzle = json.load(f)
        
        # Analyser avec ARCAnalyzer
        analyzer = ARCAnalyzer()
        analysis = analyzer.analyze_puzzle(puzzle)
        motif_result = analysis['patterns']['motif']
        
        # Charger solution AGI pour validation
        agi_commands = []
        if os.path.exists(agi_file):
            with open(agi_file, 'r', encoding='utf-8') as f:
                content = f.read()
                for line in content.split('\n'):
                    if 'MOTIF' in line:
                        agi_commands.append(line.strip())
        
        # Analyser dimensions pour contexte
        dimensions = []
        for example in puzzle['train']:
            input_shape = (len(example['input']), len(example['input'][0]))
            output_shape = (len(example['output']), len(example['output'][0]))
            ratio = (output_shape[0] / input_shape[0], output_shape[1] / input_shape[1])
            dimensions.append({
                'input': input_shape,
                'output': output_shape,
                'ratio': ratio,
                'is_multiple': (output_shape[0] % input_shape[0] == 0 and 
                               output_shape[1] % input_shape[1] == 0)
            })
        
        return {
            'puzzle_id': puzzle_id,
            'motif_detected': motif_result['detected'],
            'motif_type': motif_result['type'],
            'confidence': motif_result['confidence'],
            'tiling_factor': motif_result.get('tiling_factor'),
            'agi_commands': agi_commands,
            'dimensions': dimensions,
            'success': True
        }
        
    except Exception as e:
        return {"error": f"Erreur analyse {puzzle_id}: {e}"}

def analyze_results(results: List[Dict]) -> Dict:
    """Analyse les résultats de validation"""
    stats = {
        'total_tested': len(results),
        'successful_tests': 0,
        'motif_detected': 0,
        'tiling_detected': 0,
        'detached_detected': 0,
        'high_confidence': 0,
        'medium_confidence': 0,
        'low_confidence': 0,
        'errors': 0,
        'dimension_patterns': {},
        'problematic_cases': []
    }
    
    for result in results:
        if 'error' in result:
            stats['errors'] += 1
            continue
            
        stats['successful_tests'] += 1
        
        if result['motif_detected']:
            stats['motif_detected'] += 1
            
            if result['motif_type'] == 'tiling':
                stats['tiling_detected'] += 1
            elif result['motif_type'] == 'detached':
                stats['detached_detected'] += 1
            
            # Analyse confiance
            confidence = result['confidence']
            if confidence >= 0.8:
                stats['high_confidence'] += 1
            elif confidence >= 0.5:
                stats['medium_confidence'] += 1
            else:
                stats['low_confidence'] += 1
                stats['problematic_cases'].append({
                    'puzzle_id': result['puzzle_id'],
                    'confidence': confidence,
                    'type': result['motif_type']
                })
        
        # Analyser patterns de dimensions
        for dim in result['dimensions']:
            ratio_key = f"{dim['ratio'][0]:.1f}x{dim['ratio'][1]:.1f}"
            if ratio_key not in stats['dimension_patterns']:
                stats['dimension_patterns'][ratio_key] = 0
            stats['dimension_patterns'][ratio_key] += 1
    
    return stats

def main():
    """Validation étendue principale"""
    print("🔍 VALIDATION ÉTENDUE DÉTECTION MOTIF")
    print("=" * 50)
    
    # Charger liste puzzles MOTIF
    motif_puzzles = load_motif_puzzles()
    if not motif_puzzles:
        print("❌ Impossible de charger la liste des puzzles MOTIF")
        return
    
    print(f"📋 {len(motif_puzzles)} puzzles MOTIF identifiés")
    
    # Tester sur un échantillon représentatif
    sample_size = min(20, len(motif_puzzles))  # Tester 20 puzzles max
    sample_puzzles = motif_puzzles[:sample_size]
    
    print(f"🧪 Test sur échantillon de {sample_size} puzzles...")
    
    results = []
    for i, puzzle_id in enumerate(sample_puzzles):
        print(f"  [{i+1}/{sample_size}] {puzzle_id}...", end=" ")
        result = test_puzzle_motif(puzzle_id)
        results.append(result)
        
        if 'error' in result:
            print("❌")
        elif result['motif_detected']:
            print(f"✅ {result['motif_type']} ({result['confidence']:.2f})")
        else:
            print("⚠️ Non détecté")
    
    # Analyser résultats
    print("\n📊 ANALYSE DES RÉSULTATS")
    print("=" * 30)
    
    stats = analyze_results(results)
    
    print(f"Tests réussis: {stats['successful_tests']}/{stats['total_tested']}")
    print(f"MOTIF détectés: {stats['motif_detected']}/{stats['successful_tests']} ({stats['motif_detected']/stats['successful_tests']*100:.1f}%)")
    print(f"  - Tiling: {stats['tiling_detected']}")
    print(f"  - Détachés: {stats['detached_detected']}")
    
    print(f"\nConfiance:")
    print(f"  - Haute (≥0.8): {stats['high_confidence']}")
    print(f"  - Moyenne (≥0.5): {stats['medium_confidence']}")
    print(f"  - Faible (<0.5): {stats['low_confidence']}")
    
    if stats['problematic_cases']:
        print(f"\n⚠️ Cas problématiques ({len(stats['problematic_cases'])}):")
        for case in stats['problematic_cases']:
            print(f"  - {case['puzzle_id']}: {case['type']} (conf: {case['confidence']:.2f})")
    
    print(f"\nPatterns de dimensions:")
    sorted_patterns = sorted(stats['dimension_patterns'].items(), key=lambda x: x[1], reverse=True)
    for pattern, count in sorted_patterns[:10]:  # Top 10
        print(f"  - {pattern}: {count} occurrences")
    
    # Sauvegarder résultats détaillés
    with open('motif_validation_extended.json', 'w') as f:
        json.dump({
            'statistics': stats,
            'detailed_results': results,
            'sample_size': sample_size,
            'total_motif_puzzles': len(motif_puzzles)
        }, f, indent=2)
    
    print(f"\n💾 Résultats détaillés sauvegardés dans 'motif_validation_extended.json'")
    
    # Évaluation globale
    detection_rate = stats['motif_detected'] / stats['successful_tests'] if stats['successful_tests'] > 0 else 0
    high_conf_rate = stats['high_confidence'] / stats['motif_detected'] if stats['motif_detected'] > 0 else 0
    
    print(f"\n🎯 ÉVALUATION GLOBALE")
    print(f"Taux de détection: {detection_rate*100:.1f}%")
    print(f"Taux haute confiance: {high_conf_rate*100:.1f}%")
    
    if detection_rate >= 0.7 and high_conf_rate >= 0.6:
        print("✅ Performance satisfaisante")
    elif detection_rate >= 0.5:
        print("⚠️ Performance acceptable, améliorations possibles")
    else:
        print("❌ Performance insuffisante, révision nécessaire")

if __name__ == "__main__":
    main()