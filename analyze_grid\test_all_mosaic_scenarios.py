#!/usr/bin/env python3
"""
Test du générateur de commandes MOTIF sur toutes les tâches mosaïques détectées
Génère des scénarios de test pour validation manuelle
"""

import json
from pathlib import Path
from motif_command_generator import MotifCommandGenerator

def test_all_mosaic_tasks():
    """
    Test le générateur sur toutes les tâches identifiées comme mosaïques
    """
    # Tâches identifiées comme mosaïques par le StrictMosaicDetector
    mosaic_tasks = [
        "0dfd9992",  # <PERSON><PERSON><PERSON><PERSON> (0.92)
        "29ec7d0e",  # <PERSON><PERSON><PERSON><PERSON> (0.92) 
        "484b58aa",  # <PERSON><PERSON>ï<PERSON> (0.92)
        "b8825c91",  # <PERSON><PERSON><PERSON><PERSON> (1.03)
        "c3f564a4",  # <PERSON><PERSON>ïque (0.92)
        "3631a71a"   # Cas de référence (symétrie diagonale confirmée)
    ]
    
    generator = MotifCommandGenerator()
    all_results = {}
    
    print("=" * 100)
    print("TEST COMPLET - GÉNÉRATION DE SCÉNARIOS MOTIF POUR TOUTES LES MOSAÏQUES")
    print("=" * 100)
    
    for task_id in mosaic_tasks:
        print(f"\n{'='*80}")
        print(f"🎯 TÂCHE {task_id}")
        print(f"{'='*80}")
        
        try:
            results = generator.analyze_and_generate_motifs(task_id)
            all_results[task_id] = results
            
            # Générer le scénario de test
            generate_test_scenario(task_id, results)
            
        except Exception as e:
            print(f"❌ Erreur pour {task_id}: {e}")
            all_results[task_id] = {'error': str(e)}
    
    # Générer le rapport de synthèse
    generate_synthesis_report(all_results)
    
    # Sauvegarder tous les résultats
    save_all_results(all_results)
    
    return all_results

def generate_test_scenario(task_id: str, results: dict):
    """
    Génère un scénario de test spécifique pour une tâche
    """
    print(f"\n📋 SCÉNARIO DE TEST POUR {task_id}:")
    print("-" * 50)
    
    if not results.get('motif_commands'):
        print("❌ Aucune commande MOTIF générée - pas de scénario de test")
        return
    
    # Informations sur la tâche
    total_examples = len(results['examples_analysis'])
    total_motifs = sum(ex['motifs_found'] for ex in results['examples_analysis'])
    consistency = results['pattern_consistency']
    
    print(f"📊 Statistiques:")
    print(f"   • Exemples analysés: {total_examples}")
    print(f"   • Total commandes générées: {total_motifs}")
    print(f"   • Cohérence des patterns: {consistency:.2f}")
    print(f"   • Commandes consolidées: {len(results['motif_commands'])}")
    
    print(f"\n🔧 Commandes MOTIF à tester:")
    for i, command in enumerate(results['motif_commands'], 1):
        print(f"   {i}. {command}")
    
    # Analyse des types de transformation
    transformation_types = analyze_transformation_types(results['motif_commands'])
    print(f"\n🔄 Types de transformations détectées:")
    for trans_type, count in transformation_types.items():
        print(f"   • {trans_type}: {count} occurrences")
    
    # Recommandations de test
    print(f"\n✅ Recommandations de test:")
    if consistency > 0.8:
        print(f"   • Haute cohérence ({consistency:.2f}) - tester toutes les commandes")
    elif consistency > 0.6:
        print(f"   • Cohérence modérée ({consistency:.2f}) - tester les commandes principales")
    else:
        print(f"   • Faible cohérence ({consistency:.2f}) - vérifier manuellement")
    
    if total_motifs > 20:
        print(f"   • Beaucoup de commandes ({total_motifs}) - possibles redondances")
    
    # Complexité estimée
    complexity = estimate_complexity(results)
    print(f"   • Complexité estimée: {complexity}")

def analyze_transformation_types(commands: list) -> dict:
    """
    Analyse les types de transformations dans les commandes
    """
    types = {
        'COPY': 0,
        'FLIP HORIZONTAL': 0,
        'FLIP VERTICAL': 0,
        'ROTATE 90': 0,
        'ROTATE 180': 0,
        'ROTATE 270': 0
    }
    
    for command in commands:
        for trans_type in types.keys():
            if trans_type in command:
                types[trans_type] += 1
    
    # Retourner seulement les types utilisés
    return {k: v for k, v in types.items() if v > 0}

def estimate_complexity(results: dict) -> str:
    """
    Estime la complexité de la mosaïque
    """
    total_motifs = sum(ex['motifs_found'] for ex in results['examples_analysis'])
    consistency = results['pattern_consistency']
    unique_commands = len(results['motif_commands'])
    
    if total_motifs > 30 or unique_commands > 8:
        return "ÉLEVÉE"
    elif total_motifs > 15 or unique_commands > 4:
        return "MODÉRÉE"
    else:
        return "FAIBLE"

def generate_synthesis_report(all_results: dict):
    """
    Génère un rapport de synthèse de tous les tests
    """
    print(f"\n{'='*100}")
    print("RAPPORT DE SYNTHÈSE - TOUS LES SCÉNARIOS MOTIF")
    print("=" * 100)
    
    successful_tasks = {k: v for k, v in all_results.items() if 'error' not in v}
    
    print(f"\n📊 STATISTIQUES GLOBALES:")
    print(f"   • Tâches testées: {len(all_results)}")
    print(f"   • Tâches réussies: {len(successful_tasks)}")
    print(f"   • Taux de succès: {len(successful_tasks)/len(all_results)*100:.1f}%")
    
    # Analyse par complexité
    complexity_stats = {'FAIBLE': 0, 'MODÉRÉE': 0, 'ÉLEVÉE': 0}
    consistency_scores = []
    total_commands = 0
    
    for task_id, results in successful_tasks.items():
        if results.get('motif_commands'):
            complexity = estimate_complexity(results)
            complexity_stats[complexity] += 1
            consistency_scores.append(results['pattern_consistency'])
            total_commands += len(results['motif_commands'])
    
    print(f"\n🎯 RÉPARTITION PAR COMPLEXITÉ:")
    for complexity, count in complexity_stats.items():
        if count > 0:
            print(f"   • {complexity}: {count} tâches")
    
    if consistency_scores:
        avg_consistency = sum(consistency_scores) / len(consistency_scores)
        print(f"\n📈 COHÉRENCE MOYENNE: {avg_consistency:.2f}")
        print(f"📋 TOTAL COMMANDES GÉNÉRÉES: {total_commands}")
    
    # Top tâches par nombre de commandes
    print(f"\n🏆 TOP TÂCHES PAR NOMBRE DE COMMANDES:")
    task_command_counts = []
    for task_id, results in successful_tasks.items():
        if results.get('motif_commands'):
            count = len(results['motif_commands'])
            task_command_counts.append((task_id, count, results['pattern_consistency']))
    
    task_command_counts.sort(key=lambda x: x[1], reverse=True)
    
    for i, (task_id, count, consistency) in enumerate(task_command_counts[:5], 1):
        print(f"   {i}. {task_id}: {count} commandes (cohérence: {consistency:.2f})")
    
    # Recommandations de test prioritaires
    print(f"\n✅ RECOMMANDATIONS DE TEST PRIORITAIRES:")
    
    # Tâches avec haute cohérence et complexité modérée
    priority_tasks = []
    for task_id, results in successful_tasks.items():
        if results.get('motif_commands'):
            consistency = results['pattern_consistency']
            complexity = estimate_complexity(results)
            if consistency > 0.7 and complexity in ['FAIBLE', 'MODÉRÉE']:
                priority_tasks.append((task_id, consistency, complexity))
    
    priority_tasks.sort(key=lambda x: x[1], reverse=True)
    
    if priority_tasks:
        print(f"   🎯 Tâches prioritaires (haute cohérence, complexité gérable):")
        for task_id, consistency, complexity in priority_tasks:
            print(f"      • {task_id}: cohérence {consistency:.2f}, complexité {complexity}")
    else:
        print(f"   ⚠️  Toutes les tâches nécessitent une validation approfondie")

def save_all_results(all_results: dict):
    """
    Sauvegarde tous les résultats dans un fichier JSON
    """
    output_file = "all_mosaic_scenarios_results.json"
    
    # Convertir les types numpy si nécessaire
    def convert_numpy_types(obj):
        import numpy as np
        if isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, dict):
            return {str(k): convert_numpy_types(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [convert_numpy_types(item) for item in obj]
        elif isinstance(obj, tuple):
            return tuple(convert_numpy_types(item) for item in obj)
        else:
            return obj
    
    converted_results = convert_numpy_types(all_results)
    
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(converted_results, f, indent=2, ensure_ascii=False, default=str)
    
    print(f"\n💾 Résultats sauvegardés dans {output_file}")

def generate_test_commands_file(all_results: dict):
    """
    Génère un fichier avec toutes les commandes MOTIF à tester
    """
    output_file = "motif_commands_to_test.txt"
    
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("COMMANDES MOTIF GÉNÉRÉES POUR TEST MANUEL\n")
        f.write("=" * 50 + "\n\n")
        
        for task_id, results in all_results.items():
            if 'error' not in results and results.get('motif_commands'):
                f.write(f"TÂCHE {task_id}:\n")
                f.write("-" * 20 + "\n")
                
                for i, command in enumerate(results['motif_commands'], 1):
                    f.write(f"{i}. {command}\n")
                
                f.write(f"\nCohérence: {results['pattern_consistency']:.2f}\n")
                f.write(f"Complexité: {estimate_complexity(results)}\n\n")
    
    print(f"📝 Commandes de test sauvegardées dans {output_file}")

if __name__ == "__main__":
    print("Démarrage du test complet des scénarios MOTIF...")
    results = test_all_mosaic_tasks()
    
    # Générer le fichier de commandes pour test manuel
    generate_test_commands_file(results)
    
    print("\n✅ Test complet terminé!")
    print("📋 Fichiers générés:")
    print("   • all_mosaic_scenarios_results.json (résultats détaillés)")
    print("   • motif_commands_to_test.txt (commandes pour test manuel)")