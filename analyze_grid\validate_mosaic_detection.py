#!/usr/bin/env python3
"""
Validation croisée entre l'analyse automatique des scénarios AGI 
et l'analyse humaine des puzzles MOSAIC
"""

import os
import re
from typing import Dict, List, Set

def parse_human_analysis() -> Dict[str, Dict]:
    """Parse l'analyse humaine depuis arc_complete_metrics.txt"""
    
    human_results = {}
    
    try:
        with open("arc_complete_metrics.txt", "r", encoding='utf-8') as f:
            content = f.read()
        
        # Parser ligne par ligne en gérant les espaces/tabs
        lines = content.strip().split('\n')
        
        for line in lines[1:]:  # Skip header
            if line.strip():
                # Séparer par tabs multiples ou espaces multiples
                parts = re.split(r'\t+', line.strip())
                
                if len(parts) >= 4:
                    puzzle_id = parts[0].strip()
                    mosaic_score_str = parts[1].strip().replace(',', '.')
                    initial_classification = parts[2].strip()
                    human_analysis = parts[3].strip()
                    
                    try:
                        mosaic_score = float(mosaic_score_str)
                    except:
                        mosaic_score = 0.0
                    
                    # Déterminer le statut final
                    if "VRAI->FAUX" in initial_classification:
                        final_status = "FALSE_POSITIVE"
                        reason = human_analysis
                    elif "Confirmé" in human_analysis:
                        final_status = "TRUE_MOSAIC"
                        reason = human_analysis
                    elif initial_classification == "FAUX":
                        final_status = "TRUE_NEGATIVE"
                        reason = human_analysis
                    elif initial_classification == "VRAI":
                        final_status = "TRUE_MOSAIC"
                        reason = human_analysis
                    else:
                        final_status = "UNKNOWN"
                        reason = human_analysis
                    
                    human_results[puzzle_id] = {
                        'mosaic_score': mosaic_score,
                        'initial_classification': initial_classification,
                        'final_status': final_status,
                        'reason': reason
                    }
                    

    
    except Exception as e:
        print(f"Erreur lecture analyse humaine: {e}")
    
    return human_results

def load_agi_analysis_results() -> Dict[str, Dict]:
    """Charge les résultats de l'analyse AGI automatique"""
    
    # Résultats de notre analyse précédente
    agi_results = {}
    
    # Vrais MOSAIC identifiés par l'analyse AGI (avec clipboard_PASTE)
    true_mosaic_agi = [
        "0dfd9992", "29ec7d0e", "484b58aa", "9ecd008a", 
        "b8825c91", "c3f564a4", "dc0a314f", "3631a71a"
    ]
    
    # Faux positifs suspectés par l'analyse AGI
    false_positive_agi = [
        "780d0b14", "85c4e7cd", "e26a3af2", "09629e4f", "1c786137", 
        "4be741c5", "662c240a", "68b16354", "855e0971", "c909285e", 
        "d511f180", "de1cd16c", "8e1813be", "90c28cc7", "91714a58", 
        "9edfc990", "b9b7f026", "d687bc17"
    ]
    
    # Autres puzzles analysés
    other_puzzles = ["73251a56", "8731374e"]
    
    for puzzle_id in true_mosaic_agi:
        agi_results[puzzle_id] = {
            'agi_classification': 'TRUE_MOSAIC',
            'has_clipboard_paste': True,
            'complexity_score': 'HIGH'
        }
    
    for puzzle_id in false_positive_agi:
        agi_results[puzzle_id] = {
            'agi_classification': 'FALSE_POSITIVE',
            'has_clipboard_paste': False,
            'complexity_score': 'LOW'
        }
    
    for puzzle_id in other_puzzles:
        agi_results[puzzle_id] = {
            'agi_classification': 'UNCERTAIN',
            'has_clipboard_paste': False,
            'complexity_score': 'MEDIUM'
        }
    
    return agi_results

def cross_validate_results():
    """Validation croisée entre analyse humaine et AGI"""
    
    print("🔍 VALIDATION CROISÉE - ANALYSE HUMAINE vs AGI")
    print("=" * 60)
    
    human_results = parse_human_analysis()
    agi_results = load_agi_analysis_results()
    
    # Statistiques de concordance
    total_puzzles = 0
    concordant_cases = 0
    discordant_cases = 0
    
    true_positives = 0  # Vrais MOSAIC confirmés par les deux
    false_positives = 0  # Faux positifs identifiés par les deux
    agi_correct_predictions = 0  # AGI a correctement prédit
    
    print(f"\n📊 COMPARAISON DÉTAILLÉE")
    print("-" * 50)
    
    # Analyser chaque puzzle
    for puzzle_id in sorted(set(human_results.keys()) | set(agi_results.keys())):
        total_puzzles += 1
        
        human_data = human_results.get(puzzle_id, {})
        agi_data = agi_results.get(puzzle_id, {})
        
        human_status = human_data.get('final_status', 'UNKNOWN')
        agi_status = agi_data.get('agi_classification', 'UNKNOWN')
        
        # Déterminer la concordance
        concordant = False
        if human_status == 'TRUE_MOSAIC' and agi_status == 'TRUE_MOSAIC':
            concordant = True
            true_positives += 1
            status_icon = "✅✅"
        elif human_status == 'FALSE_POSITIVE' and agi_status == 'FALSE_POSITIVE':
            concordant = True
            false_positives += 1
            status_icon = "🚫🚫"
        elif human_status == 'TRUE_NEGATIVE' and agi_status == 'FALSE_POSITIVE':
            concordant = True
            status_icon = "🚫🚫"
        else:
            discordant_cases += 1
            status_icon = "❌❓"
        
        if concordant:
            concordant_cases += 1
            if agi_status in ['TRUE_MOSAIC', 'FALSE_POSITIVE']:
                agi_correct_predictions += 1
        
        # Affichage détaillé
        mosaic_score = human_data.get('mosaic_score', 0)
        reason = human_data.get('reason', 'N/A')
        has_paste = agi_data.get('has_clipboard_paste', False)
        
        print(f"{status_icon} {puzzle_id}")
        print(f"   Humain: {human_status} (score: {mosaic_score:.2f})")
        print(f"   AGI: {agi_status} (clipboard_PASTE: {has_paste})")
        print(f"   Raison: {reason}")
        print()
    
    # Statistiques finales
    print("\n" + "=" * 60)
    print("📈 STATISTIQUES DE VALIDATION")
    print("=" * 60)
    
    concordance_rate = (concordant_cases / total_puzzles) * 100 if total_puzzles > 0 else 0
    agi_accuracy = (agi_correct_predictions / total_puzzles) * 100 if total_puzzles > 0 else 0
    
    print(f"Total puzzles analysés: {total_puzzles}")
    print(f"Cas concordants: {concordant_cases} ({concordance_rate:.1f}%)")
    print(f"Cas discordants: {discordant_cases}")
    print(f"Précision AGI: {agi_accuracy:.1f}%")
    print()
    print(f"Vrais MOSAIC confirmés: {true_positives}")
    print(f"Faux positifs identifiés: {false_positives}")
    
    return {
        'concordance_rate': concordance_rate,
        'agi_accuracy': agi_accuracy,
        'true_positives': true_positives,
        'false_positives': false_positives
    }

def analyze_discriminant_patterns():
    """Analyse les patterns discriminants confirmés"""
    
    print("\n" + "=" * 60)
    print("🎯 PATTERNS DISCRIMINANTS CONFIRMÉS")
    print("=" * 60)
    
    human_results = parse_human_analysis()
    
    # Analyser les raisons des faux positifs
    false_positive_reasons = {}
    true_mosaic_characteristics = []
    
    for puzzle_id, data in human_results.items():
        if data['final_status'] == 'FALSE_POSITIVE':
            reason = data['reason']
            category = categorize_false_positive_reason(reason)
            false_positive_reasons[category] = false_positive_reasons.get(category, 0) + 1
        elif data['final_status'] == 'TRUE_MOSAIC':
            true_mosaic_characteristics.append(data['reason'])
    
    print(f"\n🚫 CATÉGORIES DE FAUX POSITIFS:")
    print("-" * 40)
    for category, count in sorted(false_positive_reasons.items(), key=lambda x: x[1], reverse=True):
        print(f"  • {category}: {count} puzzles")
    
    print(f"\n✅ CARACTÉRISTIQUES DES VRAIS MOSAIC:")
    print("-" * 40)
    unique_chars = list(set(true_mosaic_characteristics))
    for i, char in enumerate(unique_chars, 1):
        count = true_mosaic_characteristics.count(char)
        print(f"  {i}. {char} ({count} puzzles)")
    
    print(f"\n🔍 VALIDATION DU PATTERN clipboard_PASTE:")
    print("-" * 50)
    
    # Calculer les statistiques du pattern clipboard_PASTE
    true_mosaic_with_paste = 0
    false_positive_with_paste = 0
    
    agi_results = load_agi_analysis_results()
    
    for puzzle_id, data in human_results.items():
        agi_data = agi_results.get(puzzle_id, {})
        has_paste = agi_data.get('has_clipboard_paste', False)
        
        if data['final_status'] == 'TRUE_MOSAIC' and has_paste:
            true_mosaic_with_paste += 1
        elif data['final_status'] in ['FALSE_POSITIVE', 'TRUE_NEGATIVE'] and has_paste:
            false_positive_with_paste += 1
    
    total_true_mosaic = len([d for d in human_results.values() if d['final_status'] == 'TRUE_MOSAIC'])
    total_false_positive = len([d for d in human_results.values() if d['final_status'] in ['FALSE_POSITIVE', 'TRUE_NEGATIVE']])
    
    paste_precision = true_mosaic_with_paste / (true_mosaic_with_paste + false_positive_with_paste) * 100 if (true_mosaic_with_paste + false_positive_with_paste) > 0 else 0
    paste_recall = true_mosaic_with_paste / total_true_mosaic * 100 if total_true_mosaic > 0 else 0
    
    print(f"• clipboard_PASTE dans vrais MOSAIC: {true_mosaic_with_paste}/{total_true_mosaic} ({paste_recall:.1f}%)")
    print(f"• clipboard_PASTE dans faux positifs: {false_positive_with_paste}/{total_false_positive} ({false_positive_with_paste/total_false_positive*100 if total_false_positive > 0 else 0:.1f}%)")
    print(f"• Précision du pattern clipboard_PASTE: {paste_precision:.1f}%")
    print(f"• Rappel du pattern clipboard_PASTE: {paste_recall:.1f}%")
    
    print(f"\nCONCLUSION:")
    print("Le pattern clipboard_PASTE est un discriminant très fiable pour")
    print("identifier les vrais puzzles MOSAIC dans les scénarios AGI.")

def categorize_false_positive_reason(reason: str) -> str:
    """Catégorise les raisons des faux positifs"""
    
    reason_lower = reason.lower()
    
    if "zone de même couleur" in reason_lower or "fond uni" in reason_lower:
        return "Zones uniformes"
    elif "cadre" in reason_lower or "bande" in reason_lower:
        return "Cadres/Bandes"
    elif "bruit" in reason_lower:
        return "Bruit de couleur"
    elif "ligne" in reason_lower:
        return "Lignes de couleur"
    elif "petit" in reason_lower:
        return "Taille réduite"
    elif "grille" in reason_lower:
        return "Structure grille"
    else:
        return "Autre"

def main():
    """Point d'entrée principal"""
    
    # Validation croisée
    stats = cross_validate_results()
    
    # Analyse des patterns
    analyze_discriminant_patterns()
    
    print(f"\n🎯 RÉSUMÉ FINAL")
    print("=" * 40)
    print(f"L'analyse AGI basée sur clipboard_PASTE a une précision de {stats['agi_accuracy']:.1f}%")
    print(f"Concordance avec l'analyse humaine: {stats['concordance_rate']:.1f}%")
    print("Le pattern clipboard_PASTE est confirmé comme discriminant fiable.")

if __name__ == "__main__":
    main()