#!/usr/bin/env python3
"""
Test du détecteur unifié sur les 8 tâches MOSAIC validées
Comparaison avec detect_mosaic_pattern.py
"""

import json
import os
import numpy as np
from unified_mosaic_detector import UnifiedMosaicDetector
from detect_mosaic_pattern import detect_mosaic_pattern

def load_arc_puzzle(puzzle_id, data_dir="../arcdata/training"):
    """Charge un puzzle ARC depuis les fichiers JSON"""
    json_file = os.path.join(data_dir, f"{puzzle_id}.json")
    
    try:
        with open(json_file, 'r') as f:
            return json.load(f)
    except Exception as e:
        print(f"Erreur chargement {puzzle_id}: {e}")
        return None

def compare_detectors_on_validated():
    """Compare les deux détecteurs sur les 8 tâches validées"""
    
    # Les 8 tâches validées comme vrais MOSAIC
    validated_mosaic_ids = [
        "0dfd9992",  # Confirmé
        "29ec7d0e",  # Confirmé mais ensemble de petite mosaic
        "3631a71a",  # Confirmé mais mosaic non centré dans le cadre
        "484b58aa",  # Confirmé
        "9ecd008a",  # Confirmé
        "b8825c91",  # Confirmé
        "c3f564a4",  # Confirmé
        "dc0a314f"   # Confirmé
    ]
    
    print("🎨 COMPARAISON DES DÉTECTEURS SUR LES 8 TÂCHES VALIDÉES")
    print("=" * 70)
    print("Légende: [Simple] vs [Unifié]")
    print()
    
    unified_detector = UnifiedMosaicDetector()
    
    results_comparison = []
    
    for puzzle_id in validated_mosaic_ids:
        print(f"🔍 ANALYSE DE {puzzle_id}")
        print("-" * 50)
        
        # Charger le puzzle
        puzzle_data = load_arc_puzzle(puzzle_id)
        if not puzzle_data:
            print("❌ Impossible de charger le puzzle")
            continue
        
        puzzle_results = {
            'puzzle_id': puzzle_id,
            'simple_detections': 0,
            'unified_detections': 0,
            'simple_max_conf': 0,
            'unified_max_conf': 0,
            'grids_analyzed': 0
        }
        
        # Analyser tous les exemples
        for i, example in enumerate(puzzle_data.get('train', [])):
            input_grid = np.array(example['input'])
            output_grid = np.array(example['output'])
            
            # Analyser input avec les deux détecteurs
            simple_input = detect_mosaic_pattern(input_grid)
            unified_input = unified_detector.detect_and_solve(input_grid)
            
            puzzle_results['grids_analyzed'] += 1
            
            simple_detected = simple_input['is_mosaic']
            unified_detected = unified_input['is_mosaic']
            
            if simple_detected:
                puzzle_results['simple_detections'] += 1
                puzzle_results['simple_max_conf'] = max(puzzle_results['simple_max_conf'], simple_input['confidence'])
            
            if unified_detected:
                puzzle_results['unified_detections'] += 1
                puzzle_results['unified_max_conf'] = max(puzzle_results['unified_max_conf'], unified_input['confidence'])
            
            # Affichage comparatif
            simple_status = "✅" if simple_detected else "❌"
            unified_status = "✅" if unified_detected else "❌"
            
            print(f"  train{i}_input ({input_grid.shape[0]}x{input_grid.shape[1]}): "
                  f"[{simple_status} {simple_input['confidence']:.1%}] vs "
                  f"[{unified_status} {unified_input['confidence']:.1%}]")
            
            # Analyser output avec les deux détecteurs
            simple_output = detect_mosaic_pattern(output_grid)
            unified_output = unified_detector.detect_and_solve(output_grid)
            
            puzzle_results['grids_analyzed'] += 1
            
            simple_detected = simple_output['is_mosaic']
            unified_detected = unified_output['is_mosaic']
            
            if simple_detected:
                puzzle_results['simple_detections'] += 1
                puzzle_results['simple_max_conf'] = max(puzzle_results['simple_max_conf'], simple_output['confidence'])
            
            if unified_detected:
                puzzle_results['unified_detections'] += 1
                puzzle_results['unified_max_conf'] = max(puzzle_results['unified_max_conf'], unified_output['confidence'])
            
            simple_status = "✅" if simple_detected else "❌"
            unified_status = "✅" if unified_detected else "❌"
            
            print(f"  train{i}_output ({output_grid.shape[0]}x{output_grid.shape[1]}): "
                  f"[{simple_status} {simple_output['confidence']:.1%}] vs "
                  f"[{unified_status} {unified_output['confidence']:.1%}]")
        
        # Analyser les exemples de test
        for i, example in enumerate(puzzle_data.get('test', [])):
            input_grid = np.array(example['input'])
            
            # Analyser input de test
            simple_input = detect_mosaic_pattern(input_grid)
            unified_input = unified_detector.detect_and_solve(input_grid)
            
            puzzle_results['grids_analyzed'] += 1
            
            simple_detected = simple_input['is_mosaic']
            unified_detected = unified_input['is_mosaic']
            
            if simple_detected:
                puzzle_results['simple_detections'] += 1
                puzzle_results['simple_max_conf'] = max(puzzle_results['simple_max_conf'], simple_input['confidence'])
            
            if unified_detected:
                puzzle_results['unified_detections'] += 1
                puzzle_results['unified_max_conf'] = max(puzzle_results['unified_max_conf'], unified_input['confidence'])
            
            simple_status = "✅" if simple_detected else "❌"
            unified_status = "✅" if unified_detected else "❌"
            
            print(f"  test{i}_input ({input_grid.shape[0]}x{input_grid.shape[1]}): "
                  f"[{simple_status} {simple_input['confidence']:.1%}] vs "
                  f"[{unified_status} {unified_input['confidence']:.1%}]")
            
            # Analyser output de test s'il existe
            if 'output' in example:
                output_grid = np.array(example['output'])
                
                simple_output = detect_mosaic_pattern(output_grid)
                unified_output = unified_detector.detect_and_solve(output_grid)
                
                puzzle_results['grids_analyzed'] += 1
                
                simple_detected = simple_output['is_mosaic']
                unified_detected = unified_output['is_mosaic']
                
                if simple_detected:
                    puzzle_results['simple_detections'] += 1
                    puzzle_results['simple_max_conf'] = max(puzzle_results['simple_max_conf'], simple_output['confidence'])
                
                if unified_detected:
                    puzzle_results['unified_detections'] += 1
                    puzzle_results['unified_max_conf'] = max(puzzle_results['unified_max_conf'], unified_output['confidence'])
                
                simple_status = "✅" if simple_detected else "❌"
                unified_status = "✅" if unified_detected else "❌"
                
                print(f"  test{i}_output ({output_grid.shape[0]}x{output_grid.shape[1]}): "
                      f"[{simple_status} {simple_output['confidence']:.1%}] vs "
                      f"[{unified_status} {unified_output['confidence']:.1%}]")
        
        # Résumé pour ce puzzle
        print(f"  📊 RÉSUMÉ {puzzle_id}:")
        print(f"     Simple: {puzzle_results['simple_detections']}/{puzzle_results['grids_analyzed']} "
              f"(max: {puzzle_results['simple_max_conf']:.1%})")
        print(f"     Unifié: {puzzle_results['unified_detections']}/{puzzle_results['grids_analyzed']} "
              f"(max: {puzzle_results['unified_max_conf']:.1%})")
        
        results_comparison.append(puzzle_results)
        print()
    
    return results_comparison

def analyze_comparison_results(results):
    """Analyse les résultats de comparaison"""
    
    print("📊 ANALYSE COMPARATIVE GLOBALE")
    print("=" * 50)
    
    # Statistiques globales
    total_puzzles = len(results)
    simple_puzzle_detections = sum(1 for r in results if r['simple_detections'] > 0)
    unified_puzzle_detections = sum(1 for r in results if r['unified_detections'] > 0)
    
    total_grids = sum(r['grids_analyzed'] for r in results)
    total_simple_detections = sum(r['simple_detections'] for r in results)
    total_unified_detections = sum(r['unified_detections'] for r in results)
    
    print(f"📋 Puzzles avec détections:")
    print(f"   Simple: {simple_puzzle_detections}/{total_puzzles} ({simple_puzzle_detections/total_puzzles*100:.1f}%)")
    print(f"   Unifié: {unified_puzzle_detections}/{total_puzzles} ({unified_puzzle_detections/total_puzzles*100:.1f}%)")
    
    print(f"\n📋 Grilles détectées:")
    print(f"   Simple: {total_simple_detections}/{total_grids} ({total_simple_detections/total_grids*100:.1f}%)")
    print(f"   Unifié: {total_unified_detections}/{total_grids} ({total_unified_detections/total_grids*100:.1f}%)")
    
    # Comparaison par puzzle
    print(f"\n🎯 COMPARAISON PAR PUZZLE:")
    print("-" * 40)
    
    for result in results:
        puzzle_id = result['puzzle_id']
        simple_rate = result['simple_detections'] / result['grids_analyzed'] * 100
        unified_rate = result['unified_detections'] / result['grids_analyzed'] * 100
        
        winner = "Simple" if simple_rate > unified_rate else "Unifié" if unified_rate > simple_rate else "Égalité"
        
        print(f"{puzzle_id}: Simple {simple_rate:.0f}% vs Unifié {unified_rate:.0f}% → {winner}")
    
    # Analyse des confiances
    simple_confidences = [r['simple_max_conf'] for r in results if r['simple_max_conf'] > 0]
    unified_confidences = [r['unified_max_conf'] for r in results if r['unified_max_conf'] > 0]
    
    if simple_confidences:
        print(f"\n📈 Confiances maximales (Simple):")
        print(f"   Moyenne: {np.mean(simple_confidences):.1%}")
        print(f"   Médiane: {np.median(simple_confidences):.1%}")
        print(f"   Min-Max: {min(simple_confidences):.1%} - {max(simple_confidences):.1%}")
    
    if unified_confidences:
        print(f"\n📈 Confiances maximales (Unifié):")
        print(f"   Moyenne: {np.mean(unified_confidences):.1%}")
        print(f"   Médiane: {np.median(unified_confidences):.1%}")
        print(f"   Min-Max: {min(unified_confidences):.1%} - {max(unified_confidences):.1%}")

def detailed_analysis_one_case(puzzle_id="0dfd9992"):
    """Analyse détaillée d'un cas avec les deux détecteurs"""
    
    print(f"\n🔬 ANALYSE DÉTAILLÉE COMPARATIVE - {puzzle_id}")
    print("=" * 60)
    
    puzzle_data = load_arc_puzzle(puzzle_id)
    if not puzzle_data:
        print("❌ Impossible de charger le puzzle")
        return
    
    unified_detector = UnifiedMosaicDetector()
    
    # Analyser le premier exemple d'entraînement
    if puzzle_data.get('train'):
        example = puzzle_data['train'][0]
        input_grid = np.array(example['input'])
        
        print(f"📋 Analyse de train0_input ({input_grid.shape[0]}x{input_grid.shape[1]}):")
        
        # Détecteur simple
        print(f"\n🔍 DÉTECTEUR SIMPLE:")
        simple_result = detect_mosaic_pattern(input_grid)
        print(f"   Est mosaïque: {simple_result['is_mosaic']}")
        print(f"   Confiance: {simple_result['confidence']:.1%}")
        print(f"   Preuves: {len(simple_result['evidence'])}")
        for evidence in simple_result['evidence']:
            print(f"     • {evidence}")
        print(f"   Blocs: {len(simple_result['pattern_blocks'])}, "
              f"Vides: {len(simple_result['empty_regions'])}, "
              f"Symétries: {len(simple_result['symmetries'])}")
        
        # Détecteur unifié
        print(f"\n🔍 DÉTECTEUR UNIFIÉ:")
        unified_result = unified_detector.detect_and_solve(input_grid)
        print(f"   Est mosaïque: {unified_result['is_mosaic']}")
        print(f"   Confiance: {unified_result['confidence']:.1%}")
        print(f"   Preuves: {len(unified_result['detection_evidence'])}")
        for evidence in unified_result['detection_evidence']:
            print(f"     • {evidence}")
        print(f"   Blocs sources: {len(unified_result['source_blocks'])}, "
              f"Régions cibles: {len(unified_result['target_regions'])}, "
              f"Transformations: {len(unified_result['transformations'])}")
        
        # Comparaison
        print(f"\n⚖️ COMPARAISON:")
        print(f"   Confiance: Simple {simple_result['confidence']:.1%} vs Unifié {unified_result['confidence']:.1%}")
        print(f"   Détection: Simple {simple_result['is_mosaic']} vs Unifié {unified_result['is_mosaic']}")
        
        if unified_result['is_mosaic'] and unified_result['transformations']:
            print(f"   Transformations proposées par l'unifié:")
            for i, transform in enumerate(unified_result['transformations'], 1):
                print(f"     {i}. Score: {transform['score']:.2f}")

def main():
    """Point d'entrée principal"""
    
    # Comparaison sur les 8 puzzles validés
    results = compare_detectors_on_validated()
    
    # Analyse des résultats
    analyze_comparison_results(results)
    
    # Analyse détaillée d'un cas
    detailed_analysis_one_case("0dfd9992")

if __name__ == "__main__":
    main()