#!/usr/bin/env python3
"""
Analyse des scénarios AGI pour les puzzles MOSAIC identifiés
Compare les commandes entre INIT\\TRANSFERT et END
"""

import os
import re
from typing import Dict, List, Optional

def extract_agi_commands(agi_file_path: str) -> Optional[Dict]:
    """Extrait les commandes entre INIT/TRANSFERT et END selon la grammaire AGI"""
    
    try:
        with open(agi_file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Nettoyer le contenu (supprimer commentaires et lignes vides)
        lines = []
        for line in content.split('\n'):
            line = line.strip()
            if line and not line.startswith('#') and not line.startswith('//'):
                lines.append(line)
        clean_content = ' '.join(lines)
        
        # Chercher les patterns selon la grammaire
        result = {
            'raw_content': clean_content,
            'initial_command': None,
            'scenario_commands': None,
            'scenario_type': None
        }
        
        # Pattern TRANSFERT { ... }
        transfert_pattern = r'TRANSFERT\s*\{\s*(.*?)\s*\}\s*(?:END|$)'
        transfert_match = re.search(transfert_pattern, clean_content, re.DOTALL | re.IGNORECASE)
        
        if transfert_match:
            result['initial_command'] = 'TRANSFERT'
            result['scenario_commands'] = transfert_match.group(1).strip()
            result['scenario_type'] = 'TRANSFERT_BLOCK'
        else:
            # Pattern TRANSFERT ... END (ancien format)
            transfert_simple_pattern = r'TRANSFERT\s+(.*?)\s*END'
            transfert_simple_match = re.search(transfert_simple_pattern, clean_content, re.DOTALL | re.IGNORECASE)
            
            if transfert_simple_match:
                result['initial_command'] = 'TRANSFERT'
                result['scenario_commands'] = transfert_simple_match.group(1).strip()
                result['scenario_type'] = 'TRANSFERT_SIMPLE'
            else:
                # Pattern INIT ... END
                init_pattern = r'INIT\s+(\d+x\d+)\s*(.*?)\s*END'
                init_match = re.search(init_pattern, clean_content, re.DOTALL | re.IGNORECASE)
                
                if init_match:
                    result['initial_command'] = f'INIT {init_match.group(1)}'
                    result['scenario_commands'] = init_match.group(2).strip()
                    result['scenario_type'] = 'INIT'
        
        return result if result['scenario_commands'] else None
            
    except Exception as e:
        print(f"Erreur lecture {agi_file_path}: {e}")
        return None

def analyze_agi_scenario(scenario_data: Dict) -> Dict:
    """Analyse détaillée d'un scénario AGI selon la grammaire"""
    
    if not scenario_data or not scenario_data.get('scenario_commands'):
        return {
            'total_commands': 0,
            'unique_commands': 0,
            'command_types': [],
            'has_motif': False,
            'has_grouped_commands': False,
            'complexity_score': 0,
            'scenario_type': None,
            'mosaic_indicators': []
        }
    
    commands_text = scenario_data['scenario_commands']
    scenario_type = scenario_data['scenario_type']
    
    # Séparer les commandes (par ; ou par espaces selon le format)
    if ';' in commands_text:
        commands = [cmd.strip() for cmd in commands_text.split(';') if cmd.strip()]
    else:
        # Essayer de détecter les commandes par mots-clés
        command_keywords = ['FILL', 'EDIT', 'CLEAR', 'REPLACE', 'SURROUND', 'COPY', 'CUT', 'PASTE', 
                           'FLIP', 'ROTATE', 'MULTIPLY', 'DIVIDE', 'INSERT', 'DELETE', 'EXTRACT',
                           'MOTIF', 'EDITS', 'FILLS', 'CLEARS', 'REPLACES', 'SURROUNDS', 'FLIPS', 
                           'ROTATES', 'MULTIPLYS', 'DIVIDES', 'INSERTS', 'DELETES']
        
        commands = []
        words = commands_text.split()
        current_command = []
        
        for word in words:
            if word.upper() in command_keywords and current_command:
                commands.append(' '.join(current_command))
                current_command = [word]
            else:
                current_command.append(word)
        
        if current_command:
            commands.append(' '.join(current_command))
    
    # Analyser chaque commande
    command_types = []
    mosaic_indicators = []
    has_motif = False
    has_grouped_commands = False
    
    for cmd in commands:
        cmd_upper = cmd.upper()
        
        # Extraire le type de commande principal
        parts = cmd.split()
        if parts:
            main_cmd = parts[0].upper()
            command_types.append(main_cmd)
            
            # Détecter les indicateurs MOSAIC
            if main_cmd == 'MOTIF':
                has_motif = True
                mosaic_indicators.append('MOTIF_command')
            
            if main_cmd in ['EDITS', 'FILLS', 'CLEARS', 'REPLACES', 'SURROUNDS', 'FLIPS', 'ROTATES', 'MULTIPLYS', 'DIVIDES']:
                has_grouped_commands = True
                mosaic_indicators.append(f'grouped_{main_cmd}')
            
            # Détecter les opérations de copie/collage (indicateur MOSAIC)
            if main_cmd in ['COPY', 'CUT', 'PASTE']:
                mosaic_indicators.append(f'clipboard_{main_cmd}')
            
            # Détecter les transformations (indicateur MOSAIC)
            if main_cmd in ['FLIP', 'ROTATE', 'MULTIPLY', 'DIVIDE']:
                mosaic_indicators.append(f'transform_{main_cmd}')
            
            # Détecter les coordonnées multiples (indicateur MOSAIC potentiel)
            if '[' in cmd and cmd.count('[') > 1:
                mosaic_indicators.append('multiple_coordinates')
    
    # Calculer le score de complexité
    unique_commands = len(set(command_types))
    complexity_score = len(commands) + unique_commands * 2
    
    if has_motif:
        complexity_score += 15  # MOTIF est très complexe
    if has_grouped_commands:
        complexity_score += 10
    if len(mosaic_indicators) > 2:
        complexity_score += 5
    
    return {
        'total_commands': len(commands),
        'unique_commands': unique_commands,
        'command_types': command_types,
        'has_motif': has_motif,
        'has_grouped_commands': has_grouped_commands,
        'complexity_score': complexity_score,
        'scenario_type': scenario_type,
        'mosaic_indicators': list(set(mosaic_indicators)),
        'most_common_commands': sorted(set(command_types), key=command_types.count, reverse=True)[:5],
        'commands_detail': commands
    }

def analyze_mosaic_scenarios():
    """Analyse les scénarios des 28 puzzles MOSAIC identifiés"""
    
    # Liste des 28 puzzles MOSAIC identifiés
    mosaic_puzzle_ids = [
        "0dfd9992", "29ec7d0e", "484b58aa", "73251a56", "780d0b14", "85c4e7cd",
        "8731374e", "9ecd008a", "b8825c91", "c3f564a4", "dc0a314f", "e26a3af2",
        "09629e4f", "1c786137", "4be741c5", "662c240a", "68b16354", "855e0971",
        "c909285e", "d511f180", "de1cd16c", "3631a71a", "8e1813be", "90c28cc7",
        "91714a58", "9edfc990", "b9b7f026", "d687bc17"
    ]
    
    # Puzzles suspectés de ne pas être des vrais MOSAIC
    suspected_false_positives = [
        "780d0b14", "85c4e7cd", "e26a3af2", "09629e4f", "1c786137", "4be741c5",
        "662c240a", "68b16354", "855e0971", "c909285e", "d511f180", "de1cd16c",
        "8e1813be", "90c28cc7", "91714a58", "9edfc990", "b9b7f026", "d687bc17"
    ]
    
    print("🎨 ANALYSE DES SCÉNARIOS AGI - PUZZLES MOSAIC")
    print("=" * 60)
    print(f"Puzzles à analyser: {len(mosaic_puzzle_ids)}")
    print(f"Suspectés faux positifs: {len(suspected_false_positives)}")
    print()
    
    agi_dir = "../arcdata/training"
    results = []
    
    for puzzle_id in mosaic_puzzle_ids:
        agi_file = os.path.join(agi_dir, f"{puzzle_id}_TEST0_VALID.agi")
        
        if os.path.exists(agi_file):
            scenario_data = extract_agi_commands(agi_file)
            analysis = analyze_agi_scenario(scenario_data)
            
            is_suspected = puzzle_id in suspected_false_positives
            
            result = {
                'puzzle_id': puzzle_id,
                'is_suspected_false_positive': is_suspected,
                'scenario_data': scenario_data,
                'analysis': analysis
            }
            results.append(result)
            
            # Affichage amélioré
            status = "🚫 SUSPECT" if is_suspected else "✅ PROBABLE"
            print(f"{status} {puzzle_id}")
            
            if scenario_data:
                print(f"   Type: {analysis['scenario_type']}")
                print(f"   Commandes: {analysis['total_commands']}, Uniques: {analysis['unique_commands']}")
                print(f"   Complexité: {analysis['complexity_score']}")
                print(f"   MOTIF: {analysis['has_motif']}, Groupées: {analysis['has_grouped_commands']}")
                
                if analysis['mosaic_indicators']:
                    print(f"   Indicateurs MOSAIC: {', '.join(analysis['mosaic_indicators'][:3])}")
                
                print(f"   Commandes principales: {', '.join(analysis.get('most_common_commands', [])[:3])}")
                
                # Afficher le scénario intelligent
                scenario_commands = scenario_data['scenario_commands']
                if scenario_commands:
                    print(f"   📋 SCÉNARIO INTELLIGENT:")
                    if len(scenario_commands) > 100:
                        print(f"      {scenario_commands[:100]}...")
                    else:
                        print(f"      {scenario_commands}")
                else:
                    print("   > Aucun scénario trouvé")
            else:
                print("   ❌ Impossible d'extraire le scénario")
            print()
        else:
            print(f"❌ {puzzle_id} - Fichier AGI non trouvé")
            results.append({
                'puzzle_id': puzzle_id,
                'is_suspected_false_positive': puzzle_id in suspected_false_positives,
                'commands': None,
                'analysis': None
            })
    
    return results

def compare_true_vs_false_mosaic(results: List[Dict]):
    """Compare les vrais MOSAIC vs les faux positifs selon les scénarios AGI"""
    
    print("\n" + "=" * 60)
    print("📊 COMPARAISON VRAIS MOSAIC vs FAUX POSITIFS")
    print("=" * 60)
    
    true_mosaic = [r for r in results if not r['is_suspected_false_positive'] and r['analysis']]
    false_positives = [r for r in results if r['is_suspected_false_positive'] and r['analysis']]
    
    print(f"\n✅ VRAIS MOSAIC ({len(true_mosaic)} puzzles)")
    print("-" * 40)
    
    if true_mosaic:
        # Statistiques des vrais MOSAIC
        true_complexities = [r['analysis']['complexity_score'] for r in true_mosaic]
        true_commands = [r['analysis']['total_commands'] for r in true_mosaic]
        true_unique_cmds = [r['analysis']['unique_commands'] for r in true_mosaic]
        true_motif_count = sum(1 for r in true_mosaic if r['analysis']['has_motif'])
        true_grouped_count = sum(1 for r in true_mosaic if r['analysis']['has_grouped_commands'])
        
        print(f"Complexité moyenne: {sum(true_complexities)/len(true_complexities):.1f}")
        print(f"Commandes moyennes: {sum(true_commands)/len(true_commands):.1f}")
        print(f"Commandes uniques moyennes: {sum(true_unique_cmds)/len(true_unique_cmds):.1f}")
        print(f"Avec MOTIF: {true_motif_count}/{len(true_mosaic)} ({true_motif_count/len(true_mosaic)*100:.1f}%)")
        print(f"Avec commandes groupées: {true_grouped_count}/{len(true_mosaic)} ({true_grouped_count/len(true_mosaic)*100:.1f}%)")
        
        # Indicateurs MOSAIC les plus fréquents
        all_indicators = []
        for r in true_mosaic:
            all_indicators.extend(r['analysis']['mosaic_indicators'])
        
        from collections import Counter
        common_indicators = Counter(all_indicators).most_common(10)
        print(f"Indicateurs MOSAIC fréquents: {', '.join([f'{ind}({count})' for ind, count in common_indicators[:5]])}")
        
        # Commandes les plus fréquentes
        all_commands = []
        for r in true_mosaic:
            all_commands.extend(r['analysis']['command_types'])
        
        common_commands = Counter(all_commands).most_common(10)
        print(f"Commandes fréquentes: {', '.join([f'{cmd}({count})' for cmd, count in common_commands[:5]])}")
        
        print(f"\nExemples de vrais MOSAIC:")
        for r in true_mosaic[:3]:
            indicators = ', '.join(r['analysis']['mosaic_indicators'][:2]) if r['analysis']['mosaic_indicators'] else 'aucun'
            print(f"  {r['puzzle_id']}: complexité {r['analysis']['complexity_score']}, indicateurs: {indicators}")
    
    print(f"\n🚫 FAUX POSITIFS SUSPECTÉS ({len(false_positives)} puzzles)")
    print("-" * 40)
    
    if false_positives:
        # Statistiques des faux positifs
        false_complexities = [r['analysis']['complexity_score'] for r in false_positives]
        false_commands = [r['analysis']['total_commands'] for r in false_positives]
        false_unique_cmds = [r['analysis']['unique_commands'] for r in false_positives]
        false_motif_count = sum(1 for r in false_positives if r['analysis']['has_motif'])
        false_grouped_count = sum(1 for r in false_positives if r['analysis']['has_grouped_commands'])
        
        print(f"Complexité moyenne: {sum(false_complexities)/len(false_complexities):.1f}")
        print(f"Commandes moyennes: {sum(false_commands)/len(false_commands):.1f}")
        print(f"Commandes uniques moyennes: {sum(false_unique_cmds)/len(false_unique_cmds):.1f}")
        print(f"Avec MOTIF: {false_motif_count}/{len(false_positives)} ({false_motif_count/len(false_positives)*100:.1f}%)")
        print(f"Avec commandes groupées: {false_grouped_count}/{len(false_positives)} ({false_grouped_count/len(false_positives)*100:.1f}%)")
        
        # Indicateurs MOSAIC les plus fréquents
        all_indicators = []
        for r in false_positives:
            all_indicators.extend(r['analysis']['mosaic_indicators'])
        
        common_indicators = Counter(all_indicators).most_common(10)
        print(f"Indicateurs MOSAIC fréquents: {', '.join([f'{ind}({count})' for ind, count in common_indicators[:5]])}")
        
        # Commandes les plus fréquentes
        all_commands = []
        for r in false_positives:
            all_commands.extend(r['analysis']['command_types'])
        
        common_commands = Counter(all_commands).most_common(10)
        print(f"Commandes fréquentes: {', '.join([f'{cmd}({count})' for cmd, count in common_commands[:5]])}")
        
        print(f"\nExemples de faux positifs:")
        for r in false_positives[:5]:
            indicators = ', '.join(r['analysis']['mosaic_indicators'][:2]) if r['analysis']['mosaic_indicators'] else 'aucun'
            print(f"  {r['puzzle_id']}: complexité {r['analysis']['complexity_score']}, indicateurs: {indicators}")

def generate_detailed_report(results: List[Dict]):
    """Génère un rapport détaillé des scénarios AGI"""
    
    with open("mosaic_scenarios_analysis.txt", "w", encoding='utf-8') as f:
        f.write("ANALYSE DÉTAILLÉE DES SCÉNARIOS AGI - PUZZLES MOSAIC\n")
        f.write("=" * 60 + "\n\n")
        
        for result in results:
            puzzle_id = result['puzzle_id']
            is_suspected = result['is_suspected_false_positive']
            scenario_data = result.get('scenario_data')
            analysis = result['analysis']
            
            status = "FAUX POSITIF SUSPECTÉ" if is_suspected else "VRAI MOSAIC PROBABLE"
            f.write(f"{puzzle_id} - {status}\n")
            f.write("-" * 50 + "\n")
            
            if analysis and scenario_data:
                f.write(f"Type de scénario: {analysis['scenario_type']}\n")
                f.write(f"Commande initiale: {scenario_data['initial_command']}\n")
                f.write(f"Complexité: {analysis['complexity_score']}\n")
                f.write(f"Nombre de commandes: {analysis['total_commands']}\n")
                f.write(f"Commandes uniques: {analysis['unique_commands']}\n")
                f.write(f"Contient MOTIF: {analysis['has_motif']}\n")
                f.write(f"Commandes groupées: {analysis['has_grouped_commands']}\n")
                f.write(f"Indicateurs MOSAIC: {', '.join(analysis['mosaic_indicators'])}\n")
                f.write(f"Commandes principales: {', '.join(analysis['most_common_commands'])}\n\n")
                
                # Scénario intelligent complet
                scenario_commands = scenario_data['scenario_commands']
                if scenario_commands:
                    f.write("SCÉNARIO INTELLIGENT (entre INIT/TRANSFERT et END):\n")
                    f.write("-" * 40 + "\n")
                    f.write(scenario_commands)
                    f.write("\n\n")
                    
                    # Détail des commandes individuelles
                    if 'commands_detail' in analysis and analysis['commands_detail']:
                        f.write("DÉTAIL DES COMMANDES:\n")
                        f.write("-" * 30 + "\n")
                        for i, cmd in enumerate(analysis['commands_detail'], 1):
                            f.write(f"{i:2d}. {cmd}\n")
                        f.write("\n")
                else:
                    f.write("Aucun scénario intelligent trouvé\n\n")
            else:
                f.write("Analyse non disponible - fichier AGI non trouvé ou invalide\n\n")
            
            f.write("\n" + "="*60 + "\n\n")
    
    print(f"📄 Rapport détaillé sauvegardé: mosaic_scenarios_analysis.txt")

def identify_true_mosaic_patterns(results: List[Dict]):
    """Identifie les patterns qui caractérisent les vrais puzzles MOSAIC"""
    
    print("\n" + "=" * 60)
    print("🔍 IDENTIFICATION DES PATTERNS MOSAIC AUTHENTIQUES")
    print("=" * 60)
    
    true_mosaic = [r for r in results if not r['is_suspected_false_positive'] and r['analysis']]
    false_positives = [r for r in results if r['is_suspected_false_positive'] and r['analysis']]
    
    if not true_mosaic:
        print("❌ Aucun vrai MOSAIC trouvé pour l'analyse")
        return
    
    print(f"\n📊 PATTERNS DISTINCTIFS DES VRAIS MOSAIC:")
    print("-" * 50)
    
    # Analyser les patterns spécifiques aux vrais MOSAIC
    true_patterns = {}
    false_patterns = {}
    
    # Collecter les patterns
    for r in true_mosaic:
        analysis = r['analysis']
        for indicator in analysis['mosaic_indicators']:
            true_patterns[indicator] = true_patterns.get(indicator, 0) + 1
    
    for r in false_positives:
        analysis = r['analysis']
        for indicator in analysis['mosaic_indicators']:
            false_patterns[indicator] = false_patterns.get(indicator, 0) + 1
    
    # Identifier les patterns discriminants
    discriminant_patterns = []
    for pattern, true_count in true_patterns.items():
        false_count = false_patterns.get(pattern, 0)
        true_ratio = true_count / len(true_mosaic)
        false_ratio = false_count / len(false_positives) if false_positives else 0
        
        if true_ratio > 0.5 and true_ratio > false_ratio * 2:  # Pattern significatif
            discriminant_patterns.append((pattern, true_ratio, false_ratio, true_count))
    
    discriminant_patterns.sort(key=lambda x: x[1], reverse=True)
    
    print("Patterns discriminants (plus fréquents chez les vrais MOSAIC):")
    for pattern, true_ratio, false_ratio, count in discriminant_patterns:
        print(f"  • {pattern}: {true_ratio:.1%} vrais vs {false_ratio:.1%} faux ({count} occurrences)")
    
    # Analyser les scénarios les plus représentatifs
    print(f"\n🎯 SCÉNARIOS REPRÉSENTATIFS:")
    print("-" * 40)
    
    # Trier par score de "MOSAIC-ness"
    def calculate_mosaic_score(analysis):
        score = 0
        if analysis['has_motif']:
            score += 20
        if analysis['has_grouped_commands']:
            score += 10
        score += len(analysis['mosaic_indicators']) * 5
        score += analysis['complexity_score'] * 0.1
        return score
    
    true_mosaic_sorted = sorted(true_mosaic, key=lambda x: calculate_mosaic_score(x['analysis']), reverse=True)
    
    print("Top 5 des vrais MOSAIC les plus caractéristiques:")
    for i, r in enumerate(true_mosaic_sorted[:5], 1):
        analysis = r['analysis']
        mosaic_score = calculate_mosaic_score(analysis)
        scenario_data = r.get('scenario_data', {})
        
        print(f"\n{i}. {r['puzzle_id']} (score MOSAIC: {mosaic_score:.1f})")
        print(f"   Type: {analysis['scenario_type']}")
        print(f"   Indicateurs: {', '.join(analysis['mosaic_indicators'])}")
        
        if scenario_data and scenario_data.get('scenario_commands'):
            scenario = scenario_data['scenario_commands']
            if len(scenario) > 80:
                print(f"   Scénario: {scenario[:80]}...")
            else:
                print(f"   Scénario: {scenario}")

def main():
    """Point d'entrée principal"""
    
    # Analyser les scénarios
    results = analyze_mosaic_scenarios()
    
    # Comparer vrais vs faux
    compare_true_vs_false_mosaic(results)
    
    # Identifier les patterns authentiques
    identify_true_mosaic_patterns(results)
    
    # Générer le rapport détaillé
    generate_detailed_report(results)
    
    print(f"\n🎯 CONCLUSION")
    print("L'analyse des scénarios AGI révèle les caractéristiques distinctives")
    print("des vrais puzzles MOSAIC par rapport aux faux positifs détectés.")
    print("Les patterns identifiés peuvent améliorer la détection automatique.")

if __name__ == "__main__":
    main()