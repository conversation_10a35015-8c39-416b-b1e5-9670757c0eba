"""
Script pour lister les fichiers d'analyse existants
"""

from pathlib import Path
import json

def list_analysis_files(data_dir="../arcdata/training"):
    """Liste tous les fichiers d'analyse existants"""
    
    data_path = Path(data_dir)
    
    if not data_path.exists():
        print(f"Répertoire non trouvé: {data_dir}")
        return
    
    # Chercher tous les fichiers *_analysis.json
    analysis_files = list(data_path.glob("*_analysis.json"))
    
    if not analysis_files:
        print("Aucun fichier d'analyse trouvé")
        return
    
    print(f"FICHIERS D'ANALYSE TROUVÉS ({len(analysis_files)})")
    print("=" * 50)
    
    for analysis_file in sorted(analysis_files):
        task_id = analysis_file.stem.replace("_analysis", "")
        
        try:
            # Lire le fichier d'analyse
            with open(analysis_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            summary = data['puzzle_summary']
            agi = data['agi_mapping']
            
            print(f"\n📋 {task_id}")
            print(f"   Type: {summary['dominant_transformation_type']}")
            print(f"   Ratio: {summary['average_size_ratio']:.2f}")
            print(f"   Consistance: {summary['consistency_score']:.2f}")
            print(f"   Commandes AGI: {', '.join(agi['possible_commands'])}")
            print(f"   Confiance: {agi['confidence_level']}")
            
        except Exception as e:
            print(f"\n❌ {task_id} - Erreur: {e}")
    
    print(f"\n✓ {len(analysis_files)} analyses listées")

def main():
    """Fonction principale"""
    
    print("LISTE DES ANALYSES DE TÂCHES")
    print("=" * 30)
    
    list_analysis_files()

if __name__ == "__main__":
    main()